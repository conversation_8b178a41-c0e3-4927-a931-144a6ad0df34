{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_shenqingjishi_1/index.vue?c803", "webpack:///./src/pages/lanhu_shenqingjishi_1/index.vue?af2d", "webpack:///./src/pages/lanhu_shenqingjishi_1/index.vue?193c", "webpack:///./src/pages/lanhu_shenqingjishi_1/index.vue?cfd8", "uni-app:///src/pages/lanhu_shenqingjishi_1/index.vue", "webpack:///./src/pages/lanhu_shenqingjishi_1/index.vue?fd60", "webpack:///./src/pages/lanhu_shenqingjishi_1/index.vue?4818"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loopData0", "lanhuBg0", "lanhuimage0", "lanhutext0", "lanhufontColor0", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAA0D,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFvCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCC2Rtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QACAC,QAAA,EACA;QACAC,WAAA,EACA;QACAC,UAAA;QACAC,eAAA;MACA,GACA;QACAH,QAAA,EACA;QACAC,WAAA,EACA;QACAC,UAAA;QACAC,eAAA;MACA,GACA;QACAH,QAAA,EACA;QACAC,WAAA,EACA;QACAC,UAAA;QACAC,eAAA;MACA,EACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;AC5TA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_shenqingjishi_1/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_shenqingjishi_1/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0ab4e819&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_shenqingjishi_1/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0ab4e819&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"box_1 flex-col justify-between\">\n      <view class=\"box_2 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG01288eef35ac7e55af88547c7bcf2176.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"box_3 flex-row\">\n        <image\n          class=\"thumbnail_4\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n        />\n        <text class=\"text_2\">申请技师</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n        />\n      </view>\n    </view>\n    <view class=\"image-wrapper_1 flex-col\">\n      <image\n        class=\"image_2\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG1299af285f3e3c951809c3a1b14fd773.png\"\n      />\n    </view>\n    <view class=\"box_4 flex-col\">\n      <view class=\"section_1 flex-col\">\n        <text class=\"text_3\">寻找闪耀的你</text>\n        <text class=\"text_4\">技师招募</text>\n        <text class=\"text_5\">\n          时间自由&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;高额提成\n        </text>\n        <view class=\"text-wrapper_1\">\n          <text class=\"text_6\">*</text>\n          <text class=\"text_7\">您的姓名</text>\n        </view>\n        <view class=\"text-wrapper_2 flex-col\">\n          <text class=\"text_8\">请输入您的姓名</text>\n        </view>\n        <view class=\"text-wrapper_3\">\n          <text class=\"text_9\">*</text>\n          <text class=\"text_10\">您的性别</text>\n        </view>\n        <view class=\"group_1 flex-row justify-between\">\n          <view class=\"image-text_1 flex-row justify-between\">\n            <image\n              class=\"thumbnail_5\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG5c461f6aca1714cd6ec3cc06af431c1b.png\"\n            />\n            <text class=\"text-group_1\">男</text>\n          </view>\n          <view class=\"image-text_2 flex-row justify-between\">\n            <image\n              class=\"thumbnail_6\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG5c461f6aca1714cd6ec3cc06af431c1b.png\"\n            />\n            <text class=\"text-group_2\">女</text>\n          </view>\n        </view>\n        <view class=\"text-wrapper_4\">\n          <text class=\"text_11\">*</text>\n          <text class=\"text_12\">你的生日</text>\n        </view>\n        <view class=\"group_2 flex-row\">\n          <text class=\"text_13\">请选择</text>\n          <image\n            class=\"thumbnail_7\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG0679e8b660dc832a2cfe07defac448e7.png\"\n          />\n        </view>\n        <view class=\"text-wrapper_5\">\n          <text class=\"text_14\">*</text>\n          <text class=\"text_15\">手机号</text>\n        </view>\n        <view class=\"text-wrapper_6 flex-col\">\n          <text class=\"text_16\">请输入手机号</text>\n        </view>\n        <view class=\"text-wrapper_7\">\n          <text class=\"text_17\">*</text>\n          <text class=\"text_18\">从业年份</text>\n        </view>\n        <view class=\"text-wrapper_8 flex-col\">\n          <text class=\"text_19\">请输入从业年份&nbsp;例如5年</text>\n        </view>\n        <view class=\"text-wrapper_9\">\n          <text class=\"text_20\">*</text>\n          <text class=\"text_21\">意向工作城市</text>\n        </view>\n        <view class=\"group_3 flex-row\">\n          <text class=\"text_22\">请选择</text>\n          <image\n            class=\"thumbnail_8\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG547abb33e6e334fd7e3acf00242167af.png\"\n          />\n        </view>\n        <view class=\"text-wrapper_10\">\n          <text class=\"text_23\">*</text>\n          <text class=\"text_24\">免出行费半径距离</text>\n        </view>\n        <view class=\"text-wrapper_11 flex-col\">\n          <text class=\"text_25\">请选择</text>\n        </view>\n        <view class=\"text-wrapper_12\">\n          <text class=\"text_26\">*</text>\n          <text class=\"text_27\">所在地址</text>\n        </view>\n        <view class=\"group_4 flex-row\">\n          <text class=\"text_28\">请点击图标设置</text>\n          <image\n            class=\"thumbnail_9\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNGb07afa368fe989d36416f7708b6c0c01.png\"\n          />\n        </view>\n        <view class=\"text-wrapper_13\">\n          <text class=\"text_29\">*</text>\n          <text class=\"text_30\">所在地址</text>\n        </view>\n        <view class=\"text-wrapper_14 flex-col\">\n          <text class=\"text_31\">请输入技师简介</text>\n        </view>\n        <view class=\"text-wrapper_15\">\n          <text class=\"text_32\">*</text>\n          <text class=\"text_33\">身份证号</text>\n        </view>\n        <view class=\"text-wrapper_16 flex-col\">\n          <text class=\"text_34\">请输入您的身份证号码</text>\n        </view>\n        <view class=\"group_5 flex-row\">\n          <view class=\"text-wrapper_17\">\n            <text class=\"text_35\">*</text>\n            <text class=\"text_36\">身份证照片</text>\n          </view>\n          <text class=\"text_37\">图片大小不超过10M</text>\n        </view>\n        <view class=\"group_6 flex-col\"></view>\n        <image\n          class=\"image_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG2fb148c30a44ac5fdd383a5d507fb6f3.png\"\n        />\n        <view class=\"group_7 flex-col\"></view>\n        <view class=\"group_8 flex-col\"></view>\n        <view class=\"text-wrapper_18 flex-col\">\n          <text class=\"text_38\">赶快加入</text>\n        </view>\n      </view>\n      <view class=\"section_2 flex-col\">\n        <view class=\"box_5 flex-row justify-between\">\n          <view class=\"group_9 flex-col\">\n            <view class=\"image-wrapper_2 flex-col\">\n              <image\n                class=\"thumbnail_10\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNGf65f215eaf3eef82fcdd15f0c547bc01.png\"\n              />\n            </view>\n            <text class=\"text_39\">重新上传</text>\n          </view>\n          <view class=\"group_10 flex-col\">\n            <view class=\"image-wrapper_3 flex-col\">\n              <image\n                class=\"thumbnail_11\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNGc2771a7af5c189588e4f223323e8659c.png\"\n              />\n            </view>\n            <text class=\"text_40\">重新上传</text>\n          </view>\n        </view>\n        <view class=\"box_6 flex-row\">\n          <view class=\"group_11 flex-col\">\n            <view class=\"image-wrapper_4 flex-col\">\n              <image\n                class=\"thumbnail_12\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG942eb16a4e91a4d4d7eca2c83d527d32.png\"\n              />\n            </view>\n            <text class=\"text_41\">重新上传</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"section_3 flex-col\">\n        <view class=\"section_4 flex-row\">\n          <view class=\"text-wrapper_19\">\n            <text class=\"text_42\">*</text>\n            <text class=\"text_43\">资格证书</text>\n          </view>\n          <text class=\"text_44\">图片大小不超过10M</text>\n        </view>\n        <view class=\"section_5 flex-col\">\n          <view class=\"list_1 flex-row\">\n            <view\n              class=\"list-items_1 flex-col\"\n              :style=\"{ background: item.lanhuBg0 }\"\n              v-for=\"(item, index) in loopData0\"\n              :key=\"index\"\n            >\n              <view class=\"image-wrapper_5 flex-col\">\n                <image\n                  class=\"thumbnail_13\"\n                  referrerpolicy=\"no-referrer\"\n                  :src=\"item.lanhuimage0\"\n                />\n              </view>\n              <text\n                class=\"text_45\"\n                :style=\"{ color: item.lanhufontColor0 }\"\n                v-html=\"item.lanhutext0\"\n              ></text>\n            </view>\n          </view>\n        </view>\n        <view class=\"section_6 flex-row\">\n          <view class=\"text-wrapper_20\">\n            <text class=\"text_46\">*</text>\n            <text class=\"text_47\">工作形象照</text>\n          </view>\n          <text class=\"text_48\">图片建议尺寸750*750&nbsp;大小不超过10M</text>\n        </view>\n        <view class=\"section_7 flex-col\">\n          <view class=\"section_8 flex-col\">\n            <view class=\"image-wrapper_6 flex-col\">\n              <image\n                class=\"thumbnail_14\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG3ca2b8eff7bae1adfe394791916c3f8f.png\"\n              />\n            </view>\n            <text class=\"text_49\">重新上传</text>\n          </view>\n        </view>\n        <view class=\"section_9 flex-row\">\n          <view class=\"text-wrapper_21\">\n            <text class=\"text_50\">*</text>\n            <text class=\"text_51\">个人生活照</text>\n          </view>\n          <text class=\"text_52\">图片建议尺寸750*750&nbsp;大小不超过10M</text>\n        </view>\n        <view class=\"section_10 flex-col\">\n          <view class=\"block_1 flex-col\">\n            <view class=\"image-wrapper_7 flex-col\">\n              <image\n                class=\"thumbnail_15\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_shenqingjishi_1/FigmaDDSSlicePNG3ca2b8eff7bae1adfe394791916c3f8f.png\"\n              />\n            </view>\n            <text class=\"text_53\">重新上传</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"section_11 flex-col\">\n        <view class=\"text-wrapper_22 flex-col\">\n          <text class=\"text_54\">提交</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      loopData0: [\n        {\n          lanhuBg0:\n            'url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGef5552a3674028c2446bc6b9f91912b8.png) 100% no-repeat',\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGeb7e05d7a236b6491229ffa4683a2aca.png',\n          lanhutext0: '重新上传',\n          lanhufontColor0: 'rgba(255,255,255,1.000000)'\n        },\n        {\n          lanhuBg0:\n            'url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG65d5ebae05d9b9d0d50e191a6aa1d31e.png) 100% no-repeat',\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG420a1c93e23449c15576e03126099d2d.png',\n          lanhutext0: '重新上传',\n          lanhufontColor0: 'rgba(255,255,255,1.000000)'\n        },\n        {\n          lanhuBg0:\n            'url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG73d954f5f022589d9bd0bb2a9ed1af10.png) 100% no-repeat',\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG3ac2ecd722688e9792f36c1c6be46da9.png',\n          lanhutext0: '重新上传<br/>2/5',\n          lanhufontColor0: 'rgba(153,153,153,1.000000)'\n        }\n      ],\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332583353\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}