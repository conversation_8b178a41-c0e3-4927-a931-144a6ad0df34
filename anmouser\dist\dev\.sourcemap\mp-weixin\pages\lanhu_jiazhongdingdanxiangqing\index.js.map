{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_jiazhongdingdanxiangqing/index.vue?754d", "webpack:///./src/pages/lanhu_jiazhongdingdanxiangqing/index.vue?b8df", "webpack:///./src/pages/lanhu_jiazhongdingdanxiangqing/index.vue?cc3c", "webpack:///./src/pages/lanhu_jiazhongdingdanxiangqing/index.vue?bdce", "uni-app:///src/pages/lanhu_jiazhongdingdanxiangqing/index.vue", "webpack:///./src/pages/lanhu_jiazhongdingdanxiangqing/index.vue?5f43", "webpack:///./src/pages/lanhu_jiazhongdingdanxiangqing/index.vue?ebc3"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loopData0", "lanhuimage0", "lanhutext0", "loopData1", "lanhutext1", "lanhufontColor1", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAmE,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFhDG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCuQtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QACAC,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,EACA;MACAC,SAAA,GACA;QACAD,UAAA;QACAE,UAAA;QACAC,eAAA;MACA,GACA;QACAH,UAAA;QACAE,UAAA;QACAC,eAAA;MACA,EACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;AC3SA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_jiazhongdingdanxiangqing/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_jiazhongdingdanxiangqing/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=77c11f14&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_jiazhongdingdanxiangqing/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=77c11f14&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"group_1 flex-row\">\n      <view class=\"group_2 flex-col\">\n        <view class=\"block_1 flex-row\">\n          <text class=\"text_1\">12:30</text>\n          <image\n            class=\"thumbnail_1\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNG8c1896cde99a71b27008ac478bbfc5da.png\"\n          />\n          <image\n            class=\"thumbnail_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n          />\n          <image\n            class=\"thumbnail_3\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n          />\n        </view>\n        <view class=\"block_2 flex-col\">\n          <view class=\"block_3 flex-row\">\n            <view class=\"image-text_1 flex-row justify-between\">\n              <image\n                class=\"label_1\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNG9bb7f9691971f53c5c493535a42a446e.png\"\n              />\n              <text class=\"text-group_1\">服务中</text>\n            </view>\n          </view>\n          <view class=\"block_4 flex-row\">\n            <text class=\"text_2\">服务还有</text>\n            <view class=\"text-wrapper_1 flex-col\">\n              <text class=\"text_3\">00</text>\n            </view>\n            <text class=\"text_4\">天</text>\n            <view class=\"text-wrapper_2 flex-col\">\n              <text class=\"text_5\">01</text>\n            </view>\n            <text class=\"text_6\">时</text>\n            <view class=\"text-wrapper_3 flex-col\">\n              <text class=\"text_7\">15</text>\n            </view>\n            <text class=\"text_8\">分</text>\n            <view class=\"text-wrapper_4 flex-col\">\n              <text class=\"text_9\">10</text>\n            </view>\n            <text class=\"text_10\">秒</text>\n            <text class=\"text_11\">结束</text>\n          </view>\n          <view class=\"block_5 flex-col\">\n            <view class=\"list_1 flex-row\">\n              <view\n                class=\"image-text_2 flex-col justify-between\"\n                v-for=\"(item, index) in loopData0\"\n                :key=\"index\"\n              >\n                <image\n                  class=\"label_2\"\n                  referrerpolicy=\"no-referrer\"\n                  :src=\"item.lanhuimage0\"\n                />\n                <text class=\"text-group_2\" v-html=\"item.lanhutext0\"></text>\n              </view>\n            </view>\n          </view>\n        </view>\n        <view class=\"block_6 flex-col\">\n          <view class=\"text-wrapper_5 flex-row\">\n            <text class=\"text_12\">服务内容</text>\n          </view>\n          <view class=\"box_1 flex-row justify-between\">\n            <view class=\"image-text_3 flex-row justify-between\">\n              <image\n                class=\"image_1\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNG3b12fecafbcaf151adff3dfde3e478ec.png\"\n              />\n              <view class=\"text-group_3 flex-col\">\n                <text class=\"text_13\">狐狸舒适推拿</text>\n                <text class=\"text_14\">服务时长：100分钟</text>\n                <text class=\"text_15\">物料费：60</text>\n                <view class=\"text-wrapper_6\">\n                  <text class=\"text_16\">￥</text>\n                  <text class=\"text_17\">298.00</text>\n                </view>\n              </view>\n            </view>\n            <text class=\"text_18\">x1</text>\n          </view>\n        </view>\n        <view class=\"block_7 flex-row\">\n          <view class=\"image-text_4 flex-row justify-between\">\n            <image\n              class=\"thumbnail_4\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNGed89718b81e3bef588dc7c495902ca3b.png\"\n            />\n            <text class=\"text-group_4\">李三思</text>\n          </view>\n          <image\n            class=\"label_3\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNG58f672630dfd018bc23354b841cfc29d.png\"\n          />\n        </view>\n        <view class=\"text-wrapper_7 flex-row justify-between\">\n          <text class=\"text_19\">代理商</text>\n          <text class=\"text_20\">李成武</text>\n        </view>\n        <view class=\"block_8 flex-col\">\n          <view class=\"text-wrapper_8\">\n            <text class=\"text_21\">下单人&nbsp;&nbsp;&nbsp;</text>\n            <text class=\"text_22\">\n              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n            </text>\n            <text class=\"text_23\">冉</text>\n            <text class=\"text_24\">（第16次下单）</text>\n            <text class=\"text_25\"></text>\n          </view>\n        </view>\n        <view class=\"block_9 flex-row\">\n          <view class=\"image-text_5 flex-row justify-between\">\n            <view class=\"text-group_5\">\n              <text class=\"text_26\">联系方式&nbsp;&nbsp;&nbsp;</text>\n              <text class=\"text_27\">\n                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;187237558401\n              </text>\n            </view>\n            <image\n              class=\"label_4\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNG58f672630dfd018bc23354b841cfc29d.png\"\n            />\n          </view>\n        </view>\n        <view class=\"text-wrapper_9 flex-col\">\n          <text class=\"text_28\">服务地址</text>\n        </view>\n        <view class=\"block_10 flex-row\">\n          <view class=\"image-text_6 flex-row\">\n            <text class=\"text-group_6\">\n              重庆市渝中区民族路166号(临江门地铁站1号口步行\n              <br />\n              地王广场(临江支路)\n            </text>\n            <view class=\"text-wrapper_10 flex-col\">\n              <text class=\"text_29\">复制</text>\n            </view>\n            <image\n              class=\"label_5\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_jiazhongdingdanxiangqing/b05b9632184f432395fbc47b056d7f4d_mergeImage.png\"\n            />\n          </view>\n        </view>\n        <view class=\"text-wrapper_11 flex-row\">\n          <text class=\"text_30\">下单时间</text>\n          <text class=\"text_31\">2024-12-21&nbsp;&nbsp;12:12:45</text>\n        </view>\n        <view class=\"text-wrapper_12 flex-row\">\n          <text class=\"text_32\">服务时间</text>\n          <text class=\"text_33\">2024-12-21&nbsp;&nbsp;12:12-12:11</text>\n        </view>\n        <view class=\"text-wrapper_13 flex-row justify-between\">\n          <text class=\"text_34\">服务时长</text>\n          <text class=\"text_35\">80分钟</text>\n        </view>\n        <view class=\"text-wrapper_14 flex-row justify-between\">\n          <text class=\"text_36\">车费详情</text>\n          <text class=\"text_37\">出租车&nbsp;全程101.62m</text>\n        </view>\n        <view class=\"block_11 flex-row justify-between\">\n          <text class=\"text_38\">出行费用</text>\n          <view class=\"text-wrapper_15\">\n            <text class=\"text_39\">￥0</text>\n            <text class=\"text_40\"></text>\n            <text class=\"text_41\">&nbsp;技师补贴</text>\n          </view>\n        </view>\n        <view class=\"text-wrapper_16 flex-row justify-between\">\n          <text class=\"text_42\">项目服务费用</text>\n          <text class=\"text_43\">￥298</text>\n        </view>\n        <view class=\"list_2 flex-col\">\n          <view\n            class=\"text-wrapper_17 flex-row justify-between\"\n            v-for=\"(item, index) in loopData1\"\n            :key=\"index\"\n          >\n            <text class=\"text_44\" v-html=\"item.lanhutext0\"></text>\n            <text\n              class=\"text_45\"\n              :style=\"{ color: item.lanhufontColor1 }\"\n              v-html=\"item.lanhutext1\"\n            ></text>\n          </view>\n        </view>\n        <view class=\"block_12 flex-row\">\n          <image\n            class=\"thumbnail_5\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n          />\n          <text class=\"text_46\">订单详情</text>\n          <image\n            class=\"image_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n          />\n        </view>\n        <view class=\"block_13 flex-row justify-between\">\n          <text class=\"text_47\">总价</text>\n          <view class=\"text-wrapper_18\">\n            <text class=\"text_48\">总计</text>\n            <text class=\"text_49\">:</text>\n            <text class=\"text_50\">￥298</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"group_3 flex-col\">\n        <view class=\"box_2 flex-col\">\n          <view class=\"box_3 flex-row justify-between\">\n            <text class=\"text_51\">订单号：2022441545646545645...</text>\n            <view class=\"text-wrapper_19 flex-col\">\n              <text class=\"text_52\">复制</text>\n            </view>\n          </view>\n          <view class=\"box_4 flex-row\">\n            <view class=\"group_4 flex-col\"></view>\n            <text class=\"text_53\">技师接单</text>\n            <text class=\"text_54\">2024-12-12&nbsp;&nbsp;12::12:32</text>\n          </view>\n        </view>\n        <view class=\"box_5 flex-row\">\n          <view class=\"box_6 flex-col\"></view>\n          <text class=\"text_55\">开始服务</text>\n          <text class=\"text_56\">2024-12-12&nbsp;&nbsp;12::12:32</text>\n        </view>\n        <view class=\"box_7 flex-row\">\n          <view class=\"block_14 flex-col\"></view>\n          <text class=\"text_57\">服务完成</text>\n          <text class=\"text_58\">状态未开始</text>\n        </view>\n        <view class=\"image-text_7 flex-row justify-between\">\n          <view class=\"group_5 flex-col\"></view>\n          <text class=\"text-group_7\">\n            重庆市渝北区天罡路达萨罗大概放假了看...\n          </text>\n        </view>\n        <image\n          class=\"image_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_jiazhongdingdanxiangqing/FigmaDDSSlicePNG5d3d8ec6746789160ecbd997b6b213cc.png\"\n        />\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      loopData0: [\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG13e518501012769544ebe13193cea0bc.png',\n          lanhutext0: '技师接单'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/76d3798de1184b369afc30f8769cb9f5_mergeImage.png',\n          lanhutext0: '开始服务'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/70ac9d3f3f4d4adf9dce0a9babae8bb9_mergeImage.png',\n          lanhutext0: '服务完成'\n        }\n      ],\n      loopData1: [\n        {\n          lanhutext0: '客户打赏',\n          lanhutext1: '￥298',\n          lanhufontColor1: 'rgba(11,206,148,1.000000)'\n        },\n        {\n          lanhutext0: '支付方式',\n          lanhutext1: '微信支付',\n          lanhufontColor1: 'rgba(102,102,102,1.000000)'\n        }\n      ],\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332585409\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}