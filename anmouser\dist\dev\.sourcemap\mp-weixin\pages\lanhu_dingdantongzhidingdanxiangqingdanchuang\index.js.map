{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_dingdantongzhidingdanxiangqingdanchuang/index.vue?7468", "webpack:///./src/pages/lanhu_dingdantongzhidingdanxiangqingdanchuang/index.vue?4555", "webpack:///./src/pages/lanhu_dingdantongzhidingdanxiangqingdanchuang/index.vue?1c9b", "webpack:///./src/pages/lanhu_dingdantongzhidingdanxiangqingdanchuang/index.vue?943e", "uni-app:///src/pages/lanhu_dingdant<PERSON>zhidingdanxiangqingdanchuang/index.vue", "webpack:///./src/pages/lanhu_dingdantongzhidingdanxiangqingdanchuang/index.vue?f942", "webpack:///./src/pages/lanhu_dingdantongzhidingdanxiangqingdanchuang/index.vue?a2b2"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAkF,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAF/DG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCiTtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;ACxTA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_dingdant<PERSON>zhidingdanxiangqingdanchuang/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_dingdantongzhidingdanxiangqingdanchuang/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3588f461&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_dingdantongzhidingdanxiangqingdanchuang/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=3588f461&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"group_1 flex-row\">\n      <text class=\"text_1\">12:30</text>\n      <image\n        class=\"thumbnail_1\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG8c1896cde99a71b27008ac478bbfc5da.png\"\n      />\n      <image\n        class=\"thumbnail_2\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n      />\n      <image\n        class=\"thumbnail_3\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n      />\n    </view>\n    <view class=\"group_2 flex-row\">\n      <image\n        class=\"thumbnail_4\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n      />\n      <text class=\"text_2\">订单详情</text>\n      <image\n        class=\"image_1\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n      />\n    </view>\n    <view class=\"group_3 flex-row\">\n      <view class=\"image-text_1 flex-row justify-between\">\n        <image\n          class=\"label_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG9bb7f9691971f53c5c493535a42a446e.png\"\n        />\n        <text class=\"text-group_1\">待接单</text>\n      </view>\n      <view class=\"section_1 flex-row\">\n        <view class=\"image-text_2 flex-col justify-between\">\n          <image\n            class=\"label_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG13e518501012769544ebe13193cea0bc.png\"\n          />\n          <text class=\"text-group_2\">技师接单</text>\n        </view>\n        <view class=\"image-text_3 flex-col justify-between\">\n          <image\n            class=\"label_3\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNGe049eb1ccde9b04a298904619cfa49db.png\"\n          />\n          <text class=\"text-group_3\">技师出发</text>\n        </view>\n        <view class=\"image-text_4 flex-col justify-between\">\n          <image\n            class=\"label_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG1bf547fa429fedb029ef47a3febdd9ec.png\"\n          />\n          <text class=\"text-group_4\">技师到达</text>\n        </view>\n        <view class=\"image-text_5 flex-col justify-between\">\n          <image\n            class=\"label_5\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/8efb460cd3c44a72ad2922110bc3b376_mergeImage.png\"\n          />\n          <text class=\"text-group_5\">开始服务</text>\n        </view>\n        <view class=\"image-text_6 flex-col justify-between\">\n          <image\n            class=\"label_6\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/ece8952f6e2b4af4aeec71fb78111c81_mergeImage.png\"\n          />\n          <text class=\"text-group_6\">服务完成</text>\n        </view>\n      </view>\n    </view>\n    <view class=\"group_4 flex-col\">\n      <view class=\"text-wrapper_1 flex-row\">\n        <text class=\"text_3\">服务内容</text>\n      </view>\n      <view class=\"section_2 flex-row justify-between\">\n        <image\n          class=\"image_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG3b12fecafbcaf151adff3dfde3e478ec.png\"\n        />\n        <text class=\"text_4\">x1</text>\n      </view>\n    </view>\n    <view class=\"group_5 flex-row\">\n      <view class=\"image-text_7 flex-row justify-between\">\n        <image\n          class=\"thumbnail_5\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNGed89718b81e3bef588dc7c495902ca3b.png\"\n        />\n        <text class=\"text-group_7\">李三思</text>\n      </view>\n      <image\n        class=\"label_7\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG58f672630dfd018bc23354b841cfc29d.png\"\n      />\n    </view>\n    <view class=\"text-wrapper_2 flex-row justify-between\">\n      <text class=\"text_5\">代理人</text>\n      <text class=\"text_6\">李成武</text>\n    </view>\n    <view class=\"group_6 flex-col\">\n      <view class=\"text-wrapper_3\">\n        <text class=\"text_7\">下单人&nbsp;&nbsp;&nbsp;</text>\n        <text class=\"text_8\">\n          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n        </text>\n        <text class=\"text_9\">冉</text>\n        <text class=\"text_10\">（第16次下单）</text>\n        <text class=\"text_11\"></text>\n      </view>\n    </view>\n    <view class=\"group_7 flex-row\">\n      <view class=\"image-text_8 flex-row justify-between\">\n        <view class=\"text-group_8\">\n          <text class=\"text_12\">联系方式&nbsp;&nbsp;&nbsp;</text>\n          <text class=\"text_13\">\n            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;187237558401\n          </text>\n        </view>\n        <image\n          class=\"label_8\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG58f672630dfd018bc23354b841cfc29d.png\"\n        />\n      </view>\n    </view>\n    <view class=\"text-wrapper_4 flex-col\">\n      <text class=\"text_14\">服务地址</text>\n    </view>\n    <view class=\"group_8 flex-row\">\n      <view class=\"image-text_9 flex-row\">\n        <text class=\"text-group_9\">\n          重庆市渝中区民族路166号(临江门地铁站1号口步行\n          <br />\n          地王广场(临江支路)\n        </text>\n        <view class=\"text-wrapper_5 flex-col\">\n          <text class=\"text_15\">复制</text>\n        </view>\n        <image\n          class=\"label_9\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/9fed50d9d1c64e609801b0cba6c7af71_mergeImage.png\"\n        />\n      </view>\n    </view>\n    <view class=\"text-wrapper_6 flex-row\">\n      <text class=\"text_16\">下单时间</text>\n      <text class=\"text_17\">2024-12-21&nbsp;&nbsp;12:12:45</text>\n    </view>\n    <view class=\"text-wrapper_7 flex-row\">\n      <text class=\"text_18\">服务时间</text>\n      <text class=\"text_19\">2024-12-21&nbsp;&nbsp;12:12-12:11</text>\n    </view>\n    <view class=\"text-wrapper_8 flex-row justify-between\">\n      <text class=\"text_20\">服务时长</text>\n      <text class=\"text_21\">80分钟</text>\n    </view>\n    <view class=\"text-wrapper_9 flex-row justify-between\">\n      <text class=\"text_22\">车费详情</text>\n      <text class=\"text_23\">出租车&nbsp;全程101.62m</text>\n    </view>\n    <view class=\"group_9 flex-row justify-between\">\n      <text class=\"text_24\">出行费用</text>\n      <view class=\"text-wrapper_10\">\n        <text class=\"text_25\">￥0</text>\n        <text class=\"text_26\"></text>\n        <text class=\"text_27\">&nbsp;技师补贴</text>\n      </view>\n    </view>\n    <view class=\"text-wrapper_11 flex-row justify-between\">\n      <text class=\"text_28\">项目服务费用</text>\n      <text class=\"text_29\">￥298</text>\n    </view>\n    <view class=\"text-wrapper_12 flex-row justify-between\">\n      <text class=\"text_30\">客户打赏</text>\n      <text class=\"text_31\">￥298</text>\n    </view>\n    <view class=\"text-wrapper_13 flex-row justify-between\">\n      <text class=\"text_32\">物料费</text>\n      <text class=\"text_33\">￥298</text>\n    </view>\n    <view class=\"text-wrapper_14 flex-row justify-between\">\n      <text class=\"text_34\">支付方式</text>\n      <text class=\"text_35\">微信支付</text>\n    </view>\n    <view class=\"group_10 flex-row justify-between\">\n      <text class=\"text_36\">支付方式</text>\n      <view class=\"text-wrapper_15\">\n        <text class=\"text_37\">总计</text>\n        <text class=\"text_38\">:</text>\n        <text class=\"text_39\">￥298</text>\n      </view>\n    </view>\n    <view class=\"group_11 flex-col\">\n      <view class=\"block_1 flex-col\">\n        <view class=\"box_1 flex-row justify-between\">\n          <text class=\"text_40\">订单号：2022441545646545645...</text>\n          <view class=\"text-wrapper_16 flex-col\">\n            <text class=\"text_41\">复制</text>\n          </view>\n        </view>\n        <view class=\"box_2 flex-row\">\n          <view class=\"group_12 flex-col\"></view>\n          <text class=\"text_42\">技师接单</text>\n          <text class=\"text_43\">2024-12-12&nbsp;&nbsp;12::12:32</text>\n        </view>\n      </view>\n      <view class=\"block_2 flex-row\">\n        <view class=\"group_13 flex-col\"></view>\n        <text class=\"text_44\">技师出发</text>\n        <text class=\"text_45\">2024-12-12&nbsp;&nbsp;12::12:32</text>\n      </view>\n      <view class=\"block_3 flex-row justify-between\">\n        <view class=\"box_3 flex-col\"></view>\n        <text class=\"text_46\">重庆市渝北区天罡路达萨罗大概放假了看...</text>\n      </view>\n      <view class=\"block_4 flex-row\">\n        <view class=\"box_4 flex-col\"></view>\n        <text class=\"text_47\">技师到达</text>\n        <text class=\"text_48\">2024-12-12&nbsp;&nbsp;12::12:32</text>\n      </view>\n      <view class=\"image-text_10 flex-row justify-between\">\n        <view class=\"box_5 flex-col\"></view>\n        <text class=\"text-group_10\">\n          重庆市渝北区天罡路达萨罗大概放假了看...\n        </text>\n      </view>\n      <image\n        class=\"image_3\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG5d3d8ec6746789160ecbd997b6b213cc.png\"\n      />\n      <view class=\"block_5 flex-row\">\n        <view class=\"block_6 flex-col\"></view>\n        <text class=\"text_49\">开始服务</text>\n        <text class=\"text_50\">2024-12-12&nbsp;&nbsp;12::12:32</text>\n      </view>\n      <view class=\"block_7 flex-row\">\n        <view class=\"group_14 flex-col\"></view>\n        <text class=\"text_51\">服务完成</text>\n        <text class=\"text_52\">状态未开始</text>\n      </view>\n      <view class=\"image-text_11 flex-row justify-between\">\n        <view class=\"box_6 flex-col\"></view>\n        <text class=\"text-group_11\">\n          重庆市渝北区天罡路达萨罗大概放假了看...\n        </text>\n      </view>\n      <image\n        class=\"image_4\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_dingdantongzhidingdanxiangqingdanchuang/FigmaDDSSlicePNG5d3d8ec6746789160ecbd997b6b213cc.png\"\n      />\n      <view class=\"block_8 flex-row\">\n        <view class=\"group_15 flex-col\"></view>\n        <text class=\"text_53\">签字确认</text>\n        <text class=\"text_54\">暂未签字确认</text>\n      </view>\n      <view class=\"text-wrapper_17 flex-col\">\n        <text class=\"text_55\">确认签字</text>\n      </view>\n    </view>\n    <view class=\"group_16 flex-row\">\n      <view class=\"text-wrapper_18 flex-col\">\n        <text class=\"text_56\">转单</text>\n      </view>\n      <view class=\"text-wrapper_19 flex-col\">\n        <text class=\"text_57\">技师接单</text>\n      </view>\n    </view>\n    <view class=\"group_17 flex-col\">\n      <view class=\"text-wrapper_20 flex-col\">\n        <text class=\"text_58\">温馨提示</text>\n        <text class=\"text_59\">你确认要操作技师接单吗</text>\n      </view>\n      <view class=\"block_9 flex-col justify-between\">\n        <view class=\"section_3 flex-col\"></view>\n        <view class=\"section_4 flex-row\">\n          <text class=\"text_60\">取消</text>\n          <view class=\"box_7 flex-col\"></view>\n          <text class=\"text_61\">确定</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332582792\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}