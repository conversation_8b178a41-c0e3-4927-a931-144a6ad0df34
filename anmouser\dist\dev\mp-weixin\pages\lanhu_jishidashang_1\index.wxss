@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(247, 247, 247);
  position: relative;
  width: 750rpx;
  height: 1636rpx;
  overflow: hidden;
}
.page .group_1 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 172rpx;
}
.page .group_1 .group_2 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 64rpx;
}
.page .group_1 .group_2 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .group_1 .group_2 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .group_1 .group_2 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .group_1 .group_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .group_1 .group_3 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 108rpx;
}
.page .group_1 .group_3 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .group_1 .group_3 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .group_1 .group_3 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .group_4 {
  background-image: -webkit-linear-gradient(269deg, rgb(62, 200, 174) 55.500001%, rgba(247, 247, 247, 0) 100%);
  background-image: linear-gradient(181deg, rgb(62, 200, 174) 55.500001%, rgba(247, 247, 247, 0) 100%);
  height: 448rpx;
  margin-bottom: 1016rpx;
  width: 750rpx;
}
.page .group_4 .box_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 250rpx;
  width: 690rpx;
  margin: 48rpx 0 0 30rpx;
}
.page .group_4 .box_1 .section_1 {
  width: 642rpx;
  height: 90rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .group_4 .box_1 .section_1 .label_1 {
  width: 90rpx;
  height: 90rpx;
}
.page .group_4 .box_1 .section_1 .text_3 {
  width: 102rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 34rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 22rpx 0 0 10rpx;
}
.page .group_4 .box_1 .section_1 .image-text_1 {
  width: 46rpx;
  height: 28rpx;
  margin: 30rpx 0 0 48rpx;
}
.page .group_4 .box_1 .section_1 .image-text_1 .group_5 {
  width: 22rpx;
  height: 22rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEmSURBVHgBXVAxTsNAEJy93J0diQJ+YHiBoUI0iUSNaHgAfACFH1CFElIgRMkHAIkHIDrK/ADzAaBAcQDnlr21HUgs7a33ZnZnboGl7/MsPyyHmzwZ5oNljP4X5fl2hhAeAc4k3mHsVvfkuWhxM594uZOTM3dIXIbEgxK/GuvySga0kyfX/QEZ7BNCj5moleMaj4nkGFtDF1Te7L7J1VqkCJWZG2vawQtODbwbwXtIBntLYgNIrNQSYkf/Yzh7qm3l7Z48Cj1RoHq6JOJGRRSZX9KDhw0bycH6kYB9dVwbjgbm+hz4KGYlG+c+0FH4z3Pkx245bbM0JWMlBWY/UBNxAboKUn1dRr2kmjytMO6mrhCokOv7ir+fMDO5dZ3jwLRefU1fI+8XlnVmBHapEbUAAAAASUVORK5CYII=) 100% no-repeat;
  background-size: 100% 100%;
  margin-top: 4rpx;
}
.page .group_4 .box_1 .section_1 .image-text_1 .text-group_1 {
  width: 16rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(247, 160, 68);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .box_1 .section_1 .text-wrapper_1 {
  background-color: rgb(11, 206, 148);
  border-radius: 5px;
  height: 54rpx;
  width: 138rpx;
  margin: 12rpx 0 0 208rpx;
}
.page .group_4 .box_1 .section_1 .text-wrapper_1 .text_4 {
  width: 90rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 0 0 26rpx;
}
.page .group_4 .box_1 .section_2 {
  width: 548rpx;
  height: 62rpx;
  margin: 34rpx 0 28rpx 72rpx;
}
.page .group_4 .box_1 .section_2 .text-group_2 {
  width: 90rpx;
  height: 62rpx;
}
.page .group_4 .box_1 .section_2 .text-group_2 .text_5 {
  width: 88rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 2rpx;
}
.page .group_4 .box_1 .section_2 .text-group_2 .text_6 {
  width: 88rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 22rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 12rpx;
}
.page .group_4 .box_1 .section_2 .text_7 {
  width: 32rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 168rpx;
}
.page .group_4 .box_1 .section_2 .text-group_3 {
  width: 88rpx;
  height: 62rpx;
  margin-left: 170rpx;
}
.page .group_4 .box_1 .section_2 .text-group_3 .text_8 {
  width: 48rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 20rpx;
}
.page .group_4 .box_1 .section_2 .text-group_3 .text_9 {
  width: 88rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 22rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 12rpx;
}
.page .group_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  position: absolute;
  left: 30rpx;
  top: 490rpx;
  width: 690rpx;
  height: 614rpx;
}
.page .group_6 .text_10 {
  width: 448rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 38rpx 0 0 24rpx;
}
.page .group_6 .block_1 {
  width: 642rpx;
  height: 228rpx;
  margin: 50rpx 0 0 24rpx;
}
.page .group_6 .block_1 .group_7 {
  background-color: rgb(248, 248, 248);
  border-radius: 5px;
  width: 150rpx;
  height: 228rpx;
}
.page .group_6 .block_1 .group_8 {
  background-color: rgb(248, 248, 248);
  border-radius: 5px;
  width: 150rpx;
  height: 228rpx;
}
.page .group_6 .block_2 {
  background-color: rgb(11, 206, 148);
  border-radius: 30px;
  width: 630rpx;
  height: 102rpx;
  margin: 64rpx 0 112rpx 30rpx;
}
.page .group_9 {
  background-color: rgba(0, 0, 0, 0.5);
  height: 1588rpx;
  width: 750rpx;
  position: absolute;
  left: 0;
  top: 0;
}
.page .group_9 .box_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 20px;
  position: relative;
  width: 626rpx;
  height: 624rpx;
  margin: 480rpx 0 0 62rpx;
}
.page .group_9 .box_2 .text_11 {
  width: 300rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 50rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 164rpx 0 0 164rpx;
}
.page .group_9 .box_2 .text_12 {
  width: 392rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 36rpx 0 0 118rpx;
}
.page .group_9 .box_2 .text-wrapper_2 {
  width: 200rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 86rpx 0 0 202rpx;
}
.page .group_9 .box_2 .text-wrapper_2 .text_13 {
  width: 200rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_9 .box_2 .text-wrapper_2 .text_14 {
  width: 200rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 80rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_9 .box_2 .text-wrapper_3 {
  background-color: rgb(11, 206, 148);
  border-radius: 50px;
  height: 90rpx;
  width: 386rpx;
  margin: 86rpx 0 62rpx 120rpx;
}
.page .group_9 .box_2 .text-wrapper_3 .text_15 {
  width: 160rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 40rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 30rpx 0 0 114rpx;
}
.page .group_9 .box_2 .box_3 {
  box-shadow: 0px 6px 10px 0px rgba(62, 200, 174, 0.3);
  background-image: -webkit-linear-gradient(307deg, rgb(156, 255, 236) 0, rgb(62, 200, 174) 100%);
  background-image: linear-gradient(143deg, rgb(156, 255, 236) 0, rgb(62, 200, 174) 100%);
  border-radius: 50%;
  height: 180rpx;
  width: 180rpx;
  position: absolute;
  left: 236rpx;
  top: -76rpx;
}
.page .group_9 .box_2 .box_3 .section_3 {
  box-shadow: 0px 4px 4px 0px rgb(62, 200, 174);
  background-color: rgb(255, 255, 255);
  width: 104rpx;
  height: 80rpx;
  margin: 50rpx 0 0 38rpx;
}
.page .group_9 .image-wrapper_1 {
  position: absolute;
  left: 0;
  top: 0;
  width: 750rpx;
  height: 482rpx;
}
.page .group_9 .image-wrapper_1 .image_2 {
  width: 284rpx;
  height: 204rpx;
  margin: 376rpx 0 0 234rpx;
}
