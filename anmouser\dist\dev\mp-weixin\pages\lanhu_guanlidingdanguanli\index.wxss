@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 3368rpx;
  overflow: hidden;
}
.page .box_1 {
  width: 750rpx;
  height: 388rpx;
}
.page .box_1 .group_1 {
  width: 750rpx;
  height: 64rpx;
  margin-top: 4rpx;
}
.page .box_1 .group_1 .group_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .group_1 .group_2 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .group_1 .group_2 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .group_1 .group_2 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .group_1 .group_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .group_3 {
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .group_3 .section_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .group_3 .section_1 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .box_1 .group_3 .section_1 .text_2 {
  width: 64rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .box_1 .group_3 .section_1 .text_3 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 8rpx 0 0 262rpx;
}
.page .box_1 .group_3 .section_1 .box_2 {
  position: relative;
  width: 226rpx;
  height: 86rpx;
  margin: 0 12rpx 0 26rpx;
}
.page .box_1 .group_3 .section_1 .box_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 0 0 52rpx;
}
.page .box_1 .group_3 .section_1 .box_2 .text_4 {
  position: absolute;
  left: 0;
  top: 0;
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_1 .group_4 {
  width: 750rpx;
  height: 102rpx;
}
.page .box_1 .group_4 .box_3 {
  background-color: rgb(255, 255, 255);
  height: 102rpx;
  width: 750rpx;
}
.page .box_1 .group_4 .box_3 .box_4 {
  background-color: rgb(246, 246, 246);
  border-radius: 50px;
  width: 702rpx;
  height: 78rpx;
  margin: 12rpx 0 0 24rpx;
}
.page .box_1 .group_4 .box_3 .box_4 .image-text_1 {
  width: 316rpx;
  height: 38rpx;
  margin: 20rpx 0 0 38rpx;
}
.page .box_1 .group_4 .box_3 .box_4 .image-text_1 .group_5 {
  background-color: rgb(213, 213, 213);
  width: 38rpx;
  height: 38rpx;
}
.page .box_1 .group_4 .box_3 .box_4 .image-text_1 .text-group_1 {
  width: 260rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .box_1 .group_6 {
  width: 808rpx;
  height: 88rpx;
  margin-bottom: 22rpx;
}
.page .box_1 .group_6 .text-wrapper_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 88rpx;
}
.page .box_1 .group_6 .text-wrapper_1 .text_5 {
  width: 56rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 20rpx;
}
.page .box_1 .group_6 .text-wrapper_1 .text_6 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 38rpx;
}
.page .box_1 .group_6 .text-wrapper_1 .text_7 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 38rpx;
}
.page .box_1 .group_6 .text-wrapper_1 .text_8 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 38rpx;
}
.page .box_1 .group_6 .text-wrapper_1 .text_9 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 38rpx;
}
.page .box_1 .group_6 .text-wrapper_1 .text_10 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 64rpx 0 38rpx;
}
.page .box_1 .group_6 .text_11 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 -26rpx;
}
.page .box_5 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  margin: -2rpx 0 0 24rpx;
}
.page .box_5 .text-wrapper_2 {
  width: 656rpx;
  height: 26rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_5 .text-wrapper_2 .text_12 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_5 .text-wrapper_2 .text_13 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_5 .group_7 {
  width: 654rpx;
  height: 126rpx;
  margin: 72rpx 0 0 22rpx;
}
.page .box_5 .group_7 .image-text_2 {
  width: 436rpx;
  height: 126rpx;
}
.page .box_5 .group_7 .image-text_2 .image_2 {
  width: 126rpx;
  height: 126rpx;
}
.page .box_5 .group_7 .image-text_2 .text-group_2 {
  width: 296rpx;
  height: 116rpx;
}
.page .box_5 .group_7 .image-text_2 .text-group_2 .text_14 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 32rpx;
}
.page .box_5 .group_7 .image-text_2 .text-group_2 .text_15 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .box_5 .group_7 .image-text_2 .text-group_2 .text_16 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .box_5 .group_7 .image-text_2 .text-group_2 .text_17 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .box_5 .group_7 .text_18 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_5 .group_8 {
  width: 660rpx;
  height: 50rpx;
  margin: 58rpx 0 30rpx 22rpx;
}
.page .box_5 .group_8 .text-wrapper_3 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 10rpx;
}
.page .box_5 .group_8 .text-wrapper_3 .text_19 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_5 .group_8 .text-wrapper_3 .text_20 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_5 .group_8 .text-wrapper_4 {
  border-radius: 4px;
  height: 50rpx;
  border: 1px solid rgb(153, 153, 153);
  margin-left: 184rpx;
  width: 136rpx;
}
.page .box_5 .group_8 .text-wrapper_4 .text_21 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 44rpx;
}
.page .box_5 .group_8 .text-wrapper_5 {
  background-color: rgb(11, 206, 148);
  border-radius: 4px;
  height: 50rpx;
  margin-left: 20rpx;
  width: 136rpx;
}
.page .box_5 .group_8 .text-wrapper_5 .text_22 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 20rpx;
}
.page .box_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_6 .text-wrapper_6 {
  width: 656rpx;
  height: 26rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_6 .text-wrapper_6 .text_23 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_6 .text-wrapper_6 .text_24 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_6 .group_9 {
  width: 654rpx;
  height: 126rpx;
  margin: 72rpx 0 0 22rpx;
}
.page .box_6 .group_9 .image-text_3 {
  width: 436rpx;
  height: 126rpx;
}
.page .box_6 .group_9 .image-text_3 .image_3 {
  width: 126rpx;
  height: 126rpx;
}
.page .box_6 .group_9 .image-text_3 .text-group_3 {
  width: 296rpx;
  height: 116rpx;
}
.page .box_6 .group_9 .image-text_3 .text-group_3 .text_25 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 32rpx;
}
.page .box_6 .group_9 .image-text_3 .text-group_3 .text_26 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .box_6 .group_9 .image-text_3 .text-group_3 .text_27 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .box_6 .group_9 .image-text_3 .text-group_3 .text_28 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .box_6 .group_9 .text_29 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_6 .group_10 {
  width: 184rpx;
  height: 28rpx;
  margin: 68rpx 0 42rpx 22rpx;
}
.page .box_6 .group_10 .text-wrapper_7 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_6 .group_10 .text-wrapper_7 .text_30 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_6 .group_10 .text-wrapper_7 .text_31 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_7 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_7 .text-wrapper_8 {
  width: 656rpx;
  height: 26rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_7 .text-wrapper_8 .text_32 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_7 .text-wrapper_8 .text_33 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(62, 200, 174);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_7 .box_8 {
  width: 654rpx;
  height: 126rpx;
  margin: 72rpx 0 0 22rpx;
}
.page .box_7 .box_8 .image-text_4 {
  width: 436rpx;
  height: 126rpx;
}
.page .box_7 .box_8 .image-text_4 .image_4 {
  width: 126rpx;
  height: 126rpx;
}
.page .box_7 .box_8 .image-text_4 .text-group_4 {
  width: 296rpx;
  height: 116rpx;
}
.page .box_7 .box_8 .image-text_4 .text-group_4 .text_34 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 32rpx;
}
.page .box_7 .box_8 .image-text_4 .text-group_4 .text_35 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .box_7 .box_8 .image-text_4 .text-group_4 .text_36 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .box_7 .box_8 .image-text_4 .text-group_4 .text_37 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .box_7 .box_8 .text_38 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_7 .box_9 {
  width: 660rpx;
  height: 50rpx;
  margin: 58rpx 0 30rpx 22rpx;
}
.page .box_7 .box_9 .text-wrapper_9 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 10rpx;
}
.page .box_7 .box_9 .text-wrapper_9 .text_39 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_7 .box_9 .text-wrapper_9 .text_40 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_7 .box_9 .text-wrapper_10 {
  border-radius: 4px;
  height: 50rpx;
  border: 1px solid rgb(153, 153, 153);
  margin-left: 184rpx;
  width: 136rpx;
}
.page .box_7 .box_9 .text-wrapper_10 .text_41 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 44rpx;
}
.page .box_7 .box_9 .text-wrapper_11 {
  background-color: rgb(11, 206, 148);
  border-radius: 4px;
  height: 50rpx;
  margin-left: 20rpx;
  width: 136rpx;
}
.page .box_7 .box_9 .text-wrapper_11 .text_42 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 20rpx;
}
.page .box_10 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_10 .text-wrapper_12 {
  width: 656rpx;
  height: 26rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_10 .text-wrapper_12 .text_43 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_10 .text-wrapper_12 .text_44 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(62, 200, 174);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_10 .box_11 {
  width: 654rpx;
  height: 126rpx;
  margin: 72rpx 0 0 22rpx;
}
.page .box_10 .box_11 .image-text_5 {
  width: 436rpx;
  height: 126rpx;
}
.page .box_10 .box_11 .image-text_5 .image_5 {
  width: 126rpx;
  height: 126rpx;
}
.page .box_10 .box_11 .image-text_5 .text-group_5 {
  width: 296rpx;
  height: 116rpx;
}
.page .box_10 .box_11 .image-text_5 .text-group_5 .text_45 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 32rpx;
}
.page .box_10 .box_11 .image-text_5 .text-group_5 .text_46 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .box_10 .box_11 .image-text_5 .text-group_5 .text_47 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .box_10 .box_11 .image-text_5 .text-group_5 .text_48 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .box_10 .box_11 .text_49 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_10 .box_12 {
  width: 660rpx;
  height: 50rpx;
  margin: 58rpx 0 30rpx 22rpx;
}
.page .box_10 .box_12 .text-wrapper_13 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 10rpx;
}
.page .box_10 .box_12 .text-wrapper_13 .text_50 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_10 .box_12 .text-wrapper_13 .text_51 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_10 .box_12 .text-wrapper_14 {
  border-radius: 4px;
  height: 50rpx;
  border: 1px solid rgb(153, 153, 153);
  margin-left: 184rpx;
  width: 136rpx;
}
.page .box_10 .box_12 .text-wrapper_14 .text_52 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 44rpx;
}
.page .box_10 .box_12 .text-wrapper_15 {
  background-color: rgb(11, 206, 148);
  border-radius: 4px;
  height: 50rpx;
  margin-left: 20rpx;
  width: 136rpx;
}
.page .box_10 .box_12 .text-wrapper_15 .text_53 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 20rpx;
}
.page .box_13 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_13 .text-wrapper_16 {
  width: 656rpx;
  height: 26rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_13 .text-wrapper_16 .text_54 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .text-wrapper_16 .text_55 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(62, 200, 174);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_13 .box_14 {
  width: 436rpx;
  height: 126rpx;
  margin: 72rpx 0 0 22rpx;
}
.page .box_13 .box_14 .image-text_6 {
  width: 436rpx;
  height: 126rpx;
}
.page .box_13 .box_14 .image-text_6 .image_6 {
  width: 126rpx;
  height: 126rpx;
}
.page .box_13 .box_14 .image-text_6 .text-group_6 {
  width: 296rpx;
  height: 116rpx;
}
.page .box_13 .box_14 .image-text_6 .text-group_6 .text_56 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 32rpx;
}
.page .box_13 .box_14 .image-text_6 .text-group_6 .text_57 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .box_13 .box_14 .image-text_6 .text-group_6 .text_58 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .box_13 .box_14 .image-text_6 .text-group_6 .text_59 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .box_13 .box_15 {
  width: 660rpx;
  height: 50rpx;
  margin: 58rpx 0 30rpx 22rpx;
}
.page .box_13 .box_15 .text-wrapper_17 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 10rpx;
}
.page .box_13 .box_15 .text-wrapper_17 .text_60 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .box_15 .text-wrapper_17 .text_61 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .box_15 .text-wrapper_18 {
  border-radius: 4px;
  height: 50rpx;
  border: 1px solid rgb(153, 153, 153);
  margin-left: 184rpx;
  width: 136rpx;
}
.page .box_13 .box_15 .text-wrapper_18 .text_62 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 44rpx;
}
.page .box_13 .box_15 .text-wrapper_19 {
  background-color: rgb(11, 206, 148);
  border-radius: 4px;
  height: 50rpx;
  margin-left: 20rpx;
  width: 136rpx;
}
.page .box_13 .box_15 .text-wrapper_19 .text_63 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 20rpx;
}
.page .box_16 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_16 .text-wrapper_20 {
  width: 656rpx;
  height: 26rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_16 .text-wrapper_20 .text_64 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_16 .text-wrapper_20 .text_65 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(62, 200, 174);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_16 .group_11 {
  width: 654rpx;
  height: 126rpx;
  margin: 72rpx 0 0 22rpx;
}
.page .box_16 .group_11 .image-text_7 {
  width: 436rpx;
  height: 126rpx;
}
.page .box_16 .group_11 .image-text_7 .image_7 {
  width: 126rpx;
  height: 126rpx;
}
.page .box_16 .group_11 .image-text_7 .text-group_7 {
  width: 296rpx;
  height: 116rpx;
}
.page .box_16 .group_11 .image-text_7 .text-group_7 .text_66 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 32rpx;
}
.page .box_16 .group_11 .image-text_7 .text-group_7 .text_67 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .box_16 .group_11 .image-text_7 .text-group_7 .text_68 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .box_16 .group_11 .image-text_7 .text-group_7 .text_69 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .box_16 .group_11 .text_70 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_16 .group_12 {
  width: 660rpx;
  height: 50rpx;
  margin: 58rpx 0 30rpx 22rpx;
}
.page .box_16 .group_12 .text-wrapper_21 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 10rpx;
}
.page .box_16 .group_12 .text-wrapper_21 .text_71 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_16 .group_12 .text-wrapper_21 .text_72 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_16 .group_12 .text-wrapper_22 {
  border-radius: 4px;
  height: 50rpx;
  border: 1px solid rgb(153, 153, 153);
  margin-left: 184rpx;
  width: 136rpx;
}
.page .box_16 .group_12 .text-wrapper_22 .text_73 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 44rpx;
}
.page .box_16 .group_12 .text-wrapper_23 {
  background-color: rgb(11, 206, 148);
  border-radius: 4px;
  height: 50rpx;
  margin-left: 20rpx;
  width: 136rpx;
}
.page .box_16 .group_12 .text-wrapper_23 .text_74 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 20rpx;
}
.page .box_17 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  margin: 20rpx 0 76rpx 24rpx;
}
.page .box_17 .text-wrapper_24 {
  width: 656rpx;
  height: 26rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_17 .text-wrapper_24 .text_75 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_17 .text-wrapper_24 .text_76 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_17 .section_2 {
  width: 654rpx;
  height: 126rpx;
  margin: 72rpx 0 0 22rpx;
}
.page .box_17 .section_2 .image-text_8 {
  width: 436rpx;
  height: 126rpx;
}
.page .box_17 .section_2 .image-text_8 .image_8 {
  width: 126rpx;
  height: 126rpx;
}
.page .box_17 .section_2 .image-text_8 .text-group_8 {
  width: 296rpx;
  height: 116rpx;
}
.page .box_17 .section_2 .image-text_8 .text-group_8 .text_77 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 32rpx;
}
.page .box_17 .section_2 .image-text_8 .text-group_8 .text_78 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .box_17 .section_2 .image-text_8 .text-group_8 .text_79 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .box_17 .section_2 .image-text_8 .text-group_8 .text_80 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .box_17 .section_2 .text_81 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .box_17 .section_3 {
  width: 184rpx;
  height: 28rpx;
  margin: 68rpx 0 42rpx 22rpx;
}
.page .box_17 .section_3 .text-wrapper_25 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_17 .section_3 .text-wrapper_25 .text_82 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_17 .section_3 .text-wrapper_25 .text_83 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
