@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(247, 247, 247);
  position: relative;
  width: 750rpx;
  height: 1928rpx;
  overflow: hidden;
}
.page .group_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 172rpx;
}
.page .group_1 .group_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .group_1 .group_2 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .group_1 .group_2 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .group_1 .group_2 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .group_1 .group_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .group_1 .group_3 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .group_1 .group_3 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .group_1 .group_3 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .group_1 .group_3 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .image_2 {
  width: 750rpx;
  height: 626rpx;
}
.page .group_4 {
  width: 750rpx;
  height: 1132rpx;
  margin-bottom: 2rpx;
}
.page .group_4 .box_1 {
  width: 700rpx;
  height: 40rpx;
  margin: 388rpx 0 0 22rpx;
}
.page .group_4 .box_1 .text_3 {
  width: 128rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 4rpx;
}
.page .group_4 .box_1 .text_4 {
  width: 128rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 4rpx 0 0 56rpx;
}
.page .group_4 .box_1 .image-text_1 {
  width: 102rpx;
  height: 40rpx;
  margin-left: 286rpx;
}
.page .group_4 .box_1 .image-text_1 .text-group_1 {
  width: 52rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 8rpx;
}
.page .group_4 .box_1 .image-text_1 .thumbnail_5 {
  width: 40rpx;
  height: 40rpx;
}
.page .group_4 .box_2 {
  background-color: rgb(11, 206, 148);
  border-radius: 50px;
  width: 102rpx;
  height: 12rpx;
  margin: 6rpx 0 0 32rpx;
}
.page .group_4 .list_1 {
  width: 702rpx;
  height: 484rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
  margin: 28rpx 0 174rpx 24rpx;
}
.page .group_4 .list_1 .list-items_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 702rpx;
  height: 232rpx;
  margin-bottom: 20rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 {
  position: relative;
  width: 482rpx;
  height: 182rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .image_3 {
  width: 182rpx;
  height: 182rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .text-group_2 {
  width: 276rpx;
  height: 174rpx;
  margin: 4rpx 0 0 22rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .text-group_2 .text_5 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .text-group_2 .text_6 {
  width: 248rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(152, 152, 152);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 20rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .text-group_2 .block_1 {
  width: 276rpx;
  height: 36rpx;
  margin-top: 64rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .text-group_2 .block_1 .text-wrapper_1 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 200rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .text-group_2 .block_1 .text-wrapper_1 .text_7 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 16rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 200rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .text-group_2 .block_1 .text-wrapper_1 .text_8 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .text-group_2 .block_1 .text_9 {
  width: 104rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  text-decoration: line-through;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .text-wrapper_2 {
  background-image: -webkit-linear-gradient(left, rgb(255, 239, 190) 0, rgb(254, 230, 157) 100%);
  background-image: linear-gradient(90deg, rgb(255, 239, 190) 0, rgb(254, 230, 157) 100%);
  border-radius: 10px 10px 0px 10px;
  height: 30rpx;
  width: 72rpx;
  margin: 146rpx 0 0 -186rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .text-wrapper_2 .text_10 {
  width: 48rpx;
  height: 16rpx;
  overflow-wrap: break-word;
  color: rgb(98, 59, 4);
  font-size: 16rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 8rpx 0 0 12rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .box_3 {
  border-radius: 4px;
  width: 154rpx;
  height: 30rpx;
  border: 1px solid rgb(231, 96, 81);
  margin: 92rpx 0 0 -38rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .box_3 .group_5 {
  background-color: rgb(231, 96, 81);
  border-radius: 4px 0px 4px 0px;
  height: 30rpx;
  width: 34rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .box_3 .group_5 .box_4 {
  background-color: rgb(255, 207, 202);
  width: 16rpx;
  height: 20rpx;
  margin: 4rpx 0 0 10rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .box_3 .text_11 {
  width: 106rpx;
  height: 16rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 6rpx 0 8rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .box_5 {
  background-image: -webkit-linear-gradient(top, rgb(59, 41, 0) 0, rgb(86, 54, 0) 100%);
  background-image: linear-gradient(180deg, rgb(59, 41, 0) 0, rgb(86, 54, 0) 100%);
  border-radius: 4px;
  position: absolute;
  left: 204rpx;
  top: 92rpx;
  width: 112rpx;
  height: 30rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .box_5 .image-text_3 {
  width: 90rpx;
  height: 20rpx;
  margin: 6rpx 0 0 12rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .box_5 .image-text_3 .box_6 {
  background-color: rgb(255, 234, 184);
  width: 20rpx;
  height: 20rpx;
}
.page .group_4 .list_1 .list-items_1 .image-text_2 .box_5 .image-text_3 .text-group_3 {
  width: 64rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(255, 234, 184);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .list_1 .list-items_1 .text-wrapper_3 {
  background-color: rgb(11, 206, 148);
  border-radius: 60px;
  height: 50rpx;
  width: 132rpx;
  margin: 160rpx 24rpx 0 0;
}
.page .group_4 .list_1 .list-items_1 .text-wrapper_3 .text_12 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 18rpx;
}
.page .group_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  position: absolute;
  left: 0;
  top: 746rpx;
  width: 750rpx;
  height: 412rpx;
}
.page .group_6 .text_13 {
  width: 278rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 26rpx 0 0 24rpx;
}
.page .group_6 .box_7 {
  width: 698rpx;
  height: 26rpx;
  margin: 34rpx 0 0 26rpx;
}
.page .group_6 .box_7 .image-text_4 {
  width: 178rpx;
  height: 26rpx;
}
.page .group_6 .box_7 .image-text_4 .section_1 {
  background-color: rgb(102, 102, 102);
  width: 24rpx;
  height: 24rpx;
}
.page .group_6 .box_7 .image-text_4 .text-group_4 {
  width: 142rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_6 .box_7 .thumbnail_6 {
  width: 12rpx;
  height: 20rpx;
  margin-top: 2rpx;
}
.page .group_6 .image-text_5 {
  width: 322rpx;
  height: 26rpx;
  margin: 24rpx 0 0 26rpx;
}
.page .group_6 .image-text_5 .thumbnail_7 {
  width: 24rpx;
  height: 24rpx;
}
.page .group_6 .image-text_5 .text-group_5 {
  width: 286rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_6 .box_8 {
  width: 700rpx;
  height: 38rpx;
  margin: 20rpx 0 0 28rpx;
}
.page .group_6 .box_8 .image-text_6 {
  width: 86rpx;
  height: 26rpx;
  margin-top: 4rpx;
}
.page .group_6 .box_8 .image-text_6 .thumbnail_8 {
  width: 20rpx;
  height: 24rpx;
  margin-top: 2rpx;
}
.page .group_6 .box_8 .image-text_6 .text-group_6 {
  width: 52rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_6 .box_8 .text-wrapper_4 {
  border-radius: 2px;
  height: 28rpx;
  border: 0.5px solid rgb(11, 206, 148);
  width: 48rpx;
  margin: 4rpx 0 0 18rpx;
}
.page .group_6 .box_8 .text-wrapper_4 .text_14 {
  width: 40rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 4rpx 0 0 4rpx;
}
.page .group_6 .box_8 .thumbnail_9 {
  width: 32rpx;
  height: 32rpx;
  margin: 4rpx 0 0 460rpx;
}
.page .group_6 .box_8 .thumbnail_10 {
  width: 38rpx;
  height: 38rpx;
  margin-left: 18rpx;
}
.page .group_6 .text-wrapper_5 {
  background-color: rgb(245, 244, 239);
  border-radius: 10px;
  height: 104rpx;
  width: 700rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .group_6 .text-wrapper_5 .text_15 {
  width: 644rpx;
  height: 66rpx;
  overflow-wrap: break-word;
  color: rgb(173, 153, 128);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 320rpx;
  margin: 18rpx 0 0 26rpx;
}
.page .group_6 .box_9 {
  background-color: rgb(247, 247, 247);
  width: 750rpx;
  height: 20rpx;
  margin-top: 38rpx;
}
.page .group_6 .box_10 {
  background-color: rgba(62, 200, 174, 0.2);
  border-radius: 2px;
  position: absolute;
  left: 634rpx;
  top: 192rpx;
  width: 38rpx;
  height: 38rpx;
}
