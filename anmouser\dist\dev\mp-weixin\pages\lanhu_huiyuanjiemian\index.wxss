@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(11, 11, 11);
  position: relative;
  width: 750rpx;
  height: 1776rpx;
  overflow: hidden;
}
.page .box_1 {
  width: 1374rpx;
  height: 2216rpx;
  margin: -350rpx 0 0 -378rpx;
}
.page .box_1 .group_1 {
  position: relative;
  width: 1374rpx;
  height: 1974rpx;
  background: url(/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNG1b24b8fb2f4951617799f0f75436778b.png) 100% no-repeat;
  background-size: 100% 100%;
}
.page .box_1 .group_1 .box_2 {
  width: 688rpx;
  height: 38rpx;
  margin: 364rpx 0 0 410rpx;
}
.page .box_1 .group_1 .box_2 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
}
.page .box_1 .group_1 .box_2 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin-left: 502rpx;
}
.page .box_1 .group_1 .box_2 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin-left: 6rpx;
}
.page .box_1 .group_1 .box_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin-left: 6rpx;
}
.page .box_1 .group_1 .box_3 {
  position: relative;
  width: 288rpx;
  height: 288rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
  margin: 74rpx 0 0 380rpx;
}
.page .box_1 .group_1 .box_3 .image-wrapper_1 {
  height: 288rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
  width: 288rpx;
  position: absolute;
  left: 226rpx;
  top: 0;
}
.page .box_1 .group_1 .box_3 .image-wrapper_1 .image_1 {
  width: 288rpx;
  height: 288rpx;
}
.page .box_1 .group_1 .box_3 .thumbnail_4 {
  position: absolute;
  left: 34rpx;
  top: -24rpx;
  width: 18rpx;
  height: 34rpx;
}
.page .box_1 .group_1 .text-wrapper_1 {
  width: 576rpx;
  height: 34rpx;
  margin: 164rpx 0 0 454rpx;
}
.page .box_1 .group_1 .text-wrapper_1 .text_2 {
  width: 56rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 34rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.page .box_1 .group_1 .text-wrapper_1 .text_3 {
  width: 32rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 22rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 22rpx;
  margin-left: 130rpx;
}
.page .box_1 .group_1 .text-wrapper_1 .text_4 {
  width: 32rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 22rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 22rpx;
  margin-left: 148rpx;
}
.page .box_1 .group_1 .text-wrapper_1 .text_5 {
  width: 32rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 22rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 22rpx;
  margin-left: 146rpx;
}
.page .box_1 .group_1 .box_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 306rpx;
  width: 684rpx;
  margin: 34rpx 0 0 410rpx;
}
.page .box_1 .group_1 .box_4 .text-wrapper_2 {
  width: 128rpx;
  height: 38rpx;
  margin: 36rpx 0 0 22rpx;
}
.page .box_1 .group_1 .box_4 .text-wrapper_2 .text_6 {
  width: 128rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_4 .group_2 {
  width: 614rpx;
  height: 98rpx;
  margin: 26rpx 0 0 38rpx;
}
.page .box_1 .group_1 .box_4 .group_2 .group_3 {
  background-image: -webkit-linear-gradient(314deg, rgb(246, 200, 101) 0, rgb(240, 177, 76) 100%);
  background-image: linear-gradient(136deg, rgb(246, 200, 101) 0, rgb(240, 177, 76) 100%);
  border-radius: 50%;
  width: 98rpx;
  height: 98rpx;
}
.page .box_1 .group_1 .box_4 .group_2 .group_4 {
  background-image: -webkit-linear-gradient(302deg, rgb(239, 144, 133) 0, rgb(238, 124, 104) 100%);
  background-image: linear-gradient(148deg, rgb(239, 144, 133) 0, rgb(238, 124, 104) 100%);
  border-radius: 50%;
  width: 98rpx;
  height: 98rpx;
  margin-left: 74rpx;
}
.page .box_1 .group_1 .box_4 .group_2 .group_5 {
  background-image: -webkit-linear-gradient(309deg, rgb(112, 193, 126) 0, rgb(78, 177, 111) 100%);
  background-image: linear-gradient(141deg, rgb(112, 193, 126) 0, rgb(78, 177, 111) 100%);
  border-radius: 50%;
  width: 98rpx;
  height: 98rpx;
  margin-left: 74rpx;
}
.page .box_1 .group_1 .box_4 .group_2 .group_6 {
  background-image: -webkit-linear-gradient(top, rgb(255, 185, 186) 0, rgb(248, 161, 162) 100%);
  background-image: linear-gradient(180deg, rgb(255, 185, 186) 0, rgb(248, 161, 162) 100%);
  border-radius: 50%;
  width: 98rpx;
  height: 98rpx;
  margin-left: 74rpx;
}
.page .box_1 .group_1 .box_4 .group_7 {
  width: 628rpx;
  height: 70rpx;
  margin: 6rpx 0 32rpx 30rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_3 {
  width: 118rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 268rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_3 .paragraph_1 {
  width: 118rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 268rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_3 .text_7 {
  width: 118rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 268rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_4 {
  width: 120rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 268rpx;
  margin-left: 42rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_4 .paragraph_2 {
  width: 120rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 268rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_4 .text_8 {
  width: 120rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 268rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_5 {
  width: 112rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 268rpx;
  margin-left: 74rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_5 .paragraph_3 {
  width: 112rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 268rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_5 .text_9 {
  width: 112rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 268rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_6 {
  width: 112rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 268rpx;
  margin-left: 50rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_6 .paragraph_4 {
  width: 112rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 268rpx;
}
.page .box_1 .group_1 .box_4 .group_7 .text-wrapper_6 .text_10 {
  width: 112rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 268rpx;
}
.page .box_1 .group_1 .box_5 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 630rpx;
  width: 684rpx;
  margin: 20rpx 0 22rpx 410rpx;
}
.page .box_1 .group_1 .box_5 .text-wrapper_7 {
  width: 128rpx;
  height: 38rpx;
  margin: 38rpx 0 0 22rpx;
}
.page .box_1 .group_1 .box_5 .text-wrapper_7 .text_11 {
  width: 128rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_8 {
  width: 634rpx;
  height: 88rpx;
  margin: 40rpx 0 0 26rpx;
}
.page .box_1 .group_1 .box_5 .group_8 .image-wrapper_2 {
  background-color: rgb(233, 248, 241);
  border-radius: 50%;
  height: 88rpx;
  width: 88rpx;
}
.page .box_1 .group_1 .box_5 .group_8 .image-wrapper_2 .label_1 {
  width: 46rpx;
  height: 46rpx;
  margin: 20rpx 0 0 22rpx;
}
.page .box_1 .group_1 .box_5 .group_8 .text-group_1 {
  width: 270rpx;
  height: 72rpx;
  margin: 6rpx 0 0 14rpx;
}
.page .box_1 .group_1 .box_5 .group_8 .text-group_1 .text_12 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_8 .text-group_1 .text-wrapper_8 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin-top: 10rpx;
}
.page .box_1 .group_1 .box_5 .group_8 .text-group_1 .text-wrapper_8 .text_13 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_8 .text-group_1 .text-wrapper_8 .text_14 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_8 .text-wrapper_9 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 60rpx;
  width: 142rpx;
  margin: 12rpx 0 0 120rpx;
}
.page .box_1 .group_1 .box_5 .group_8 .text-wrapper_9 .text_15 {
  width: 78rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .group_1 .box_5 .group_9 {
  width: 634rpx;
  height: 88rpx;
  margin: 32rpx 0 0 28rpx;
}
.page .box_1 .group_1 .box_5 .group_9 .label_2 {
  width: 88rpx;
  height: 88rpx;
}
.page .box_1 .group_1 .box_5 .group_9 .text-group_2 {
  width: 270rpx;
  height: 72rpx;
  margin: 6rpx 0 0 14rpx;
}
.page .box_1 .group_1 .box_5 .group_9 .text-group_2 .text_16 {
  width: 56rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_9 .text-group_2 .text-wrapper_10 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin-top: 10rpx;
}
.page .box_1 .group_1 .box_5 .group_9 .text-group_2 .text-wrapper_10 .text_17 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_9 .text-group_2 .text-wrapper_10 .text_18 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_9 .text-wrapper_11 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 60rpx;
  width: 142rpx;
  margin: 12rpx 0 0 120rpx;
}
.page .box_1 .group_1 .box_5 .group_9 .text-wrapper_11 .text_19 {
  width: 78rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .group_1 .box_5 .group_10 {
  width: 634rpx;
  height: 88rpx;
  margin: 38rpx 0 0 28rpx;
}
.page .box_1 .group_1 .box_5 .group_10 .group_11 {
  background-color: rgb(255, 245, 240);
  border-radius: 50%;
  height: 88rpx;
  width: 88rpx;
}
.page .box_1 .group_1 .box_5 .group_10 .group_11 .group_12 {
  background-color: rgb(255, 104, 83);
  width: 52rpx;
  height: 48rpx;
  margin: 22rpx 0 0 18rpx;
}
.page .box_1 .group_1 .box_5 .group_10 .text-group_3 {
  width: 270rpx;
  height: 72rpx;
  margin: 6rpx 0 0 14rpx;
}
.page .box_1 .group_1 .box_5 .group_10 .text-group_3 .text_20 {
  width: 56rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_10 .text-group_3 .text-wrapper_12 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin-top: 10rpx;
}
.page .box_1 .group_1 .box_5 .group_10 .text-group_3 .text-wrapper_12 .text_21 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_10 .text-group_3 .text-wrapper_12 .text_22 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_10 .text-wrapper_13 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 60rpx;
  width: 142rpx;
  margin: 12rpx 0 0 120rpx;
}
.page .box_1 .group_1 .box_5 .group_10 .text-wrapper_13 .text_23 {
  width: 78rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .group_1 .box_5 .group_13 {
  width: 634rpx;
  height: 88rpx;
  margin: 40rpx 0 52rpx 30rpx;
}
.page .box_1 .group_1 .box_5 .group_13 .image-wrapper_3 {
  background-color: rgb(241, 251, 242);
  border-radius: 50%;
  height: 88rpx;
  width: 88rpx;
}
.page .box_1 .group_1 .box_5 .group_13 .image-wrapper_3 .label_3 {
  width: 48rpx;
  height: 48rpx;
  margin: 20rpx 0 0 20rpx;
}
.page .box_1 .group_1 .box_5 .group_13 .text-group_4 {
  width: 270rpx;
  height: 72rpx;
  margin: 6rpx 0 0 14rpx;
}
.page .box_1 .group_1 .box_5 .group_13 .text-group_4 .text_24 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_13 .text-group_4 .text-wrapper_14 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin-top: 10rpx;
}
.page .box_1 .group_1 .box_5 .group_13 .text-group_4 .text-wrapper_14 .text_25 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_13 .text-group_4 .text-wrapper_14 .text_26 {
  width: 270rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_5 .group_13 .text-wrapper_15 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 60rpx;
  width: 142rpx;
  margin: 12rpx 0 0 120rpx;
}
.page .box_1 .group_1 .box_5 .group_13 .text-wrapper_15 .text_27 {
  width: 78rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .group_1 .box_6 {
  box-shadow: inset 1px 1px 0px 0px rgb(255, 255, 255);
  background-image: -webkit-linear-gradient(296deg, rgb(255, 250, 240) 0, rgb(206, 158, 120) 100%);
  background-image: linear-gradient(154deg, rgb(255, 250, 240) 0, rgb(206, 158, 120) 100%);
  border-radius: 20px;
  height: 302rpx;
  width: 686rpx;
  position: absolute;
  left: 410rpx;
  top: 540rpx;
}
.page .box_1 .group_1 .box_6 .box_7 {
  height: 302rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
  width: 686rpx;
  position: relative;
}
.page .box_1 .group_1 .box_6 .box_7 .box_8 {
  width: 662rpx;
  height: 132rpx;
  margin: 24rpx 0 0 28rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_8 .image-wrapper_4 {
  background-image: -webkit-linear-gradient(271deg, rgb(255, 234, 193) 0, rgb(235, 193, 162) 100%);
  background-image: linear-gradient(179deg, rgb(255, 234, 193) 0, rgb(235, 193, 162) 100%);
  border-radius: 50%;
  height: 112rpx;
  border: 2px gradient;
  width: 112rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_8 .image-wrapper_4 .label_4 {
  width: 64rpx;
  height: 84rpx;
  margin: 14rpx 0 0 24rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_8 .text-group_5 {
  width: 200rpx;
  height: 86rpx;
  margin: 14rpx 0 0 20rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_8 .text-group_5 .text_28 {
  background-image: -webkit-linear-gradient(304deg, rgb(213, 172, 135) 0, rgb(185, 124, 72) 100%);
  background-image: linear-gradient(146deg, rgb(213, 172, 135) 0, rgb(185, 124, 72) 100%);
  width: 200rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: #000;
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.page .box_1 .group_1 .box_6 .box_7 .box_8 .text-group_5 .text_29 {
  width: 196rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(185, 125, 73);
  font-size: 26rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin: 14rpx 0 0 2rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_8 .box_9 {
  background-image: -webkit-linear-gradient(359deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  background-image: linear-gradient(91deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  width: 60rpx;
  height: 42rpx;
  margin: 90rpx 0 0 118rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_8 .box_10 {
  background-image: -webkit-linear-gradient(359deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  background-image: linear-gradient(91deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  height: 42rpx;
  width: 60rpx;
  margin: 20rpx 0 0 2rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_8 .box_10 .box_11 {
  background-color: rgb(255, 255, 255);
  width: 26rpx;
  height: 26rpx;
  border: 0.0857142881px solid rgb(62, 116, 255);
  margin: 8rpx 0 0 16rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_8 .box_12 {
  background-image: -webkit-linear-gradient(359deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  background-image: linear-gradient(91deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  width: 60rpx;
  height: 42rpx;
  margin: 38rpx 0 0 30rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_13 {
  width: 638rpx;
  height: 42rpx;
  margin: 16rpx 0 0 42rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_13 .text_30 {
  width: 398rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(132, 84, 40);
  font-size: 22rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_13 .image_2 {
  width: 48rpx;
  height: 36rpx;
  margin: 6rpx 0 0 66rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_13 .group_14 {
  height: 36rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAASCAYAAAAzI3woAAABoElEQVR4nL2WQU4qQRCGv5qoa44wnMC5geUJxBsMiRp3DxVNXIErExRHdyYu5AY+TzDtDfAEcAT2yNRb8BTEmRER5k96U13d9eXvTnVDgdLIShqZn5fjFcQyVkLEiJ627TELTIpi0ch8RvRmqt/iceeOpP8eKs6hEdGXmFEjIdZrq+QD1ay0TBZtWwhUUicNH3jSK2tC2pEdWBNoIHQQLrif2PkLoN7/wvkyjj4D7VkNb8baX4Jp20KMxznT/06A9i1EchYuAKaR+STEc7kjNN2JXIyBDk1JiFPSuoADuhivPEg3F6BlAWtskRDAx/hOA/GoxMfyMubatwCIEQYIjoQu8MKQPh0ZZBa/NJ8NFNjEUAQfozQHwLQcQ6rufOL6GkMAynnFP4FEFjCiAVSwqQnLWpEN4+qyPRtcuDHqpfmss4NRQ+a4I6sGmpbe2A4JIVm95gdAS+nU7lieXV12GVIGOhj9Rfda6tPhzqXv6lLljW0SqouArfxx1ZaFCI2Ue7a6I8uTO5OOO5WyeCjjnparwr4f79KWBXj8Afw0h/4Bfd6XT8EVrT4AAAAASUVORK5CYII=) 100% no-repeat;
  background-size: 100% 100%;
  width: 72rpx;
  margin: 6rpx 0 0 54rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_13 .group_14 .group_15 {
  background-image: -webkit-linear-gradient(359deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  background-image: linear-gradient(91deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  width: 60rpx;
  height: 42rpx;
  margin: -30rpx 0 0 50rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_14 {
  width: 614rpx;
  height: 52rpx;
  margin: 2rpx 0 34rpx 44rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_14 .image-text_1 {
  width: 306rpx;
  height: 52rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_14 .image-text_1 .image_3 {
  width: 306rpx;
  height: 18rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_14 .image-text_1 .text-group_6 {
  width: 214rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(132, 84, 40);
  font-size: 22rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin-top: 6rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_14 .text_31 {
  width: 32rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(64, 140, 255);
  font-size: 24rpx;
  font-family: Inter-Semi Bold;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 240rpx;
  margin: 14rpx 0 0 202rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_14 .section_1 {
  background-image: -webkit-linear-gradient(359deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  background-image: linear-gradient(91deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  width: 60rpx;
  height: 42rpx;
  margin-left: 14rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_15 {
  background-image: -webkit-linear-gradient(359deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  background-image: linear-gradient(91deg, rgb(227, 235, 255) 48.875913%, rgb(209, 229, 238) 48.885912%, rgb(179, 194, 217) 48.89591%);
  position: absolute;
  left: 508rpx;
  top: 200rpx;
  width: 60rpx;
  height: 42rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_16 {
  background-image: -webkit-linear-gradient(311deg, rgb(212, 227, 255) 0, rgb(240, 245, 255) 52.083331%, rgb(208, 223, 254) 100%);
  background-image: linear-gradient(139deg, rgb(212, 227, 255) 0, rgb(240, 245, 255) 52.083331%, rgb(208, 223, 254) 100%);
  border-radius: 50%;
  height: 132rpx;
  border: 0.4285714328px gradient;
  width: 134rpx;
  position: absolute;
  left: 504rpx;
  top: 64rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_16 .group_16 {
  box-shadow: inset 0px 0px 1px 0px rgba(0, 44, 157, 0.15);
  background-image: -webkit-linear-gradient(309deg, rgb(177, 204, 255) 0, rgb(213, 227, 255) 100%);
  background-image: linear-gradient(141deg, rgb(177, 204, 255) 0, rgb(213, 227, 255) 100%);
  border-radius: 50%;
  height: 116rpx;
  width: 118rpx;
  position: relative;
  margin: 8rpx 0 0 8rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_16 .group_16 .group_17 {
  box-shadow: inset 0px 0px 4px 0px rgb(112, 160, 255);
  background-image: -webkit-linear-gradient(294deg, rgb(120, 166, 255) 0, rgb(55, 110, 193) 48.4375%, rgb(19, 82, 176) 100%);
  background-image: linear-gradient(156deg, rgb(120, 166, 255) 0, rgb(55, 110, 193) 48.4375%, rgb(19, 82, 176) 100%);
  border-radius: 50%;
  width: 98rpx;
  height: 98rpx;
  margin: 10rpx 0 0 10rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_16 .group_16 .group_17 .box_17 {
  background-color: rgb(255, 255, 255);
  width: 10rpx;
  height: 10rpx;
  margin: 6rpx 0 0 18rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_16 .group_16 .group_17 .box_18 {
  box-shadow: 0px 0px 1px 0px rgba(29, 93, 190, 0.68);
  width: 66rpx;
  height: 62rpx;
  margin: 4rpx 0 16rpx 16rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_16 .group_16 .group_18 {
  background-color: rgb(255, 255, 255);
  position: absolute;
  left: 6rpx;
  top: 0;
  width: 24rpx;
  height: 24rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_19 {
  position: absolute;
  left: 538rpx;
  top: 114rpx;
  width: 118rpx;
  height: 36rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADsAAAASCAYAAADlhqZNAAADWklEQVR4nO2WzW4cVRCFv+oe9s0TxOYJeIRmCUhgdkaKsFkAG6SMhQCxAMerLGIyDghFIotxFCR+JcewCBIWPbxAzBPgeQMigSDuW3WLxe3u6djGYMXEAVFSS93TU3XuqVOnZuD/+I/HqPJi9J2XZ32O044+p6y9kciuZFTv7/j40m2fO4uDnWZcuu3llR3fQ6g2dryCHlmgcMeDsqyR3Xe/9uHZHPPBYnXLi7VvfMOcqlbmNOKxedeR9chmMMQiqFGYMXrzK6+Gn/57VH7jSy9r2K2NC0GhTpeEmk3okb2nbFsEjbg5qOMaKRX2Xr3pF8+KwN+J4diL1z/zLY1JzWB4bVAb1Mr07WfkBvTIvvO0TNSYaIQQcVVEDdRAI6svjf2nxWuPnsrLYx/+krNXK8/31awNrxUPxqT9bt+zBOOWGmKKaMQbdWlGe16Fvec+fDRUXrzmc4vXvaqVUVCKoLBveEiKeghIbfC7pxEGkH6B1S0vfjN+bhIITXIwJGhTJDViuu88NVmR6cMmCfDsBz7Mc94bZDyeC+Q5nmcwECTPoLtyph+9KPNtnhwstPKFV8Eo65aoIl23kgcIinjK3iBjbbIidx8GyXLkT4oxGuSUgxkhzzPIBRn0iWYwyFn++HzyKxwYY4BgrKl1o0u7tFr/xog4OADO0I075RVf+odJFuVlv4hxB6EMMS2gXvPp+TXdB6gDP/TrHFJ2OPbi15y9YBS14WpIHaCOyb/BAG/yBMcRBAduNCpPT5XoZS/JGOOcQ2jbLAAieCbIIMdzgTzrxtgHwvbnr8kL/VqHlN14We4GY1OTsmkje6OwIg1R74jSPS0TqU5L5XLkRbnuGwgVzlyHygzfPf0vqANSK9JTWfaNWwdrHlIW4Px1L4NTdcpqUjTOxvfIPGZ9fyCVy3VfQBjhnLvvjHI8fiZ4lsEgY/rtBXni0Pujkj55RSYWmVgzthohegJx7xGTpsMtSZCm78tEvi/XfeFEJJOaFbDVqdmPv8CPjmiEWu/36rFkAdSZWEy/txZnnwudR6WFk5mH6I32PDA+CVmMJaA8AutE+NG4elT5wZ8C3+NqEBZipABcpNvCya3tYpKOnDvdU4p42DfHRmCbx1hCKEhgbbWT4E8mb8mPR5X/AzAHM74KK79KAAAAAElFTkSuQmCC) 100% no-repeat;
  background-size: 100% 100%;
}
.page .box_1 .group_1 .box_6 .box_7 .box_19 .group_19 {
  background-image: -webkit-linear-gradient(top, rgb(176, 198, 255) 0, rgb(64, 140, 255) 100%);
  background-image: linear-gradient(180deg, rgb(176, 198, 255) 0, rgb(64, 140, 255) 100%);
  border-radius: 50%;
  position: absolute;
  left: 0;
  top: -2rpx;
  width: 8rpx;
  height: 8rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_19 .group_20 {
  background-image: -webkit-linear-gradient(top, rgb(176, 198, 255) 0, rgb(64, 140, 255) 100%);
  background-image: linear-gradient(180deg, rgb(176, 198, 255) 0, rgb(64, 140, 255) 100%);
  border-radius: 50%;
  position: absolute;
  left: 58rpx;
  top: -2rpx;
  width: 8rpx;
  height: 8rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_20 {
  background-image: -webkit-linear-gradient(left, rgb(144, 189, 255) 49.728024%, rgb(64, 140, 255) 49.738023%);
  background-image: linear-gradient(90deg, rgb(144, 189, 255) 49.728024%, rgb(64, 140, 255) 49.738023%);
  border-radius: 1px;
  height: 56rpx;
  width: 48rpx;
  position: absolute;
  left: 546rpx;
  top: 108rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_20 .group_21 {
  height: 56rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAcCAYAAACUJBTQAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHCSURBVHgB7ZW7SgNREIb/2d3E9VZYCCrWIgg2aUSICtaWPoadhTGKsfA9bKy8oA9imVZQ8EbUSKIxu9nN+J+1shBz3MVGB2Zhz9mZb2f+cwH+rKmqwLiFObC0SgWys48+mxhryMwMpDGIgY0DHek1xhpSHYXk8vC9PhR6jbGGTAxDRJFXF+OlUy32EmMNMebTu0Ce6q/28r015Pwc8FyIoUAxXT7V+e9irCEFKvEaQlwXXcdBzKH172J+1C5XELOQWBUd+nz5TBcyhUw1oR4B1COgvzmCQIGtTCG1GpXwEfHZYvImPeRqK5ZOdDEzSHWV6V2EGiWQBofadKPPdmaQXa6pgTY64qPJdj2L4JXDEbVZ+mrf2AvPzK17aiKgOngy1RAQ8N2cmqVMIEyI2wJXVcB2dfFEPeoUP2kZIcXysc6lhggPlfoFuo0YLVZVI/Mu0UYRmmkeN2upIcYOq9DJPNo5D48E3HDokb0Kkp/gft080tnUEKqv/VcIud3rrOCaie/YthfOxMm8h+X0EHZlbA1R3MaLo6xEcclKHvCxnGNxcPXpa6QwcxVXDpHDEPxOB/1RhBzvGt1bkWv827/9ir0D3QKurTTpoJ4AAAAASUVORK5CYII=) 100% no-repeat;
  background-size: 100% 100%;
  width: 48rpx;
  position: relative;
}
.page .box_1 .group_1 .box_6 .box_7 .box_20 .group_21 .group_22 {
  background-image: -webkit-linear-gradient(left, rgb(144, 189, 255) 0, rgb(69, 143, 255) 100%);
  background-image: linear-gradient(90deg, rgb(144, 189, 255) 0, rgb(69, 143, 255) 100%);
  border-radius: 4px;
  width: 38rpx;
  height: 6rpx;
  margin: 44rpx 0 0 4rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_20 .group_21 .group_23 {
  background-image: -webkit-linear-gradient(top, rgb(176, 198, 255) 0, rgb(64, 140, 255) 100%);
  background-image: linear-gradient(180deg, rgb(176, 198, 255) 0, rgb(64, 140, 255) 100%);
  border-radius: 50%;
  position: absolute;
  left: 20rpx;
  top: -6rpx;
  width: 8rpx;
  height: 8rpx;
}
.page .box_1 .group_1 .box_6 .box_7 .box_21 {
  position: absolute;
  left: 518rpx;
  top: 160rpx;
  width: 104rpx;
  height: 34rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
}
.page .box_1 .group_1 .text_32 {
  position: absolute;
  left: 454rpx;
  top: 446rpx;
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_1 .group_1 .image_4 {
  position: absolute;
  left: 942rpx;
  top: 436rpx;
  width: 174rpx;
  height: 64rpx;
}
.page .box_1 .group_1 .box_22 {
  background-color: rgba(208, 229, 252, 0.42);
  height: 8rpx;
  width: 758rpx;
  position: absolute;
  left: 376rpx;
  top: 902rpx;
}
.page .box_1 .group_1 .box_22 .box_23 {
  background-color: rgb(91, 169, 254);
  width: 280rpx;
  height: 8rpx;
}
.page .box_1 .group_1 .box_24 {
  background-color: rgb(91, 169, 254);
  border-radius: 50%;
  position: absolute;
  left: 1002rpx;
  top: 894rpx;
  width: 24rpx;
  height: 24rpx;
  border: 1.5px solid rgb(49, 114, 185);
}
.page .box_1 .group_1 .box_25 {
  background-color: rgb(91, 169, 254);
  border-radius: 50%;
  position: absolute;
  left: 642rpx;
  top: 894rpx;
  width: 24rpx;
  height: 24rpx;
  border: 1.5px solid rgb(49, 114, 185);
}
.page .box_1 .group_1 .box_26 {
  background-color: rgb(91, 169, 254);
  border-radius: 50%;
  position: absolute;
  left: 822rpx;
  top: 894rpx;
  width: 24rpx;
  height: 24rpx;
  border: 1.5px solid rgb(49, 114, 185);
}
.page .box_1 .group_1 .box_27 {
  background-color: rgb(49, 114, 185);
  border-radius: 50%;
  position: absolute;
  left: 462rpx;
  top: 894rpx;
  width: 24rpx;
  height: 24rpx;
  border: 1.5px solid rgb(255, 255, 255);
}
.page .box_1 .group_24 {
  background-color: rgb(43, 47, 54);
  width: 750rpx;
  height: 244rpx;
  margin: 1972rpx 246rpx 0 -996rpx;
}
