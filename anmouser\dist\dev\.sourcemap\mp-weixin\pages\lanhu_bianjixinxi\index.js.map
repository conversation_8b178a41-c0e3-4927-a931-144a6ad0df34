{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_bianjixinxi/index.vue?ef63", "webpack:///./src/pages/lanhu_bianjixinxi/index.vue?7e0c", "webpack:///./src/pages/lanhu_bianjixinxi/index.vue?2c3d", "webpack:///./src/pages/lanhu_bianjixinxi/index.vue?19d1", "uni-app:///src/pages/lanhu_bianjixinxi/index.vue", "webpack:///./src/pages/lanhu_bianjixinxi/index.vue?25c7", "webpack:///./src/pages/lanhu_bianjixinxi/index.vue?7f54"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loopData0", "lanhuBg0", "lanhuimage0", "lanhutext0", "lanhufontColor0", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAsD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFnCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCC+Rtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QACAC,QAAA,EACA;QACAC,WAAA,EACA;QACAC,UAAA;QACAC,eAAA;MACA,GACA;QACAH,QAAA,EACA;QACAC,WAAA,EACA;QACAC,UAAA;QACAC,eAAA;MACA,GACA;QACAH,QAAA,EACA;QACAC,WAAA,EACA;QACAC,UAAA;QACAC,eAAA;MACA,EACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;AChUA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_bianjixinxi/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_bianjixinxi/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=f641d508&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_bianjixinxi/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=f641d508&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"section_1 flex-col justify-between\">\n      <view class=\"block_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"block_2 flex-row\">\n        <image\n          class=\"thumbnail_4\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n        />\n        <text class=\"text_2\">编辑信息</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n        />\n      </view>\n    </view>\n    <view class=\"section_2 flex-col\">\n      <view class=\"section_3 flex-col justify-between\">\n        <view class=\"group_1 flex-col\">\n          <view class=\"block_3 flex-row\">\n            <view class=\"text-wrapper_1\">\n              <text class=\"text_3\">*</text>\n              <text class=\"text_4\">姓名</text>\n            </view>\n            <text class=\"text_5\">冉</text>\n          </view>\n        </view>\n        <view class=\"group_2 flex-col\">\n          <view class=\"block_4 flex-row\">\n            <view class=\"text-wrapper_2\">\n              <text class=\"text_6\">*</text>\n              <text class=\"text_7\">姓别</text>\n            </view>\n            <text class=\"text_8\">冉</text>\n          </view>\n        </view>\n        <view class=\"group_3 flex-col\">\n          <view class=\"group_4 flex-row\">\n            <view class=\"text-wrapper_3\">\n              <text class=\"text_9\">*</text>\n              <text class=\"text_10\">生日</text>\n            </view>\n            <text class=\"text_11\">2024-09-29</text>\n            <image\n              class=\"thumbnail_5\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG5107ddfcfc41a891aafc4d34691f1a46.png\"\n            />\n          </view>\n        </view>\n        <view class=\"group_5 flex-col\">\n          <view class=\"block_5 flex-row\">\n            <view class=\"text-wrapper_4\">\n              <text class=\"text_12\">*</text>\n              <text class=\"text_13\">手机号</text>\n            </view>\n            <text class=\"text_14\">18723755954</text>\n          </view>\n        </view>\n        <view class=\"group_6 flex-col\">\n          <view class=\"block_6 flex-row\">\n            <view class=\"text-wrapper_5\">\n              <text class=\"text_15\">*</text>\n              <text class=\"text_16\">从业年份</text>\n            </view>\n            <text class=\"text_17\">冉</text>\n          </view>\n        </view>\n        <view class=\"group_7 flex-col\">\n          <view class=\"section_4 flex-row\">\n            <view class=\"text-wrapper_6\">\n              <text class=\"text_18\">*</text>\n              <text class=\"text_19\">意向工作城市</text>\n            </view>\n            <text class=\"text_20\">重庆</text>\n            <image\n              class=\"thumbnail_6\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG5107ddfcfc41a891aafc4d34691f1a46.png\"\n            />\n          </view>\n        </view>\n        <view class=\"group_8 flex-col\">\n          <view class=\"group_9 flex-row\">\n            <view class=\"text-wrapper_7\">\n              <text class=\"text_21\">*</text>\n              <text class=\"text_22\">意向工作城市</text>\n            </view>\n            <text class=\"text_23\">重庆市</text>\n            <image\n              class=\"thumbnail_7\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG5107ddfcfc41a891aafc4d34691f1a46.png\"\n            />\n          </view>\n        </view>\n        <view class=\"group_10 flex-col\">\n          <view class=\"box_1 flex-row\">\n            <view class=\"text-wrapper_8\">\n              <text class=\"text_24\">*</text>\n              <text class=\"text_25\">从出行费半径距离</text>\n            </view>\n            <text class=\"text_26\">5.00</text>\n            <text class=\"text_27\">km</text>\n          </view>\n        </view>\n        <view class=\"group_11 flex-col\">\n          <view class=\"section_5 flex-row\">\n            <view class=\"text-wrapper_9\">\n              <text class=\"text_28\">*</text>\n              <text class=\"text_29\">所在城市</text>\n            </view>\n            <text class=\"text_30\">点击右边图标设置</text>\n            <view class=\"block_7 flex-col\"></view>\n          </view>\n        </view>\n      </view>\n      <view class=\"section_6 flex-col\">\n        <view class=\"text-wrapper_10\">\n          <text class=\"text_31\">*</text>\n          <text class=\"text_32\">技师简介</text>\n        </view>\n        <text class=\"text_33\">请输入技师简介</text>\n      </view>\n      <view class=\"section_7 flex-row\">\n        <view class=\"text-wrapper_11\">\n          <text class=\"text_34\">*</text>\n          <text class=\"text_35\">身份证照片</text>\n        </view>\n        <text class=\"text_36\">图片大小不超过10M</text>\n      </view>\n    </view>\n    <view class=\"section_8 flex-col\">\n      <view class=\"box_2 flex-row justify-between\">\n        <view class=\"block_8 flex-col\">\n          <view class=\"image-wrapper_1 flex-col\">\n            <image\n              class=\"thumbnail_8\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG442be65c6063e81fd71a74fec7263d0c.png\"\n            />\n          </view>\n          <text class=\"text_37\">重新上传</text>\n        </view>\n        <view class=\"block_9 flex-col\">\n          <view class=\"image-wrapper_2 flex-col\">\n            <image\n              class=\"thumbnail_9\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG442be65c6063e81fd71a74fec7263d0c.png\"\n            />\n          </view>\n          <text class=\"text_38\">重新上传</text>\n        </view>\n      </view>\n      <view class=\"box_3 flex-row\">\n        <view class=\"section_9 flex-col\">\n          <view class=\"image-wrapper_3 flex-col\">\n            <image\n              class=\"thumbnail_10\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG442be65c6063e81fd71a74fec7263d0c.png\"\n            />\n          </view>\n          <text class=\"text_39\">重新上传</text>\n        </view>\n      </view>\n    </view>\n    <view class=\"section_10 flex-col\">\n      <view class=\"box_4 flex-row\">\n        <view class=\"text-wrapper_12\">\n          <text class=\"text_40\">*</text>\n          <text class=\"text_41\">资格证书</text>\n        </view>\n        <text class=\"text_42\">图片大小不超过10M</text>\n      </view>\n      <view class=\"box_5 flex-col\">\n        <view class=\"list_1 flex-row\">\n          <view\n            class=\"list-items_1 flex-col\"\n            :style=\"{ background: item.lanhuBg0 }\"\n            v-for=\"(item, index) in loopData0\"\n            :key=\"index\"\n          >\n            <view class=\"image-wrapper_4 flex-col\">\n              <image\n                class=\"thumbnail_11\"\n                referrerpolicy=\"no-referrer\"\n                :src=\"item.lanhuimage0\"\n              />\n            </view>\n            <text\n              class=\"text_43\"\n              :style=\"{ color: item.lanhufontColor0 }\"\n              v-html=\"item.lanhutext0\"\n            ></text>\n          </view>\n        </view>\n      </view>\n      <view class=\"box_6 flex-row\">\n        <view class=\"text-wrapper_13\">\n          <text class=\"text_44\">*</text>\n          <text class=\"text_45\">工作形象照</text>\n        </view>\n        <text class=\"text_46\">图片建议尺寸750*750&nbsp;大小不超过10M</text>\n      </view>\n      <view class=\"box_7 flex-col\">\n        <view class=\"box_8 flex-col\">\n          <view class=\"image-wrapper_5 flex-col\">\n            <image\n              class=\"thumbnail_12\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG442be65c6063e81fd71a74fec7263d0c.png\"\n            />\n          </view>\n          <text class=\"text_47\">重新上传</text>\n        </view>\n      </view>\n      <view class=\"box_9 flex-row\">\n        <view class=\"text-wrapper_14\">\n          <text class=\"text_48\">*</text>\n          <text class=\"text_49\">个人生活照</text>\n        </view>\n        <text class=\"text_50\">图片建议尺寸750*750&nbsp;大小不超过10M</text>\n      </view>\n      <view class=\"box_10 flex-col\">\n        <view class=\"section_11 flex-col\">\n          <view class=\"image-wrapper_6 flex-col\">\n            <image\n              class=\"thumbnail_13\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG442be65c6063e81fd71a74fec7263d0c.png\"\n            />\n          </view>\n          <text class=\"text_51\">重新上传</text>\n        </view>\n      </view>\n      <view class=\"box_11 flex-row\">\n        <view class=\"text-wrapper_15\">\n          <text class=\"text_52\">*</text>\n          <text class=\"text_53\">个人视频介绍</text>\n        </view>\n        <text class=\"text_54\">视频大小不超过50M</text>\n      </view>\n      <view class=\"box_12 flex-col\">\n        <view class=\"group_12 flex-col\">\n          <view class=\"image-wrapper_7 flex-col\">\n            <image\n              class=\"thumbnail_14\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_bianjixinxi/FigmaDDSSlicePNG442be65c6063e81fd71a74fec7263d0c.png\"\n            />\n          </view>\n          <text class=\"text_55\">重新上传</text>\n        </view>\n      </view>\n      <text class=\"text_56\">\n        编辑资料将进入重新审核，审核通过之前将显示原资料\n      </text>\n    </view>\n    <view class=\"section_12 flex-col\">\n      <view class=\"text-wrapper_16 flex-col\">\n        <text class=\"text_57\">确定申请</text>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      loopData0: [\n        {\n          lanhuBg0:\n            'url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG82e1684e77fc1f61a29c6014aa8cc824.png) 100% no-repeat',\n          lanhuimage0:\n            '/static/lanhu_bianjixinxi/FigmaDDSSlicePNG442be65c6063e81fd71a74fec7263d0c.png',\n          lanhutext0: '重新上传',\n          lanhufontColor0: 'rgba(255,255,255,1.000000)'\n        },\n        {\n          lanhuBg0:\n            'url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG82e1684e77fc1f61a29c6014aa8cc824.png) 100% no-repeat',\n          lanhuimage0:\n            '/static/lanhu_bianjixinxi/FigmaDDSSlicePNG442be65c6063e81fd71a74fec7263d0c.png',\n          lanhutext0: '重新上传',\n          lanhufontColor0: 'rgba(255,255,255,1.000000)'\n        },\n        {\n          lanhuBg0:\n            'url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG3771f955216a4eb51ad24179c7099f38.png) 100% no-repeat',\n          lanhuimage0:\n            '/static/lanhu_bianjixinxi/FigmaDDSSlicePNG442be65c6063e81fd71a74fec7263d0c.png',\n          lanhutext0: '重新上传<br/>2/5',\n          lanhufontColor0: 'rgba(153,153,153,1.000000)'\n        }\n      ],\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332583641\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}