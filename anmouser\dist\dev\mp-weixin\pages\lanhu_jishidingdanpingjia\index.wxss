@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(255, 255, 255);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .box_1 {
  position: relative;
  width: 750rpx;
  height: 746rpx;
}
.page .box_1 .block_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .block_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .block_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .block_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .block_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .block_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .block_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .box_1 .block_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .box_1 .block_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .box_1 .text-wrapper_1 {
  width: 288rpx;
  height: 32rpx;
  margin: 38rpx 0 0 30rpx;
}
.page .box_1 .text-wrapper_1 .text_3 {
  width: 128rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_1 .text-wrapper_1 .text_4 {
  width: 144rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 4rpx;
}
.page .box_1 .block_3 {
  width: 596rpx;
  height: 24rpx;
  margin: 64rpx 0 0 68rpx;
}
.page .box_1 .block_3 .thumbnail_5 {
  width: 20rpx;
  height: 20rpx;
  margin-top: 2rpx;
}
.page .box_1 .block_3 .text_5 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 4rpx;
}
.page .box_1 .block_3 .thumbnail_6 {
  width: 20rpx;
  height: 20rpx;
  margin: 2rpx 0 0 88rpx;
}
.page .box_1 .block_3 .text_6 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 4rpx;
}
.page .box_1 .block_3 .thumbnail_7 {
  width: 20rpx;
  height: 20rpx;
  margin: 2rpx 0 0 88rpx;
}
.page .box_1 .block_3 .text_7 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 4rpx;
}
.page .box_1 .block_3 .thumbnail_8 {
  width: 20rpx;
  height: 20rpx;
  margin: 2rpx 0 0 92rpx;
}
.page .box_1 .block_3 .text_8 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 4rpx;
}
.page .box_1 .block_3 .thumbnail_9 {
  width: 20rpx;
  height: 20rpx;
  margin: 2rpx 0 0 88rpx;
}
.page .box_1 .block_3 .text_9 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 4rpx;
}
.page .box_1 .text_10 {
  width: 128rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 112rpx 0 0 30rpx;
}
.page .box_1 .block_4 {
  width: 256rpx;
  height: 72rpx;
  margin: 36rpx 0 0 30rpx;
}
.page .box_1 .block_4 .group_1 {
  background-color: rgba(62, 200, 174, 0.15);
  border-radius: 60px;
  height: 72rpx;
  width: 120rpx;
  position: relative;
}
.page .box_1 .block_4 .group_1 .text_11 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(62, 200, 174);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 24rpx 0 0 48rpx;
}
.page .box_1 .block_4 .group_1 .thumbnail_10 {
  position: absolute;
  left: 86rpx;
  top: -8rpx;
  width: 20rpx;
  height: 20rpx;
}
.page .box_1 .block_4 .group_2 {
  background-color: rgba(62, 200, 174, 0.15);
  border-radius: 60px;
  height: 72rpx;
  width: 120rpx;
  position: relative;
}
.page .box_1 .block_4 .group_2 .text_12 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(62, 200, 174);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 24rpx 0 0 48rpx;
}
.page .box_1 .block_4 .group_2 .thumbnail_11 {
  position: absolute;
  left: 90rpx;
  top: -8rpx;
  width: 20rpx;
  height: 20rpx;
}
.page .box_1 .text_13 {
  width: 128rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 90rpx 0 42rpx 30rpx;
}
.page .box_1 .block_5 {
  border-radius: 60px;
  position: absolute;
  left: 32rpx;
  top: 282rpx;
  width: 120rpx;
  height: 72rpx;
  border: 1px solid rgb(202, 202, 202);
}
.page .box_1 .block_6 {
  border-radius: 60px;
  position: absolute;
  left: 166rpx;
  top: 282rpx;
  width: 120rpx;
  height: 72rpx;
  border: 1px solid rgb(202, 202, 202);
}
.page .box_1 .block_7 {
  border-radius: 60px;
  position: absolute;
  left: 442rpx;
  top: 282rpx;
  width: 120rpx;
  height: 72rpx;
  border: 1px solid rgb(202, 202, 202);
}
.page .box_1 .block_8 {
  border-radius: 60px;
  position: absolute;
  left: 302rpx;
  top: 282rpx;
  width: 120rpx;
  height: 72rpx;
  border: 1px solid rgb(202, 202, 202);
}
.page .box_1 .block_9 {
  border-radius: 60px;
  position: absolute;
  left: 578rpx;
  top: 282rpx;
  width: 120rpx;
  height: 72rpx;
  border: 1px solid rgb(202, 202, 202);
}
.page .box_2 {
  background-color: rgb(247, 248, 250);
  border-radius: 5px;
  height: 468rpx;
  width: 702rpx;
  margin: -2rpx 0 0 24rpx;
}
.page .box_2 .text-wrapper_2 {
  width: 120rpx;
  height: 24rpx;
  margin: 32rpx 0 0 36rpx;
}
.page .box_2 .text-wrapper_2 .text_14 {
  width: 120rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .text-wrapper_3 {
  width: 70rpx;
  height: 24rpx;
  margin: 360rpx 0 28rpx 590rpx;
}
.page .box_2 .text-wrapper_3 .text_15 {
  width: 70rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_3 {
  width: 750rpx;
  height: 284rpx;
}
.page .box_3 .text-wrapper_4 {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  height: 68rpx;
  width: 188rpx;
  margin: 82rpx 0 0 276rpx;
}
.page .box_3 .text-wrapper_4 .text_16 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 38rpx;
}
.page .box_4 {
  background-color: rgb(255, 255, 255);
  height: 126rpx;
  width: 750rpx;
  margin: -2rpx 0 4rpx 0;
}
.page .box_4 .text-wrapper_5 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 88rpx;
  width: 690rpx;
  margin: 20rpx 0 0 30rpx;
}
.page .box_4 .text-wrapper_5 .text_17 {
  width: 60rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 316rpx;
}
