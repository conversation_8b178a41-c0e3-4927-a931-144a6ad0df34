{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_dingdanjiemian/index.vue?4cc0", "webpack:///./src/pages/lanhu_dingdanjiemian/index.vue?85c6", "webpack:///./src/pages/lanhu_dingdanjiemian/index.vue?7151", "webpack:///./src/pages/lanhu_dingdanjiemian/index.vue?666f", "uni-app:///src/pages/lanhu_dingdanjiemian/index.vue", "webpack:///./src/pages/lanhu_dingdanjiemian/index.vue?0b26", "webpack:///./src/pages/lanhu_dingdanjiemian/index.vue?3b6f"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAyD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFtCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCqOtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;AC5OA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_dingdanjiemian/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_dingdanjiemian/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=c7b20fde&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_dingdanjiemian/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=c7b20fde&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"box_1 flex-col\">\n      <view class=\"section_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"section_2 flex-row\">\n        <image\n          class=\"thumbnail_4\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n        />\n        <text class=\"text_2\">下单</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n        />\n      </view>\n    </view>\n    <view class=\"box_2 flex-col\">\n      <view class=\"block_1 flex-row\">\n        <text class=\"text_3\">出行方式</text>\n        <image\n          class=\"thumbnail_5\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNG5c461f6aca1714cd6ec3cc06af431c1b.png\"\n        />\n        <text class=\"text_4\">出租车</text>\n        <image\n          class=\"thumbnail_6\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNGa3608d005db2c33be946a49f055bd728.png\"\n        />\n        <text class=\"text_5\">公交/地铁</text>\n      </view>\n      <view class=\"block_2 flex-row justify-between\">\n        <text class=\"text_6\">卡券优惠</text>\n        <view class=\"image-text_1 flex-row justify-between\">\n          <text class=\"text-group_1\">0张可用</text>\n          <image\n            class=\"thumbnail_7\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNG804a5aeaa27993298a6fa9dcdf2b2972.png\"\n          />\n        </view>\n      </view>\n      <view class=\"block_3 flex-col justify-end\">\n        <view class=\"text-wrapper_1 flex-row justify-between\">\n          <text class=\"text_7\">往返车费</text>\n          <text class=\"text_8\">￥0</text>\n        </view>\n        <text class=\"text_9\">\n          全程共0m，出租出行9公里内，起步20元。里程计价:3.8元/公里\n        </text>\n      </view>\n      <view class=\"text-wrapper_2 flex-col\">\n        <text class=\"text_10\">价格明细</text>\n      </view>\n      <view class=\"text-wrapper_3 flex-row\">\n        <text class=\"text_11\">项目价格</text>\n        <text class=\"text_12\">￥498.00</text>\n      </view>\n      <view class=\"text-wrapper_4 flex-row justify-between\">\n        <text class=\"text_13\">会员优惠</text>\n        <text class=\"text_14\">88折</text>\n      </view>\n      <view class=\"text-wrapper_5 flex-row justify-between\">\n        <text class=\"text_15\">车费</text>\n        <text class=\"text_16\">￥14.00</text>\n      </view>\n      <view class=\"text-wrapper_6 flex-row justify-between\">\n        <text class=\"text_17\">卡券优惠</text>\n        <text class=\"text_18\">￥-14.00</text>\n      </view>\n      <view class=\"block_4 flex-col\">\n        <view class=\"text-wrapper_7\">\n          <text class=\"text_19\">已优惠</text>\n          <text class=\"text_20\">￥14.00</text>\n          <text class=\"text_21\">&nbsp;&nbsp;需支付￥533.00</text>\n        </view>\n      </view>\n      <view class=\"text-wrapper_8 flex-col\">\n        <text class=\"text_22\">请输入订单备注(选填)</text>\n      </view>\n      <view class=\"block_5 flex-col justify-between\">\n        <view class=\"block_6 flex-row justify-between\">\n          <view class=\"image-text_2 flex-row justify-between\">\n            <image\n              class=\"thumbnail_8\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNGbcde635acd0f911d25cadbcce2d90ae6.png\"\n            />\n            <text class=\"text-group_2\">微信支付</text>\n          </view>\n          <view class=\"box_3 flex-col\"></view>\n        </view>\n        <view class=\"block_7 flex-row\">\n          <view class=\"image-text_3 flex-row justify-between\">\n            <image\n              class=\"thumbnail_9\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNG1a40b4ed3a831a3a2517e31f599bf741.png\"\n            />\n            <view class=\"text-group_3\">\n              <text class=\"text_23\">余额支付&nbsp;</text>\n              <text class=\"text_24\">1424.01元</text>\n            </view>\n          </view>\n          <view class=\"box_4 flex-col\"></view>\n        </view>\n      </view>\n      <view class=\"text-wrapper_9 flex-row justify-between\">\n        <text class=\"text_25\">物料费</text>\n        <text class=\"text_26\">￥4.00</text>\n      </view>\n    </view>\n    <view class=\"box_5 flex-row justify-between\">\n      <view class=\"text-wrapper_10\">\n        <text class=\"text_27\">支付金额:</text>\n        <text class=\"text_28\">￥214</text>\n      </view>\n      <view class=\"text-wrapper_11 flex-col\">\n        <text class=\"text_29\">立即支付</text>\n      </view>\n    </view>\n    <view class=\"box_6 flex-col\">\n      <view class=\"group_1 flex-row\">\n        <view class=\"image-text_4 flex-row justify-between\">\n          <image\n            class=\"thumbnail_10\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNG699f4843e533a0efe7f27a5a9790d645.png\"\n          />\n          <text class=\"text-group_4\">请添加收货地址</text>\n        </view>\n      </view>\n      <view class=\"group_2 flex-row\">\n        <view class=\"image-text_5 flex-row justify-between\">\n          <image\n            class=\"image_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNG1f76baa9c15ec3c2398a54922872dc3c.png\"\n          />\n          <view class=\"text-group_5 flex-col\">\n            <text class=\"text_30\">日式·尊享奢华</text>\n            <view class=\"box_7 flex-row justify-between\">\n              <view class=\"text-wrapper_12\">\n                <text class=\"text_31\">￥</text>\n                <text class=\"text_32\">598</text>\n              </view>\n              <text class=\"text_33\">/120分钟（含物料费￥5.00）</text>\n            </view>\n            <text class=\"text_34\">服务理疗师：王燕燕</text>\n          </view>\n        </view>\n        <image\n          class=\"thumbnail_11\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNGb5f10cb656d5180c5c8b0bd7fc109b79.png\"\n        />\n        <text class=\"text_35\">1</text>\n        <image\n          class=\"thumbnail_12\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNG473e3f399793be314056e27ec9640740.png\"\n        />\n      </view>\n      <view class=\"group_3 flex-row\">\n        <text class=\"text_36\">服务方式</text>\n        <image\n          class=\"thumbnail_13\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNG5c461f6aca1714cd6ec3cc06af431c1b.png\"\n        />\n        <text class=\"text_37\">上门服务</text>\n        <image\n          class=\"thumbnail_14\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNGa3608d005db2c33be946a49f055bd728.png\"\n        />\n        <text class=\"text_38\">到店服务</text>\n      </view>\n      <view class=\"group_4 flex-col\">\n        <view class=\"section_3 flex-row\">\n          <text class=\"text_39\">服务时间</text>\n          <image\n            class=\"thumbnail_15\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNGa3608d005db2c33be946a49f055bd728.png\"\n          />\n          <text class=\"text_40\">立即出发</text>\n          <image\n            class=\"thumbnail_16\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dingdanjiemian/f8ad517d817041f8858a5d2690debee3_mergeImage.png\"\n          />\n          <text class=\"text_41\">服务预约</text>\n        </view>\n        <view class=\"section_4 flex-row\">\n          <view class=\"image-text_6 flex-row justify-between\">\n            <text class=\"text-group_6\">09-03&nbsp;周五&nbsp;17:10</text>\n            <image\n              class=\"thumbnail_17\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dingdanjiemian/FigmaDDSSlicePNG804a5aeaa27993298a6fa9dcdf2b2972.png\"\n            />\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332584277\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}