@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 1752rpx;
  overflow: hidden;
}
.page .group_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .group_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .group_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .group_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .group_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .group_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .group_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .group_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .group_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .group_3 {
  position: relative;
  width: 750rpx;
  height: 218rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAXcAAABtCAYAAABa+iG3AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGMSURBVHgB7dQBEQAQAAAxBBNZDq3o8beF2Nz3vAFAyhoA5MgdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwj6GpUDjXk/frcAAAAASUVORK5CYII=) 100% no-repeat;
  background-size: 100% 100%;
}
.page .group_3 .text-group_1 {
  width: 454rpx;
  height: 112rpx;
  margin: 40rpx 0 0 148rpx;
}
.page .group_3 .text-group_1 .text_3 {
  width: 454rpx;
  height: 52rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 52rpx;
}
.page .group_3 .text-group_1 .text_4 {
  width: 226rpx;
  height: 52rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 52rpx;
  margin: 8rpx 0 0 114rpx;
}
.page .group_3 .box_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  position: absolute;
  left: 24rpx;
  top: 172rpx;
  width: 702rpx;
  height: 146rpx;
}
.page .group_3 .box_1 .label_1 {
  width: 66rpx;
  height: 66rpx;
  margin: 38rpx 0 0 24rpx;
}
.page .group_3 .box_1 .text_5 {
  width: 30rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 38rpx 0 0 20rpx;
}
.page .group_3 .box_1 .text_6 {
  width: 164rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 40rpx 0 0 14rpx;
}
.page .group_3 .box_1 .text_7 {
  width: 164rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: #000;
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 60rpx 200rpx 0 20rpx;
}
.page .group_3 .box_1 .text_8 {
  position: absolute;
  left: 110rpx;
  top: 84rpx;
  width: 508rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 702rpx;
  height: 286rpx;
  margin: 120rpx 0 0 24rpx;
}
.page .group_4 .block_1 {
  width: 654rpx;
  height: 158rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .group_4 .block_1 .image-text_1 {
  width: 356rpx;
  height: 158rpx;
}
.page .group_4 .block_1 .image-text_1 .image_2 {
  width: 156rpx;
  height: 156rpx;
}
.page .group_4 .block_1 .image-text_1 .text-group_2 {
  width: 184rpx;
  height: 158rpx;
}
.page .group_4 .block_1 .image-text_1 .text-group_2 .text_9 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .block_1 .image-text_1 .text-group_2 .text_10 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: -28rpx 0 0 4rpx;
}
.page .group_4 .block_1 .image-text_1 .text-group_2 .text_11 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 4rpx;
}
.page .group_4 .block_1 .image-text_1 .text-group_2 .text-wrapper_1 {
  width: 124rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 52rpx 0 0 4rpx;
}
.page .group_4 .block_1 .image-text_1 .text-group_2 .text-wrapper_1 .text_12 {
  width: 124rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .block_1 .image-text_1 .text-group_2 .text-wrapper_1 .text_13 {
  width: 124rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .block_1 .text_14 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .group_4 .text-wrapper_2 {
  width: 440rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 48rpx 0 24rpx 238rpx;
}
.page .group_4 .text-wrapper_2 .text_15 {
  width: 440rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .text-wrapper_2 .text_16 {
  width: 440rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .text-wrapper_2 .text_17 {
  width: 440rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .text-wrapper_2 .text_18 {
  width: 440rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .text-wrapper_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  height: 80rpx;
  width: 702rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .text-wrapper_3 .text_19 {
  width: 120rpx;
  height: 52rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 52rpx;
  margin: 14rpx 0 0 20rpx;
}
.page .group_5 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 80rpx;
  margin-left: 24rpx;
}
.page .group_5 .text_20 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 32rpx 0 0 24rpx;
}
.page .group_5 .text_21 {
  width: 260rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 28rpx 0 0 206rpx;
}
.page .group_5 .text-wrapper_4 {
  border-radius: 3px;
  height: 38rpx;
  border: 0.75px solid rgb(62, 200, 174);
  width: 72rpx;
  margin: 22rpx 22rpx 0 14rpx;
}
.page .group_5 .text-wrapper_4 .text_22 {
  width: 36rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(62, 200, 174);
  font-size: 18rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 0 18rpx;
}
.page .list_1 {
  width: 702rpx;
  height: 136rpx;
  margin-left: 24rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.page .list_1 .text-wrapper_5 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 68rpx;
}
.page .list_1 .text-wrapper_5 .text_23 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .list_1 .text-wrapper_5 .text_24 {
  width: 292rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 22rpx 0 260rpx;
}
.page .text-wrapper_6 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 68rpx;
  margin-left: 24rpx;
}
.page .text-wrapper_6 .text_25 {
  width: 78rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .text-wrapper_6 .text_26 {
  width: 74rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .text-wrapper_7 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 174rpx;
  margin-left: 24rpx;
}
.page .text-wrapper_7 .text_27 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .text-wrapper_7 .text_28 {
  width: 646rpx;
  height: 82rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 276rpx;
  margin: 28rpx 0 16rpx 26rpx;
}
.page .text-wrapper_8 {
  background-color: rgb(255, 255, 255);
  height: 68rpx;
  margin-left: 24rpx;
  width: 702rpx;
}
.page .text-wrapper_8 .text_29 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .image-wrapper_1 {
  background-color: rgb(255, 255, 255);
  height: 260rpx;
  width: 702rpx;
  margin: 0 0 70rpx 24rpx;
}
.page .image-wrapper_1 .image_3 {
  width: 194rpx;
  height: 194rpx;
  margin: 10rpx 0 0 24rpx;
}
