@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 3188rpx;
  overflow: hidden;
}
.page .section_1 {
  width: 750rpx;
  height: 384rpx;
}
.page .section_1 .box_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .section_1 .box_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .section_1 .box_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .section_1 .box_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .section_1 .box_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .section_1 .box_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .section_1 .box_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .section_1 .box_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .section_1 .box_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .section_1 .box_3 {
  background-color: rgb(255, 255, 255);
  height: 102rpx;
  width: 750rpx;
}
.page .section_1 .box_3 .section_2 {
  background-color: rgb(246, 246, 246);
  border-radius: 50px;
  width: 702rpx;
  height: 78rpx;
  margin: 12rpx 0 0 24rpx;
}
.page .section_1 .box_3 .section_2 .image-text_1 {
  width: 316rpx;
  height: 38rpx;
  margin: 20rpx 0 0 38rpx;
}
.page .section_1 .box_3 .section_2 .image-text_1 .group_1 {
  background-color: rgb(213, 213, 213);
  width: 38rpx;
  height: 38rpx;
}
.page .section_1 .box_3 .section_2 .image-text_1 .text-group_1 {
  width: 260rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .section_1 .text-wrapper_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 88rpx;
  margin-bottom: 22rpx;
}
.page .section_1 .text-wrapper_1 .text_3 {
  width: 56rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 20rpx;
}
.page .section_1 .text-wrapper_1 .text_4 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 46rpx;
}
.page .section_1 .text-wrapper_1 .text_5 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 46rpx;
}
.page .section_1 .text-wrapper_1 .text_6 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 46rpx;
}
.page .section_1 .text-wrapper_1 .text_7 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 46rpx;
}
.page .section_1 .text-wrapper_1 .text_8 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 24rpx 0 46rpx;
}
.page .section_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  position: relative;
  margin: 418rpx 0 0 24rpx;
}
.page .section_3 .section_4 {
  width: 126rpx;
  height: 40rpx;
}
.page .section_3 .section_4 .block_1 {
  background-color: rgb(62, 200, 174);
  border-radius: 10px 0px 0px 10px;
  width: 126rpx;
  height: 40rpx;
}
.page .section_3 .text-wrapper_2 {
  width: 650rpx;
  height: 26rpx;
  margin: 16rpx 0 0 24rpx;
}
.page .section_3 .text-wrapper_2 .text_9 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .text-wrapper_2 .text_10 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .section_5 {
  width: 654rpx;
  height: 126rpx;
  margin: 52rpx 0 0 22rpx;
}
.page .section_3 .section_5 .image-text_2 {
  width: 446rpx;
  height: 126rpx;
}
.page .section_3 .section_5 .image-text_2 .image_2 {
  width: 126rpx;
  height: 126rpx;
}
.page .section_3 .section_5 .image-text_2 .text-group_2 {
  width: 296rpx;
  height: 116rpx;
}
.page .section_3 .section_5 .image-text_2 .text-group_2 .text_11 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 22rpx;
}
.page .section_3 .section_5 .image-text_2 .text-group_2 .text_12 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .section_3 .section_5 .image-text_2 .text-group_2 .text_13 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .section_3 .section_5 .image-text_2 .text-group_2 .text_14 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .section_3 .section_5 .text_15 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .section_3 .section_6 {
  width: 184rpx;
  height: 28rpx;
  margin: 68rpx 0 42rpx 22rpx;
}
.page .section_3 .section_6 .text-wrapper_3 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .section_6 .text-wrapper_3 .text_16 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .section_6 .text-wrapper_3 .text_17 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .text_18 {
  position: absolute;
  left: 24rpx;
  top: -2rpx;
  width: 80rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 44rpx;
}
.page .section_7 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  position: relative;
  margin: 22rpx 0 0 24rpx;
}
.page .section_7 .block_2 {
  width: 126rpx;
  height: 40rpx;
}
.page .section_7 .block_2 .section_8 {
  background-color: rgb(62, 200, 174);
  border-radius: 10px 0px 0px 10px;
  width: 126rpx;
  height: 40rpx;
}
.page .section_7 .text-wrapper_4 {
  width: 656rpx;
  height: 26rpx;
  margin: 16rpx 0 0 24rpx;
}
.page .section_7 .text-wrapper_4 .text_19 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_7 .text-wrapper_4 .text_20 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_7 .block_3 {
  width: 654rpx;
  height: 126rpx;
  margin: 52rpx 0 0 22rpx;
}
.page .section_7 .block_3 .image-text_3 {
  width: 446rpx;
  height: 126rpx;
}
.page .section_7 .block_3 .image-text_3 .image_3 {
  width: 126rpx;
  height: 126rpx;
}
.page .section_7 .block_3 .image-text_3 .text-group_3 {
  width: 296rpx;
  height: 116rpx;
}
.page .section_7 .block_3 .image-text_3 .text-group_3 .text_21 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 22rpx;
}
.page .section_7 .block_3 .image-text_3 .text-group_3 .text_22 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .section_7 .block_3 .image-text_3 .text-group_3 .text_23 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .section_7 .block_3 .image-text_3 .text-group_3 .text_24 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .section_7 .block_3 .text_25 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .section_7 .block_4 {
  width: 184rpx;
  height: 28rpx;
  margin: 68rpx 0 42rpx 22rpx;
}
.page .section_7 .block_4 .text-wrapper_5 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_7 .block_4 .text-wrapper_5 .text_26 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_7 .block_4 .text-wrapper_5 .text_27 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_7 .text_28 {
  position: absolute;
  left: 24rpx;
  top: -2rpx;
  width: 80rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 44rpx;
}
.page .section_9 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  position: relative;
  margin: 22rpx 0 0 24rpx;
}
.page .section_9 .section_10 {
  width: 126rpx;
  height: 40rpx;
}
.page .section_9 .section_10 .box_4 {
  background-color: rgb(62, 200, 174);
  border-radius: 10px 0px 0px 10px;
  width: 126rpx;
  height: 40rpx;
}
.page .section_9 .text-wrapper_6 {
  width: 656rpx;
  height: 26rpx;
  margin: 16rpx 0 0 24rpx;
}
.page .section_9 .text-wrapper_6 .text_29 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_9 .text-wrapper_6 .text_30 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_9 .section_11 {
  width: 658rpx;
  height: 126rpx;
  margin: 52rpx 0 0 22rpx;
}
.page .section_9 .section_11 .image-text_4 {
  width: 446rpx;
  height: 126rpx;
}
.page .section_9 .section_11 .image-text_4 .image_4 {
  width: 126rpx;
  height: 126rpx;
}
.page .section_9 .section_11 .image-text_4 .text-group_4 {
  width: 296rpx;
  height: 116rpx;
}
.page .section_9 .section_11 .image-text_4 .text-group_4 .text_31 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 22rpx;
}
.page .section_9 .section_11 .image-text_4 .text-group_4 .text_32 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .section_9 .section_11 .image-text_4 .text-group_4 .text_33 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .section_9 .section_11 .image-text_4 .text-group_4 .text_34 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .section_9 .section_11 .text-group_5 {
  width: 92rpx;
  height: 68rpx;
  margin-top: 2rpx;
}
.page .section_9 .section_11 .text-group_5 .text_35 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 62rpx;
}
.page .section_9 .section_11 .text-group_5 .text_36 {
  width: 92rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 12rpx;
}
.page .section_9 .section_12 {
  width: 184rpx;
  height: 28rpx;
  margin: 68rpx 0 42rpx 22rpx;
}
.page .section_9 .section_12 .text-wrapper_7 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_9 .section_12 .text-wrapper_7 .text_37 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_9 .section_12 .text-wrapper_7 .text_38 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_9 .text_39 {
  position: absolute;
  left: 24rpx;
  top: -2rpx;
  width: 80rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 44rpx;
}
.page .section_13 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  position: relative;
  margin: 22rpx 0 0 24rpx;
}
.page .section_13 .block_5 {
  width: 126rpx;
  height: 40rpx;
}
.page .section_13 .block_5 .group_2 {
  background-color: rgb(62, 200, 174);
  border-radius: 10px 0px 0px 10px;
  width: 126rpx;
  height: 40rpx;
}
.page .section_13 .text-wrapper_8 {
  width: 656rpx;
  height: 26rpx;
  margin: 16rpx 0 0 24rpx;
}
.page .section_13 .text-wrapper_8 .text_40 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_13 .text-wrapper_8 .text_41 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(62, 200, 174);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_13 .block_6 {
  width: 654rpx;
  height: 126rpx;
  margin: 52rpx 0 0 22rpx;
}
.page .section_13 .block_6 .image-text_5 {
  width: 446rpx;
  height: 126rpx;
}
.page .section_13 .block_6 .image-text_5 .image_5 {
  width: 126rpx;
  height: 126rpx;
}
.page .section_13 .block_6 .image-text_5 .text-group_6 {
  width: 296rpx;
  height: 116rpx;
}
.page .section_13 .block_6 .image-text_5 .text-group_6 .text_42 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 22rpx;
}
.page .section_13 .block_6 .image-text_5 .text-group_6 .text_43 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .section_13 .block_6 .image-text_5 .text-group_6 .text_44 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .section_13 .block_6 .image-text_5 .text-group_6 .text_45 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .section_13 .block_6 .text_46 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .section_13 .block_7 {
  width: 184rpx;
  height: 28rpx;
  margin: 68rpx 0 42rpx 22rpx;
}
.page .section_13 .block_7 .text-wrapper_9 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_13 .block_7 .text-wrapper_9 .text_47 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_13 .block_7 .text-wrapper_9 .text_48 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_13 .text_49 {
  position: absolute;
  left: 24rpx;
  top: -2rpx;
  width: 80rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 44rpx;
}
.page .section_14 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  position: relative;
  margin: 20rpx 0 310rpx 24rpx;
}
.page .section_14 .box_5 {
  width: 126rpx;
  height: 40rpx;
}
.page .section_14 .box_5 .section_15 {
  background-color: rgb(62, 200, 174);
  border-radius: 10px 0px 0px 10px;
  width: 126rpx;
  height: 40rpx;
}
.page .section_14 .text-wrapper_10 {
  width: 656rpx;
  height: 26rpx;
  margin: 16rpx 0 0 24rpx;
}
.page .section_14 .text-wrapper_10 .text_50 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_14 .text-wrapper_10 .text_51 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_14 .box_6 {
  width: 654rpx;
  height: 126rpx;
  margin: 52rpx 0 0 22rpx;
}
.page .section_14 .box_6 .image-text_6 {
  width: 446rpx;
  height: 126rpx;
}
.page .section_14 .box_6 .image-text_6 .image_6 {
  width: 126rpx;
  height: 126rpx;
}
.page .section_14 .box_6 .image-text_6 .text-group_7 {
  width: 296rpx;
  height: 116rpx;
}
.page .section_14 .box_6 .image-text_6 .text-group_7 .text_52 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 22rpx;
}
.page .section_14 .box_6 .image-text_6 .text-group_7 .text_53 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .section_14 .box_6 .image-text_6 .text-group_7 .text_54 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .section_14 .box_6 .image-text_6 .text-group_7 .text_55 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .section_14 .box_6 .text_56 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .section_14 .box_7 {
  width: 184rpx;
  height: 28rpx;
  margin: 68rpx 0 42rpx 22rpx;
}
.page .section_14 .box_7 .text-wrapper_11 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_14 .box_7 .text-wrapper_11 .text_57 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_14 .box_7 .text-wrapper_11 .text_58 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_14 .text_59 {
  position: absolute;
  left: 24rpx;
  top: -2rpx;
  width: 80rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 44rpx;
}
.page .section_16 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 398rpx;
  width: 702rpx;
  position: absolute;
  left: 24rpx;
  top: 382rpx;
}
.page .section_16 .group_3 {
  width: 126rpx;
  height: 40rpx;
}
.page .section_16 .group_3 .box_8 {
  background-color: rgb(62, 200, 174);
  border-radius: 10px 0px 0px 10px;
  width: 126rpx;
  height: 40rpx;
}
.page .section_16 .text-wrapper_12 {
  width: 656rpx;
  height: 26rpx;
  margin: 16rpx 0 0 24rpx;
}
.page .section_16 .text-wrapper_12 .text_60 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_16 .text-wrapper_12 .text_61 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_16 .group_4 {
  width: 654rpx;
  height: 126rpx;
  margin: 52rpx 0 0 22rpx;
}
.page .section_16 .group_4 .image-text_7 {
  width: 446rpx;
  height: 126rpx;
}
.page .section_16 .group_4 .image-text_7 .image_7 {
  width: 126rpx;
  height: 126rpx;
}
.page .section_16 .group_4 .image-text_7 .text-group_8 {
  width: 296rpx;
  height: 116rpx;
}
.page .section_16 .group_4 .image-text_7 .text-group_8 .text_62 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 22rpx;
}
.page .section_16 .group_4 .image-text_7 .text-group_8 .text_63 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: -28rpx;
}
.page .section_16 .group_4 .image-text_7 .text-group_8 .text_64 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .section_16 .group_4 .image-text_7 .text-group_8 .text_65 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .section_16 .group_4 .text_66 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .section_16 .group_5 {
  width: 660rpx;
  height: 50rpx;
  margin: 58rpx 0 30rpx 22rpx;
}
.page .section_16 .group_5 .text-wrapper_13 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 10rpx;
}
.page .section_16 .group_5 .text-wrapper_13 .text_67 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_16 .group_5 .text-wrapper_13 .text_68 {
  width: 184rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_16 .group_5 .text-wrapper_14 {
  background-color: rgb(11, 206, 148);
  border-radius: 4px;
  height: 50rpx;
  width: 136rpx;
}
.page .section_16 .group_5 .text-wrapper_14 .text_69 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 20rpx;
}
.page .section_16 .text_70 {
  position: absolute;
  left: 24rpx;
  top: -2rpx;
  width: 80rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 44rpx;
}
