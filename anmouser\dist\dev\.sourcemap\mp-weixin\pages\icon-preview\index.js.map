{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/icon-preview/index.vue?a039", "webpack:///./src/pages/icon-preview/index.vue?9d42", "webpack:///./src/pages/icon-preview/index.vue?6479", "webpack:///./src/pages/icon-preview/index.vue?f63d", "uni-app:///src/pages/icon-preview/index.vue", "webpack:///./src/pages/icon-preview/index.vue?bd2f", "webpack:///./src/pages/icon-preview/index.vue?c58f"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "selectedIcon", "copiedText", "iconTypes", "type", "desc", "category", "methods", "copyIconType", "_this", "navigator", "clipboard", "writeText", "setTimeout", "console", "log"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAiD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAF9BG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCkCtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,UAAA;MACAC,SAAA;MACA;MACA;QAAAC,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA;MAEA;MACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA;MAEA;MACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA;MAEA;MACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA;MAEA;MACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA;MAEA;MACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA;MAEA;MACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,QAAA;MAAA;IAEA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAJ,IAAA;MAAA,IAAAK,KAAA;MACA,KAAAR,YAAA,GAAAG,IAAA;MACA,KAAAF,UAAA,GAAAE,IAAA;;MAEA;MACA,WAAAM,SAAA,oBAAAA,SAAA,CAAAC,SAAA;QACAD,SAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAR,IAAA;MACA;;MAEA;MACAS,UAAA;QACAJ,KAAA,CAAAP,UAAA;QACAO,KAAA,CAAAR,YAAA;MACA;MAEAa,OAAA,CAAAC,GAAA,UAAAX,IAAA;IACA;EACA;AACA,E;;;;;;;;;;;AC7GA;AAAA;AAAA;AAAA;AAAi8B,CAAgB,26BAAG,EAAC,C;;;;;;;;;;ACAr9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/icon-preview/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/icon-preview/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=8f0b93a0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=8f0b93a0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8f0b93a0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/icon-preview/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=8f0b93a0&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"icon-preview-page\">\n    <view class=\"header\">\n      <text class=\"title\">uniapp 图标预览</text>\n      <text class=\"subtitle\">点击图标可复制 type 值</text>\n    </view>\n    \n    <view class=\"icon-grid\">\n      <view \n        v-for=\"iconType in iconTypes\" \n        :key=\"iconType.type\"\n        class=\"icon-item\"\n        @click=\"copyIconType(iconType.type)\"\n      >\n        <view class=\"icon-container\">\n          <uni-icons\n            :type=\"iconType.type\"\n            size=\"32\"\n            :color=\"selectedIcon === iconType.type ? '#007aff' : '#333'\"\n          ></uni-icons>\n        </view>\n        <text class=\"icon-name\">{{ iconType.type }}</text>\n        <text class=\"icon-desc\">{{ iconType.desc }}</text>\n        <text class=\"icon-category\">{{ iconType.category }}</text>\n      </view>\n    </view>\n    \n    <view v-if=\"copiedText\" class=\"toast\">\n      已复制: {{ copiedText }}\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      selectedIcon: '',\n      copiedText: '',\n      iconTypes: [\n        // 基础图标\n        { type: 'star', desc: '星星(空心)', category: '评分' },\n        { type: 'star-filled', desc: '星星(实心)', category: '评分' },\n        { type: 'heart', desc: '心形(空心)', category: '评分' },\n        { type: 'heart-filled', desc: '心形(实心)', category: '评分' },\n        { type: 'thumbsup', desc: '点赞(空心)', category: '评分' },\n        { type: 'thumbsup-filled', desc: '点赞(实心)', category: '评分' },\n\n        // 圆形图标\n        { type: 'circle', desc: '圆圈', category: '形状' },\n        { type: 'circle-filled', desc: '实心圆', category: '形状' },\n        { type: 'checkbox', desc: '复选框', category: '形状' },\n        { type: 'checkbox-filled', desc: '选中复选框', category: '形状' },\n\n        // 箭头图标\n        { type: 'arrowright', desc: '右箭头', category: '箭头' },\n        { type: 'arrowleft', desc: '左箭头', category: '箭头' },\n        { type: 'arrowup', desc: '上箭头', category: '箭头' },\n        { type: 'arrowdown', desc: '下箭头', category: '箭头' },\n\n        // 功能图标\n        { type: 'search', desc: '搜索', category: '功能' },\n        { type: 'plus', desc: '加号', category: '功能' },\n        { type: 'minus', desc: '减号', category: '功能' },\n        { type: 'close', desc: '关闭', category: '功能' },\n        { type: 'checkmarkempty', desc: '勾选(空)', category: '功能' },\n        { type: 'checkmarkfilled', desc: '勾选(实)', category: '功能' },\n\n        // 状态图标\n        { type: 'info', desc: '信息', category: '状态' },\n        { type: 'info-filled', desc: '信息(实心)', category: '状态' },\n        { type: 'warning', desc: '警告', category: '状态' },\n        { type: 'warning-filled', desc: '警告(实心)', category: '状态' },\n        { type: 'error', desc: '错误', category: '状态' },\n        { type: 'error-filled', desc: '错误(实心)', category: '状态' },\n\n        // 社交图标\n        { type: 'chat', desc: '聊天', category: '社交' },\n        { type: 'chat-filled', desc: '聊天(实心)', category: '社交' },\n        { type: 'email', desc: '邮件', category: '社交' },\n        { type: 'email-filled', desc: '邮件(实心)', category: '社交' },\n\n        // 媒体图标\n        { type: 'image', desc: '图片', category: '媒体' },\n        { type: 'image-filled', desc: '图片(实心)', category: '媒体' },\n        { type: 'videocam', desc: '摄像', category: '媒体' },\n        { type: 'videocam-filled', desc: '摄像(实心)', category: '媒体' }\n      ]\n    };\n  },\n  methods: {\n    copyIconType(type) {\n      this.selectedIcon = type;\n      this.copiedText = type;\n      \n      // 复制到剪贴板（H5环境）\n      if (typeof navigator !== 'undefined' && navigator.clipboard) {\n        navigator.clipboard.writeText(type);\n      }\n      \n      // 显示提示\n      setTimeout(() => {\n        this.copiedText = '';\n        this.selectedIcon = '';\n      }, 2000);\n      \n      console.log('图标类型:', type);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.icon-preview-page {\n  padding: 20rpx;\n  background-color: #f8f8f8;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n  \n  .title {\n    display: block;\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 10rpx;\n  }\n  \n  .subtitle {\n    display: block;\n    font-size: 24rpx;\n    color: #666;\n  }\n}\n\n.icon-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));\n  gap: 20rpx;\n}\n\n.icon-item {\n  background: white;\n  border-radius: 12rpx;\n  padding: 30rpx 20rpx;\n  text-align: center;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  \n  &:hover {\n    transform: translateY(-4rpx);\n    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n}\n\n.icon-container {\n  margin-bottom: 16rpx;\n  height: 64rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.icon-name {\n  display: block;\n  font-size: 24rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 8rpx;\n}\n\n.icon-desc {\n  display: block;\n  font-size: 20rpx;\n  color: #666;\n  margin-bottom: 4rpx;\n}\n\n.icon-category {\n  display: block;\n  font-size: 18rpx;\n  color: #999;\n  background: #f0f0f0;\n  padding: 2rpx 8rpx;\n  border-radius: 4rpx;\n}\n\n.toast {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 20rpx 40rpx;\n  border-radius: 8rpx;\n  font-size: 28rpx;\n  z-index: 9999;\n}\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=8f0b93a0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=8f0b93a0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332554912\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}