{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_hexiaodaifuwuxiangqing/index.vue?74f1", "webpack:///./src/pages/lanhu_hexiaodaifuwuxiangqing/index.vue?e9c3", "webpack:///./src/pages/lanhu_hexiaodaifuwuxiangqing/index.vue?6371", "webpack:///./src/pages/lanhu_hexiaodaifuwuxiangqing/index.vue?e7c2", "uni-app:///src/pages/lanhu_hexiaodaifuwuxiangqing/index.vue", "webpack:///./src/pages/lanhu_hexiaodaifuwuxiangqing/index.vue?750b", "webpack:///./src/pages/lanhu_hexiaodaifuwuxiangqing/index.vue?0b27"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAiE,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAF9CG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCiMtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;ACxMA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_hexiaodaifuwuxiangqing/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_hexiaodaifuwuxiangqing/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=a3603c5c&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_hexiaodaifuwuxiangqing/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=a3603c5c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"box_1 flex-row\">\n      <text class=\"text_1\">12:30</text>\n      <image\n        class=\"thumbnail_1\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n      />\n      <image\n        class=\"thumbnail_2\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n      />\n      <image\n        class=\"thumbnail_3\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n      />\n    </view>\n    <view class=\"box_2 flex-row\">\n      <image\n        class=\"thumbnail_4\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n      />\n      <text class=\"text_2\">下单</text>\n      <image\n        class=\"image_1\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n      />\n    </view>\n    <view class=\"box_3 flex-col\">\n      <view class=\"section_1 flex-row\">\n        <view class=\"image-text_1 flex-row justify-between\">\n          <image\n            class=\"label_1\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNG9bb7f9691971f53c5c493535a42a446e.png\"\n          />\n          <text class=\"text-group_1\">待服务</text>\n        </view>\n      </view>\n      <view class=\"section_2 flex-row\">\n        <text class=\"text_3\">服务还有</text>\n        <view class=\"text-wrapper_1 flex-col\">\n          <text class=\"text_4\">00</text>\n        </view>\n        <text class=\"text_5\">天</text>\n        <view class=\"text-wrapper_2 flex-col\">\n          <text class=\"text_6\">01</text>\n        </view>\n        <text class=\"text_7\">时</text>\n        <view class=\"text-wrapper_3 flex-col\">\n          <text class=\"text_8\">15</text>\n        </view>\n        <text class=\"text_9\">分</text>\n        <view class=\"text-wrapper_4 flex-col\">\n          <text class=\"text_10\">10</text>\n        </view>\n        <text class=\"text_11\">秒</text>\n        <text class=\"text_12\">结束</text>\n      </view>\n      <view class=\"section_3 flex-row\">\n        <view class=\"image-text_2 flex-row justify-between\">\n          <image\n            class=\"image_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNG1f76baa9c15ec3c2398a54922872dc3c.png\"\n          />\n          <view class=\"text-group_2 flex-col\">\n            <text class=\"text_13\">日式·尊享奢华</text>\n            <view class=\"group_1 flex-row justify-between\">\n              <view class=\"text-wrapper_5\">\n                <text class=\"text_14\">￥</text>\n                <text class=\"text_15\">598</text>\n              </view>\n              <text class=\"text_16\">/120分钟（含物料费￥5.00）</text>\n            </view>\n            <text class=\"text_17\">服务理疗师：王燕燕</text>\n          </view>\n        </view>\n        <image\n          class=\"thumbnail_5\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNGb5f10cb656d5180c5c8b0bd7fc109b79.png\"\n        />\n        <text class=\"text_18\">1</text>\n        <image\n          class=\"thumbnail_6\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNG473e3f399793be314056e27ec9640740.png\"\n        />\n      </view>\n    </view>\n    <view class=\"box_4 flex-col\">\n      <view class=\"box_5 flex-row justify-between\">\n        <text class=\"text_19\">核销码</text>\n        <view class=\"text-wrapper_6\">\n          <text class=\"text_20\">XKFM</text>\n          <text class=\"text_21\">G</text>\n          <text class=\"text_22\">1424</text>\n        </view>\n      </view>\n      <view class=\"text-group_3 flex-col justify-between\">\n        <text class=\"text_23\">商家核对验券即可消费</text>\n        <text class=\"text_24\">(注：核销码仅能核销一次)</text>\n      </view>\n    </view>\n    <view class=\"box_6 flex-col\">\n      <view class=\"text-wrapper_7 flex-row\">\n        <text class=\"text_25\">尊享spa奇遇店铺</text>\n      </view>\n      <view class=\"block_1 flex-row\">\n        <view class=\"image-text_3 flex-row justify-between\">\n          <view class=\"block_2 flex-col\"></view>\n          <text class=\"text-group_4\">营业执照：1</text>\n        </view>\n      </view>\n      <view class=\"block_3 flex-row\">\n        <view class=\"image-text_4 flex-row justify-between\">\n          <image\n            class=\"thumbnail_7\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png\"\n          />\n          <text class=\"text-group_5\">营业时间：00:00-23:59</text>\n        </view>\n      </view>\n      <view class=\"block_4 flex-row\">\n        <view class=\"image-text_5 flex-row justify-between\">\n          <image\n            class=\"thumbnail_8\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNG963de7e8d6afeddae849b9f675f92506.png\"\n          />\n          <text class=\"text-group_6\">重庆</text>\n        </view>\n        <view class=\"text-wrapper_8 flex-col\">\n          <text class=\"text_26\">复制</text>\n        </view>\n        <image\n          class=\"thumbnail_9\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNG9319b895c5948ef93acca9988f9639f5.png\"\n        />\n        <image\n          class=\"thumbnail_10\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_hexiaodaifuwuxiangqing/FigmaDDSSlicePNG7e049a0f4e9bae555dc8e414abf08cf6.png\"\n        />\n      </view>\n      <view class=\"block_5 flex-col\"></view>\n    </view>\n    <view class=\"text-wrapper_9 flex-row\">\n      <text class=\"text_27\">下单时间</text>\n      <text class=\"text_28\">2024-12-21&nbsp;&nbsp;12:12:45</text>\n    </view>\n    <view class=\"text-wrapper_10 flex-row\">\n      <text class=\"text_29\">服务时间</text>\n      <text class=\"text_30\">2024-12-21&nbsp;&nbsp;12:12-12:11</text>\n    </view>\n    <view class=\"text-wrapper_11 flex-row justify-between\">\n      <text class=\"text_31\">服务时长</text>\n      <text class=\"text_32\">80分钟</text>\n    </view>\n    <view class=\"text-wrapper_12 flex-row justify-between\">\n      <text class=\"text_33\">物料费</text>\n      <text class=\"text_34\">￥18</text>\n    </view>\n    <view class=\"text-wrapper_13 flex-row justify-between\">\n      <text class=\"text_35\">卡券</text>\n      <text class=\"text_36\">￥18</text>\n    </view>\n    <view class=\"text-wrapper_14 flex-row justify-between\">\n      <text class=\"text_37\">项目服务费用</text>\n      <text class=\"text_38\">￥298</text>\n    </view>\n    <view class=\"text-wrapper_15 flex-row justify-between\">\n      <text class=\"text_39\">支付方式</text>\n      <text class=\"text_40\">微信支付</text>\n    </view>\n    <view class=\"box_7 flex-row justify-between\">\n      <text class=\"text_41\">支付方式</text>\n      <view class=\"text-wrapper_16\">\n        <text class=\"text_42\">总计:</text>\n        <text class=\"text_43\">￥298</text>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332583782\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}