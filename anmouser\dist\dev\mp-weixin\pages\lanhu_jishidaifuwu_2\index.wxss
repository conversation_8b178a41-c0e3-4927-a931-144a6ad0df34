@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .group_1 {
  width: 750rpx;
  height: 688rpx;
}
.page .group_1 .block_1 {
  width: 750rpx;
  height: 282rpx;
}
.page .group_1 .block_1 .box_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .group_1 .block_1 .box_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .group_1 .block_1 .box_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .group_1 .block_1 .box_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .group_1 .block_1 .box_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .group_1 .block_1 .box_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .group_1 .block_1 .box_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .group_1 .block_1 .box_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .group_1 .block_1 .box_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .group_1 .block_1 .box_3 {
  background-color: rgb(255, 255, 255);
  height: 88rpx;
  margin-bottom: 22rpx;
  width: 750rpx;
  position: relative;
}
.page .group_1 .block_1 .box_3 .text-wrapper_1 {
  width: 646rpx;
  height: 28rpx;
  margin: 30rpx 0 0 52rpx;
}
.page .group_1 .block_1 .box_3 .text-wrapper_1 .text_3 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_1 .block_1 .box_3 .text-wrapper_1 .text_4 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 104rpx;
}
.page .group_1 .block_1 .box_3 .text-wrapper_1 .text_5 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 104rpx;
}
.page .group_1 .block_1 .box_3 .text-wrapper_1 .text_6 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 102rpx;
}
.page .group_1 .block_1 .box_3 .box_4 {
  position: absolute;
  left: 120rpx;
  top: 4rpx;
  width: 592rpx;
  height: 34rpx;
}
.page .group_1 .block_1 .box_3 .box_4 .text-wrapper_2 {
  background-color: rgb(246, 35, 35);
  border-radius: 50%;
  height: 30rpx;
  width: 30rpx;
}
.page .group_1 .block_1 .box_3 .box_4 .text-wrapper_2 .text_7 {
  width: 12rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 2rpx 0 0 8rpx;
}
.page .group_1 .block_1 .box_3 .box_4 .text-wrapper_3 {
  background-color: rgb(246, 35, 35);
  border-radius: 50%;
  height: 30rpx;
  width: 30rpx;
  margin: 4rpx 0 0 352rpx;
}
.page .group_1 .block_1 .box_3 .box_4 .text-wrapper_3 .text_8 {
  width: 12rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 2rpx 0 0 8rpx;
}
.page .group_1 .block_1 .box_3 .box_4 .text-wrapper_4 {
  background-color: rgb(246, 35, 35);
  border-radius: 50%;
  height: 30rpx;
  width: 30rpx;
  margin: 2rpx 0 0 150rpx;
}
.page .group_1 .block_1 .box_3 .box_4 .text-wrapper_4 .text_9 {
  width: 12rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 2rpx 0 0 8rpx;
}
.page .group_1 .block_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 408rpx;
  width: 702rpx;
  margin: 280rpx 24rpx 0 -726rpx;
}
.page .group_1 .block_2 .text-wrapper_5 {
  width: 656rpx;
  height: 26rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .group_1 .block_2 .text-wrapper_5 .text_10 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_1 .block_2 .text-wrapper_5 .text_11 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .group_1 .block_2 .group_2 {
  width: 654rpx;
  height: 158rpx;
  margin: 72rpx 0 0 22rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 {
  width: 472rpx;
  height: 158rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 .image_2 {
  width: 156rpx;
  height: 156rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 .text-group_1 {
  width: 300rpx;
  height: 158rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 .text-group_1 .text_12 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 .text-group_1 .text_13 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: -28rpx 0 0 4rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 .text-group_1 .text_14 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 4rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 .text-group_1 .text_15 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 4rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 .text-group_1 .text-wrapper_6 {
  width: 178rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 4rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 .text-group_1 .text-wrapper_6 .text_16 {
  width: 178rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 .text-group_1 .text-wrapper_6 .text_17 {
  width: 178rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_1 .block_2 .group_2 .image-text_1 .text-group_1 .text-wrapper_6 .text_18 {
  width: 178rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_1 .block_2 .group_2 .text_19 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .group_1 .block_2 .group_3 {
  width: 660rpx;
  height: 50rpx;
  margin: 34rpx 0 32rpx 22rpx;
}
.page .group_1 .block_2 .group_3 .text-wrapper_7 {
  width: 158rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .group_1 .block_2 .group_3 .text-wrapper_7 .text_20 {
  width: 158rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_1 .block_2 .group_3 .text-wrapper_7 .text_21 {
  width: 158rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_1 .block_2 .group_3 .text-wrapper_8 {
  background-color: rgb(11, 206, 148);
  border-radius: 4px;
  height: 50rpx;
  width: 136rpx;
}
.page .group_1 .block_2 .group_3 .text-wrapper_8 .text_22 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 20rpx;
}
.page .group_4 {
  background-color: rgba(0, 0, 0, 0.3);
  position: absolute;
  left: 2rpx;
  top: 152rpx;
  width: 748rpx;
  height: 1472rpx;
}
.page .group_4 .box_5 {
  background-image: -webkit-linear-gradient(top, rgb(255, 255, 255) 0, rgb(255, 255, 255) 50.961536%);
  background-image: linear-gradient(180deg, rgb(255, 255, 255) 0, rgb(255, 255, 255) 50.961536%);
  border-radius: 10px 10px 0px 0px;
  width: 560rpx;
  height: 472rpx;
  margin: 262rpx 0 0 104rpx;
}
.page .group_4 .box_5 .text-group_2 {
  width: 480rpx;
  height: 98rpx;
  margin: 44rpx 0 0 40rpx;
}
.page .group_4 .box_5 .text-group_2 .text_23 {
  width: 480rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 34rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.page .group_4 .box_5 .text-group_2 .text_24 {
  width: 480rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 16rpx;
}
.page .group_4 .box_5 .text-wrapper_9 {
  background-color: rgb(245, 245, 245);
  border-radius: 5px;
  height: 252rpx;
  width: 456rpx;
  margin: 36rpx 0 42rpx 54rpx;
}
.page .group_4 .box_5 .text-wrapper_9 .text_25 {
  width: 70rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 194rpx 0 0 354rpx;
}
.page .group_4 .box_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 0px 10px 10px;
  width: 560rpx;
  height: 96rpx;
  margin: 0 0 642rpx 104rpx;
}
.page .group_4 .box_6 .group_5 {
  background-color: rgba(22, 24, 35, 0.12);
  width: 560rpx;
  height: 2rpx;
}
.page .group_4 .box_6 .group_6 {
  width: 512rpx;
  height: 96rpx;
  margin: 0 0 2rpx 24rpx;
}
.page .group_4 .box_6 .group_6 .text_26 {
  width: 230rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgba(22, 24, 35, 0.75);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 30rpx;
  margin-top: 26rpx;
}
.page .group_4 .box_6 .group_6 .section_1 {
  background-color: rgba(22, 24, 35, 0.12);
  width: 2rpx;
  height: 96rpx;
  margin-left: 26rpx;
}
.page .group_4 .box_6 .group_6 .text_27 {
  width: 232rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 30rpx;
  margin: 26rpx 0 0 22rpx;
}
