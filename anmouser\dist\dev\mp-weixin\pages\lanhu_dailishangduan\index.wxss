@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(248, 248, 248);
  position: relative;
  width: 750rpx;
  height: 2840rpx;
  overflow: hidden;
}
.page .group_1 {
  background-color: rgb(62, 200, 174);
  height: 398rpx;
  width: 750rpx;
}
.page .group_1 .section_1 {
  width: 688rpx;
  height: 38rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .group_1 .section_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
}
.page .group_1 .section_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin-left: 502rpx;
}
.page .group_1 .section_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin-left: 6rpx;
}
.page .group_1 .section_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin-left: 6rpx;
}
.page .group_1 .section_2 {
  width: 702rpx;
  height: 64rpx;
  margin: 34rpx 0 248rpx 36rpx;
}
.page .group_1 .section_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin-top: 16rpx;
}
.page .group_1 .section_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 10rpx 0 0 22rpx;
}
.page .group_1 .section_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin-left: 360rpx;
}
.page .group_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 446rpx;
  width: 702rpx;
  margin: 1158rpx 0 0 24rpx;
}
.page .group_2 .text-wrapper_1 {
  width: 112rpx;
  height: 28rpx;
  margin: 38rpx 0 0 30rpx;
}
.page .group_2 .text-wrapper_1 .text_3 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_2 .box_1 {
  width: 618rpx;
  height: 130rpx;
  margin: 48rpx 0 0 42rpx;
}
.page .group_2 .box_1 .image-text_1 {
  width: 90rpx;
  height: 130rpx;
}
.page .group_2 .box_1 .image-text_1 .group_3 {
  height: 90rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC0AAAAtCAYAAAA6GuKaAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAkBSURBVHgBtVlNbFdFEJ95/7+UIFaMYo0mUsSPRA1yIIQQjYKi8QMhmnBQMBAPRjkQVGI8GOqBGEMiJSQmHrRUEzUepHCpn5ioQS5AoygHUCDB8KGGSkAo7dtxd3Zmdt/fYv8tsO3re31v3+7szG9+M7MPYZi2rIsmlbVyEWCxEAhmOKJ2fkDyS4REsS+f4/1wTele6pOeywCIBI5Q+5I8CvccQJ9/cBARe7auHNc9nHzYeGNJ19Cioo5dDvBKmQ3JsaSVCaPwqPdRBWPJyQTnR/liTdDqgoLQqO9qHxYesGPri1Xhi/yfZz4oO7GOm33/STyAM2GiJoLAlclkUfyc+E9FIBbYpECy7nqL5IjzaHMyiz+3l85temTdmY5hNb2ke7ADi2JNrglWrB+QFa3mjLKimTrTFpshmCUqDaJyRVCiKFj2jgmo40G+RtaBWibc3dC7evwqE3rp+0PLvBa7eHA2PcWJ3TADxQkR1KTh3IBPUFOLghFEy/lwLhNUF96wKLGgHxvZhH6aJ754paVHhHYH/BTtQJnZ+AKhogHKsK3aA5T+QckOIWE+CQL5YhQW0KgQ0z5fZgrLEHZi4My4m+pBy0FgRQMw5oKZM8dRSADlTGACRLnV3qEfGsMkfEfNVoVDg0BF4zCMwPH3qnEtZxfWvS8uZPi47GWqenq4dlFmuSeW1T4uwsS0yLBDBUhGffI6RfZh2OgSEgORemsjZcrVorp/0A622uQwqR9rLqqPZ1JYKMPFbrdfX8Cs9gLuuAGhbSLChJa4wNMDBAf+9McfADt+LWHPYXH+nEZcglmYz+m6dcFsTZR+xQx8ursk41ZdS7Ys7i/enYIC+hOrC++7DWHxzBpMvgKaasdOEny0o4SvfxF+FBOyZarWzqEIOTHgU92lq6xUO0KKUmGVcV3y3N9ray1g9UM1aL8axtSC8K9+MgjH/hagZBM7cS7n2MbiuKiWxaLCGLYysAFk1aoVNuvcWwtY9+TYBQ6trRVh49LLYPa0ms5NCX4hCot2yc6oTlEErSUFo4U0styBzWLXs6YWtGJeLWD2gtvlLQivLarD/XfWoiMqRCjRHhvbSYR0UaEcxoksIKBFM2EHdbRgpskTCVfcWyBc5Pbc3Bq0XRnmdUov5nsaMJIugTT3YI/VXEAWknDN9x2+/nj9omi4sU0cHzR+GaizKSEEzQYtK54x5jJYVwKPOQaGJaWgYPkOwtzbihEZYmufg3e/LdnEE1oK+vj5etNWmdaGMN/D5PMfh0S7puaAEyRNLAM8DMPxDmYRDDTHCM8XzyxGnPj0WQKlrdNn3ahhtOTumlGuZw7Sa4qkGx3RY6aI+QVUIpLGEfmDt1+PTfOwWqmy+CZbwPX0GwvWIQpkxcliTs906zHtxAbOtB3oxiFoQPEnzxijmlzTlLG0ObfU4hiaPjQmV/6oa3iW6KEhNiN7wqlXN2fppGXJ9oapjEZq06eo5TVBjoGHJI0Jf+op85LxRWDO5aW1XzOKuaMHj1nT102SnENkcdXclldQpBhPmqFp8QGK7cubpTmyLMr4fbQt0B8r0BgkKl0qHPaxuvgeJk0TWi1IDkejsWRGnhAfXjdAkraCFgnB3BPGA21e1YIjjKNxGi0FFp8sQMsZkFzIqC+F09MD0HwzXHOuYnk2uBTtTv1z/tdPnTVohv6WPnBQFBwXJFmbFmFAWYIk5f/xk02qO6c6Edw5slTG4PM/bf9Rl953UeEyhMoGdb0Gq5YjNSbyANjzO9HUyTiiN85musrSzPh+5b0wnsctnl9oEmFj+QNcGAOmGAgB06SRLy6EKwTQ5JV7/bDf4YIZI3P1tGvRHzW4kPb93lLXyxxt2Z9SM4QsL0UwrZZTASsg33PYNQ+RC2hH+wl2HyhZYEvuETRNNt63IgBkU0awXCGskG19uL2ES926vhnUTC5VKs44H5X7CiVxyvAckx4kAvNY+OrnEn47fum0ffSEg892l5DCtg/hDjLpwbZTCkqJkTCdBhgNEGiLWrtlkKvri90Cza3cdI4qNSpxEYCWYWgcpwAPFZ9XE7OpqG6AROrxOOIx9862iw+TNz4dhCN/OSliLThLASBKVVcLlEeUkiMFsrOiEtJNOX35U0mnzhC+9Gida7wLaUHDG3sH6bu9QxiacHra6TF/o/AYNB1KuQfJY/7PqmDK82M5cPs+By+8dy6W/2Ns+444ePbtAejdNYSS9Nsz42hOnGSfJSsKcMH6AarkqyA5NVGqGrjgjNiO276CN9/hAV8iLb2nLtnZyC3QWte2QejdXcreBk+hsGRHxLgZlO15WMhggfCxtwYcZBwnmw9ZwhZox1mebXB3qWIO51BxzLm1Bjdfh1zvcbYGEQJB0L4DzgeOIc/DZCEZdAMy0nC251cVVO7bpmjI8g75G1MitWSb4S4Pv5iNoD4Q/NeTqAtbJ16og84LVmZbvDFht6CQLAdgz5Jp06ZMCiKQFxEqOGJfUWDRZ5sxBhskrcTTwBiLYiJNBYVkQuAnlQRSRhSZB22LF9VrEpWmnCLxHEnuoayhWxsytpf6YFG6oS1qdhdnRs321C0Ti1Q+J6SRZUFReboNDBIQjAXy7VtUeNnBt1DIww4g3dGVufwGRU8xrhjf42/1U3Qw2yLL9kxAslYTVE1b8QPQHJwMHjHhyb4WGI4hJ2CymCBaDr2Yo50g0dl++MGd6yd2Fz2rsN8beHn89KDz6ypNwYpPGVsnToSf6Dyuzi8Ts+hm2rdzhlNITsgajvQXKijBqVjJj9gR/uF8s/fllh7/sNNKIyZOETLiGAVnmWYUnwCUW0GMUBHQLMIeLiwQEZBBBtJYaPAK5hKLdOxc39qtK7P24JsDa3wS2+GcekplzzpLXho+IIEKR5BtUmUfM2ONARmcWCaXmIKvUbGDkDufF6dz14bWVSrnfyLC/LWnlgHW1vgXpxgkNKyDEQzmmjRBhG8h0xxUPhrlBEI2v3GyOJ1+W/Q//b7f8p2drT25jOcNY/O88IX/iOQdot2D6S5jjkz7YJqAhpKIsBG7tlio+oDh3pFG30Ne9j4/Qk9J5Za+zqv6G2X7F1Ga9y6+MuEEAAAAAElFTkSuQmCC) 100% no-repeat;
  background-size: 100% 100%;
  width: 90rpx;
}
.page .group_2 .box_1 .image-text_1 .group_3 .text-wrapper_2 {
  background-color: rgb(238, 12, 12);
  border-radius: 50%;
  height: 36rpx;
  border: 1px solid rgb(255, 255, 255);
  width: 36rpx;
  margin: -16rpx 0 0 64rpx;
}
.page .group_2 .box_1 .image-text_1 .group_3 .text-wrapper_2 .text_4 {
  width: 16rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 8rpx 0 0 10rpx;
}
.page .group_2 .box_1 .image-text_1 .text-group_1 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 16rpx 0 0 10rpx;
}
.page .group_2 .box_1 .box_2 {
  width: 90rpx;
  height: 130rpx;
  margin-left: 86rpx;
}
.page .group_2 .box_1 .box_2 .image-wrapper_1 {
  background-image: -webkit-linear-gradient(307deg, rgb(83, 230, 176) 0, rgb(12, 189, 122) 100%);
  background-image: linear-gradient(143deg, rgb(83, 230, 176) 0, rgb(12, 189, 122) 100%);
  border-radius: 10px;
  height: 90rpx;
  width: 90rpx;
}
.page .group_2 .box_1 .box_2 .image-wrapper_1 .label_1 {
  width: 50rpx;
  height: 50rpx;
  margin: 20rpx 0 0 20rpx;
}
.page .group_2 .box_1 .box_2 .text_5 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 16rpx 0 0 10rpx;
}
.page .group_2 .box_1 .image-text_2 {
  width: 90rpx;
  height: 130rpx;
  margin-left: 86rpx;
}
.page .group_2 .box_1 .image-text_2 .label_2 {
  width: 90rpx;
  height: 90rpx;
}
.page .group_2 .box_1 .image-text_2 .text-group_2 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 16rpx 0 0 10rpx;
}
.page .group_2 .box_1 .box_3 {
  width: 90rpx;
  height: 130rpx;
  margin-left: 86rpx;
}
.page .group_2 .box_1 .box_3 .image-wrapper_2 {
  background-image: -webkit-linear-gradient(308deg, rgb(93, 160, 254) 0, rgb(38, 87, 238) 100%);
  background-image: linear-gradient(142deg, rgb(93, 160, 254) 0, rgb(38, 87, 238) 100%);
  border-radius: 10px;
  height: 90rpx;
  width: 90rpx;
}
.page .group_2 .box_1 .box_3 .image-wrapper_2 .label_3 {
  width: 48rpx;
  height: 48rpx;
  margin: 20rpx 0 0 22rpx;
}
.page .group_2 .box_1 .box_3 .text_6 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 16rpx 0 0 10rpx;
}
.page .group_2 .box_4 {
  width: 266rpx;
  height: 90rpx;
  margin: 32rpx 0 0 42rpx;
}
.page .group_2 .box_4 .image-wrapper_3 {
  background-image: -webkit-linear-gradient(311deg, rgb(150, 170, 255) 0, rgb(101, 130, 252) 100%);
  background-image: linear-gradient(139deg, rgb(150, 170, 255) 0, rgb(101, 130, 252) 100%);
  border-radius: 10px;
  height: 90rpx;
  width: 90rpx;
}
.page .group_2 .box_4 .image-wrapper_3 .label_4 {
  width: 54rpx;
  height: 54rpx;
  margin: 18rpx 0 0 18rpx;
}
.page .group_2 .box_4 .image-wrapper_4 {
  background-image: -webkit-linear-gradient(307deg, rgb(255, 151, 139) 0, rgb(231, 96, 81) 100%);
  background-image: linear-gradient(143deg, rgb(255, 151, 139) 0, rgb(231, 96, 81) 100%);
  border-radius: 10px;
  height: 90rpx;
  width: 90rpx;
}
.page .group_2 .box_4 .image-wrapper_4 .label_5 {
  width: 60rpx;
  height: 60rpx;
  margin: 14rpx 0 0 16rpx;
}
.page .group_2 .text-wrapper_3 {
  width: 248rpx;
  height: 24rpx;
  margin: 16rpx 0 40rpx 52rpx;
}
.page .group_2 .text-wrapper_3 .text_7 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_2 .text-wrapper_3 .text_8 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 {
  position: relative;
  width: 750rpx;
  height: 840rpx;
  margin-bottom: 2rpx;
}
.page .group_4 .box_5 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 274rpx;
  width: 702rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .group_4 .box_5 .text-wrapper_4 {
  width: 112rpx;
  height: 28rpx;
  margin: 32rpx 0 0 30rpx;
}
.page .group_4 .box_5 .text-wrapper_4 .text_9 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .box_5 .box_6 {
  width: 618rpx;
  height: 130rpx;
  margin: 54rpx 0 30rpx 42rpx;
}
.page .group_4 .box_5 .box_6 .image-text_3 {
  width: 90rpx;
  height: 130rpx;
}
.page .group_4 .box_5 .box_6 .image-text_3 .label_6 {
  width: 90rpx;
  height: 90rpx;
}
.page .group_4 .box_5 .box_6 .image-text_3 .text-group_3 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 16rpx 0 0 10rpx;
}
.page .group_4 .box_5 .box_6 .box_7 {
  width: 90rpx;
  height: 130rpx;
  margin-left: 86rpx;
}
.page .group_4 .box_5 .box_6 .box_7 .image-wrapper_5 {
  background-image: -webkit-linear-gradient(307deg, rgb(83, 230, 176) 0, rgb(12, 189, 122) 100%);
  background-image: linear-gradient(143deg, rgb(83, 230, 176) 0, rgb(12, 189, 122) 100%);
  border-radius: 10px;
  height: 90rpx;
  width: 90rpx;
}
.page .group_4 .box_5 .box_6 .box_7 .image-wrapper_5 .label_7 {
  width: 50rpx;
  height: 50rpx;
  margin: 20rpx 0 0 20rpx;
}
.page .group_4 .box_5 .box_6 .box_7 .text_10 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 16rpx 0 0 10rpx;
}
.page .group_4 .box_5 .box_6 .image-text_4 {
  width: 90rpx;
  height: 130rpx;
  margin-left: 86rpx;
}
.page .group_4 .box_5 .box_6 .image-text_4 .label_8 {
  width: 90rpx;
  height: 90rpx;
}
.page .group_4 .box_5 .box_6 .image-text_4 .text-group_4 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 16rpx 0 0 10rpx;
}
.page .group_4 .box_5 .box_6 .box_8 {
  width: 90rpx;
  height: 130rpx;
  margin-left: 86rpx;
}
.page .group_4 .box_5 .box_6 .box_8 .image-wrapper_6 {
  background-image: -webkit-linear-gradient(308deg, rgb(93, 160, 254) 0, rgb(38, 87, 238) 100%);
  background-image: linear-gradient(142deg, rgb(93, 160, 254) 0, rgb(38, 87, 238) 100%);
  border-radius: 10px;
  height: 90rpx;
  width: 90rpx;
}
.page .group_4 .box_5 .box_6 .box_8 .image-wrapper_6 .label_9 {
  width: 48rpx;
  height: 48rpx;
  margin: 20rpx 0 0 22rpx;
}
.page .group_4 .box_5 .box_6 .box_8 .text_11 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 16rpx 0 0 10rpx;
}
.page .group_4 .list_1 {
  width: 702rpx;
  height: 486rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
  margin: 20rpx 0 40rpx 24rpx;
}
.page .group_4 .list_1 .list-items_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 232rpx;
  margin-bottom: 22rpx;
  width: 702rpx;
  position: relative;
}
.page .group_4 .list_1 .list-items_1 .section_3 {
  width: 660rpx;
  height: 44rpx;
  margin: 32rpx 0 0 30rpx;
}
.page .group_4 .list_1 .list-items_1 .section_3 .text_12 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .list_1 .list-items_1 .section_3 .image-text_5 {
  width: 88rpx;
  height: 44rpx;
}
.page .group_4 .list_1 .list-items_1 .section_3 .image-text_5 .text-group_5 {
  width: 56rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .list_1 .list-items_1 .section_3 .image-text_5 .thumbnail_5 {
  width: 12rpx;
  height: 20rpx;
  margin: 0 12rpx 0 8rpx;
}
.page .group_4 .list_1 .list-items_1 .section_3 .image-text_5 .thumbnail_6 {
  width: 12rpx;
  height: 22rpx;
  margin: 22rpx 0 0 20rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 {
  width: 606rpx;
  height: 98rpx;
  margin: 18rpx 0 40rpx 48rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .group_5 {
  width: 82rpx;
  height: 98rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .group_5 .box_9 {
  width: 54rpx;
  height: 54rpx;
  margin-left: 28rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .group_5 .box_9 .text_13 {
  width: 22rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 18rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .group_5 .box_9 .text-wrapper_5 {
  background-color: rgb(238, 12, 12);
  border-radius: 50%;
  height: 36rpx;
  border: 1px solid rgb(255, 255, 255);
  margin-left: -4rpx;
  width: 36rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .group_5 .box_9 .text-wrapper_5 .text_14 {
  width: 16rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 8rpx 0 0 10rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .group_5 .text_15 {
  width: 78rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 18rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .text-group_6 {
  width: 104rpx;
  height: 80rpx;
  margin: 18rpx 0 0 82rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .text-group_6 .text_16 {
  width: 22rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 40rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .text-group_6 .text_17 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 18rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .group_6 {
  position: relative;
  width: 32rpx;
  height: 72rpx;
  margin: 18rpx 0 0 108rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .group_6 .box_10 {
  background-color: rgb(255, 255, 255);
  width: 32rpx;
  height: 52rpx;
  margin-top: 20rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .group_6 .text_18 {
  position: absolute;
  left: 4rpx;
  top: 0;
  width: 22rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .text-group_7 {
  width: 78rpx;
  height: 80rpx;
  margin: 18rpx 0 0 120rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .text-group_7 .text_19 {
  width: 22rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 28rpx;
}
.page .group_4 .list_1 .list-items_1 .section_4 .text-group_7 .text_20 {
  width: 78rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 18rpx;
}
.page .group_4 .list_1 .list-items_1 .text_21 {
  position: absolute;
  left: 388rpx;
  top: 166rpx;
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_4 .image_2 {
  position: absolute;
  left: -790rpx;
  top: -62rpx;
  width: 336rpx;
  height: 336rpx;
}
.page .group_7 {
  position: absolute;
  left: 0;
  top: 398rpx;
  width: 750rpx;
  height: 1160rpx;
}
.page .group_7 .box_11 {
  width: 700rpx;
  height: 168rpx;
  margin: 690rpx 0 0 24rpx;
}
.page .group_7 .box_11 .group_8 {
  background-image: -webkit-linear-gradient(314deg, rgb(253, 114, 118) 0, rgb(246, 156, 159) 100%);
  background-image: linear-gradient(136deg, rgb(253, 114, 118) 0, rgb(246, 156, 159) 100%);
  border-radius: 10px;
  position: relative;
  width: 340rpx;
  height: 168rpx;
}
.page .group_7 .box_11 .group_8 .text_22 {
  width: 160rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 20rpx 0 0 28rpx;
}
.page .group_7 .box_11 .group_8 .text_23 {
  width: 180rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.8);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 18rpx 0 0 28rpx;
}
.page .group_7 .box_11 .group_8 .text-wrapper_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 100px;
  height: 40rpx;
  width: 114rpx;
  margin: 20rpx 0 24rpx 28rpx;
}
.page .group_7 .box_11 .group_8 .text-wrapper_6 .text_24 {
  width: 66rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(253, 115, 119);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 10rpx 0 0 20rpx;
}
.page .group_7 .box_11 .group_8 .image-wrapper_7 {
  background-image: -webkit-linear-gradient(314deg, rgb(110, 141, 243) 0, rgb(134, 161, 245) 100%);
  background-image: linear-gradient(136deg, rgb(110, 141, 243) 0, rgb(134, 161, 245) 100%);
  border-radius: 10px;
  height: 168rpx;
  width: 248rpx;
  position: absolute;
  left: 92rpx;
  top: 0;
}
.page .group_7 .box_11 .group_8 .image-wrapper_7 .image_3 {
  width: 248rpx;
  height: 168rpx;
}
.page .group_7 .box_11 .group_9 {
  background-image: -webkit-linear-gradient(314deg, rgb(100, 203, 148) 0, rgb(116, 221, 167) 100%);
  background-image: linear-gradient(136deg, rgb(100, 203, 148) 0, rgb(116, 221, 167) 100%);
  border-radius: 10px;
  position: relative;
  width: 340rpx;
  height: 168rpx;
}
.page .group_7 .box_11 .group_9 .text_25 {
  width: 192rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 20rpx 0 0 30rpx;
}
.page .group_7 .box_11 .group_9 .text_26 {
  width: 220rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.8);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 18rpx 0 0 30rpx;
}
.page .group_7 .box_11 .group_9 .text-wrapper_7 {
  background-color: rgb(255, 255, 255);
  border-radius: 100px;
  height: 40rpx;
  width: 114rpx;
  margin: 20rpx 0 24rpx 30rpx;
}
.page .group_7 .box_11 .group_9 .text-wrapper_7 .text_27 {
  width: 66rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(101, 204, 149);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 10rpx 0 0 20rpx;
}
.page .group_7 .box_11 .group_9 .image-wrapper_8 {
  background-image: -webkit-linear-gradient(314deg, rgb(110, 141, 243) 0, rgb(134, 161, 245) 100%);
  background-image: linear-gradient(136deg, rgb(110, 141, 243) 0, rgb(134, 161, 245) 100%);
  border-radius: 10px;
  height: 168rpx;
  width: 248rpx;
  position: absolute;
  left: 92rpx;
  top: 0;
}
.page .group_7 .box_11 .group_9 .image-wrapper_8 .image_4 {
  width: 248rpx;
  height: 168rpx;
}
.page .group_7 .box_12 {
  width: 700rpx;
  height: 120rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .group_7 .box_12 .group_10 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 120rpx;
  border: 2px solid rgb(255, 255, 255);
  width: 338rpx;
}
.page .group_7 .box_12 .group_10 .block_1 {
  width: 128rpx;
  height: 40rpx;
}
.page .group_7 .box_12 .group_10 .block_1 .text-wrapper_8 {
  background-color: rgb(27, 199, 101);
  border-radius: 10px 0px 0px 10px;
  height: 40rpx;
  width: 128rpx;
}
.page .group_7 .box_12 .group_10 .block_1 .text-wrapper_8 .text_28 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 0 12rpx;
}
.page .group_7 .box_12 .group_10 .block_2 {
  width: 240rpx;
  height: 26rpx;
  margin: 26rpx 0 28rpx 22rpx;
}
.page .group_7 .box_12 .group_10 .block_2 .text_29 {
  width: 218rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_7 .box_12 .group_10 .block_2 .box_13 {
  background-color: rgb(238, 12, 12);
  border-radius: 50%;
  width: 14rpx;
  height: 14rpx;
  margin-top: 6rpx;
}
.page .group_7 .box_12 .group_11 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 120rpx;
  border: 2px solid rgb(255, 255, 255);
  width: 338rpx;
}
.page .group_7 .box_12 .group_11 .box_14 {
  width: 128rpx;
  height: 40rpx;
}
.page .group_7 .box_12 .group_11 .box_14 .text-wrapper_9 {
  background-color: rgb(254, 162, 44);
  border-radius: 10px 0px 0px 10px;
  height: 40rpx;
  width: 128rpx;
}
.page .group_7 .box_12 .group_11 .box_14 .text-wrapper_9 .text_30 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 0 12rpx;
}
.page .group_7 .box_12 .group_11 .box_15 {
  width: 238rpx;
  height: 26rpx;
  margin: 26rpx 0 28rpx 26rpx;
}
.page .group_7 .box_12 .group_11 .box_15 .text_31 {
  width: 218rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_7 .box_12 .group_11 .box_15 .section_5 {
  background-color: rgb(238, 12, 12);
  border-radius: 50%;
  width: 14rpx;
  height: 14rpx;
  margin-top: 6rpx;
}
.page .group_7 .box_16 {
  width: 702rpx;
  height: 120rpx;
  margin: 20rpx 0 22rpx 24rpx;
}
.page .group_7 .box_16 .box_17 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 338rpx;
  height: 120rpx;
  border: 2px solid rgb(255, 255, 255);
}
.page .group_7 .box_16 .box_17 .text-wrapper_10 {
  background-color: rgb(230, 49, 36);
  border-radius: 10px 0px 0px 10px;
  height: 40rpx;
  width: 128rpx;
}
.page .group_7 .box_16 .box_17 .text-wrapper_10 .text_32 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 0 12rpx;
}
.page .group_7 .box_16 .box_17 .text_33 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 26rpx 0 28rpx 22rpx;
}
.page .group_7 .box_16 .box_18 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 120rpx;
  border: 2px solid rgb(255, 255, 255);
  width: 338rpx;
}
.page .group_7 .box_16 .box_18 .group_12 {
  width: 128rpx;
  height: 40rpx;
}
.page .group_7 .box_16 .box_18 .group_12 .text-wrapper_11 {
  background-color: rgb(44, 120, 223);
  border-radius: 10px 0px 0px 10px;
  height: 40rpx;
  width: 128rpx;
}
.page .group_7 .box_16 .box_18 .group_12 .text-wrapper_11 .text_34 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 0 12rpx;
}
.page .group_7 .box_16 .box_18 .group_13 {
  width: 208rpx;
  height: 26rpx;
  margin: 26rpx 0 28rpx 22rpx;
}
.page .group_7 .box_16 .box_18 .group_13 .text_35 {
  width: 182rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_7 .box_16 .box_18 .group_13 .box_19 {
  background-color: rgb(238, 12, 12);
  border-radius: 50%;
  width: 14rpx;
  height: 14rpx;
  margin-top: 6rpx;
}
.page .group_7 .box_20 {
  position: absolute;
  left: 200rpx;
  top: -130rpx;
  width: 628rpx;
  height: 646rpx;
  background: url(/static/lanhu_dailishangduan/FigmaDDSSlicePNG6e1addf9cb5d91d4eb6eaf404e14b442.png) 0rpx -570rpx no-repeat;
  background-size: 858rpx 866rpx;
}
.page .group_7 .box_20 .section_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  position: absolute;
  left: -176rpx;
  top: 466rpx;
  width: 702rpx;
  height: 142rpx;
}
.page .group_7 .box_20 .section_6 .image-text_6 {
  width: 104rpx;
  height: 84rpx;
  margin: 30rpx 0 0 212rpx;
}
.page .group_7 .box_20 .section_6 .image-text_6 .label_10 {
  width: 44rpx;
  height: 44rpx;
  margin-left: 30rpx;
}
.page .group_7 .box_20 .section_6 .image-text_6 .text-group_8 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .group_7 .box_20 .section_6 .image-text_7 {
  width: 104rpx;
  height: 84rpx;
  margin: 30rpx 0 0 72rpx;
}
.page .group_7 .box_20 .section_6 .image-text_7 .label_11 {
  width: 44rpx;
  height: 44rpx;
  margin-left: 30rpx;
}
.page .group_7 .box_20 .section_6 .image-text_7 .text-group_9 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .group_7 .box_20 .section_6 .image-text_8 {
  width: 104rpx;
  height: 82rpx;
  margin: 32rpx 34rpx 0 72rpx;
}
.page .group_7 .box_20 .section_6 .image-text_8 .thumbnail_7 {
  width: 40rpx;
  height: 40rpx;
  margin-left: 32rpx;
}
.page .group_7 .box_20 .section_6 .image-text_8 .text-group_10 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 16rpx;
}
.page .group_7 .box_20 .section_6 .image-text_9 {
  position: absolute;
  left: 36rpx;
  top: 30rpx;
  width: 104rpx;
  height: 84rpx;
}
.page .group_7 .box_20 .section_6 .image-text_9 .label_12 {
  width: 44rpx;
  height: 44rpx;
  margin-left: 30rpx;
}
.page .group_7 .box_20 .section_6 .image-text_9 .text-group_11 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .group_7 .box_20 .section_6 .image-wrapper_9 {
  position: absolute;
  left: 66rpx;
  top: 30rpx;
  width: 396rpx;
  height: 44rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.page .group_7 .box_20 .section_6 .image-wrapper_9 .label_12 {
  width: 44rpx;
  height: 44rpx;
  margin-left: 30rpx;
}
.page .group_7 .box_20 .section_7 {
  background-color: rgba(255, 255, 255, 0.88);
  border-radius: 10px;
  position: absolute;
  left: -176rpx;
  top: 296rpx;
  width: 702rpx;
  height: 150rpx;
  border: 2px solid rgb(255, 255, 255);
}
.page .group_7 .box_20 .section_7 .text-group_12 {
  width: 112rpx;
  height: 82rpx;
  margin: 34rpx 0 0 62rpx;
}
.page .group_7 .box_20 .section_7 .text-group_12 .text_36 {
  width: 112rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_7 .box_20 .section_7 .text-group_12 .text_37 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .group_7 .box_20 .section_7 .text-group_13 {
  width: 112rpx;
  height: 82rpx;
  margin: 34rpx 0 0 122rpx;
}
.page .group_7 .box_20 .section_7 .text-group_13 .text_38 {
  width: 42rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 34rpx;
}
.page .group_7 .box_20 .section_7 .text-group_13 .text_39 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .group_7 .box_20 .section_7 .group_14 {
  width: 168rpx;
  height: 100rpx;
  margin: 16rpx 42rpx 0 84rpx;
}
.page .group_7 .box_20 .section_7 .group_14 .group_15 {
  width: 98rpx;
  height: 58rpx;
  margin-left: 54rpx;
}
.page .group_7 .box_20 .section_7 .group_14 .group_15 .text_40 {
  width: 72rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 18rpx;
}
.page .group_7 .box_20 .section_7 .group_14 .group_15 .text-wrapper_12 {
  background-color: rgb(238, 12, 12);
  border-radius: 50%;
  height: 36rpx;
  border: 1px solid rgb(255, 255, 255);
  margin-left: -10rpx;
  width: 36rpx;
}
.page .group_7 .box_20 .section_7 .group_14 .group_15 .text-wrapper_12 .text_41 {
  width: 16rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 0 0 10rpx;
}
.page .group_7 .box_20 .section_7 .group_14 .text_42 {
  width: 168rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .group_7 .image_5 {
  position: absolute;
  left: 0;
  top: -68rpx;
  width: 750rpx;
  height: 68rpx;
}
.page .group_7 .box_21 {
  background-image: -webkit-linear-gradient(356deg, rgb(254, 217, 185) 0, rgb(253, 240, 216) 100%);
  background-image: linear-gradient(94deg, rgb(254, 217, 185) 0, rgb(253, 240, 216) 100%);
  border-radius: 10px 10px 0px 0px;
  height: 188rpx;
  width: 682rpx;
  position: absolute;
  left: 34rpx;
  top: -164rpx;
}
.page .group_7 .box_21 .section_8 {
  width: 682rpx;
  height: 188rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
}
.page .group_7 .box_21 .section_8 .box_22 {
  width: 184rpx;
  height: 96rpx;
  margin: 36rpx 0 0 36rpx;
}
.page .group_7 .box_21 .section_8 .box_22 .group_16 {
  width: 156rpx;
  height: 24rpx;
}
.page .group_7 .box_21 .section_8 .box_22 .group_16 .box_23 {
  background-color: rgb(151, 96, 58);
  width: 22rpx;
  height: 24rpx;
}
.page .group_7 .box_21 .section_8 .box_22 .group_16 .text_43 {
  width: 120rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(151, 96, 58);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .group_7 .box_21 .section_8 .box_22 .text_44 {
  width: 184rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(151, 96, 58);
  font-size: 50rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .group_7 .box_21 .section_8 .text-wrapper_13 {
  background-color: rgb(231, 96, 81);
  border-radius: 50px;
  height: 72rpx;
  width: 160rpx;
  margin: 48rpx 24rpx 0 278rpx;
}
.page .group_7 .box_21 .section_8 .text-wrapper_13 .text_45 {
  width: 128rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 18rpx;
}
.page .group_7 .box_24 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 148rpx;
  width: 702rpx;
  position: absolute;
  left: 24rpx;
  top: -2rpx;
}
.page .group_7 .box_24 .box_25 {
  width: 516rpx;
  height: 22rpx;
  margin: 32rpx 0 0 62rpx;
}
.page .group_7 .box_24 .box_25 .text_46 {
  width: 226rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_7 .box_24 .box_25 .image-text_10 {
  width: 104rpx;
  height: 22rpx;
}
.page .group_7 .box_24 .box_25 .image-text_10 .text-group_14 {
  width: 78rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_7 .box_24 .box_25 .image-text_10 .thumbnail_8 {
  width: 20rpx;
  height: 20rpx;
  margin-top: 2rpx;
}
.page .group_7 .box_24 .text-wrapper_14 {
  width: 448rpx;
  height: 44rpx;
  margin: 24rpx 0 26rpx 90rpx;
}
.page .group_7 .box_24 .text-wrapper_14 .text_47 {
  width: 170rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_7 .box_24 .text-wrapper_14 .text_48 {
  width: 24rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_7 .box_26 {
  background-image: -webkit-linear-gradient(314deg, rgb(110, 141, 243) 0, rgb(134, 161, 245) 100%);
  background-image: linear-gradient(136deg, rgb(110, 141, 243) 0, rgb(134, 161, 245) 100%);
  border-radius: 10px;
  position: absolute;
  left: 24rpx;
  top: 500rpx;
  width: 340rpx;
  height: 168rpx;
  -webkit-box-pack: flex-center;
  -webkit-justify-content: flex-center;
          justify-content: flex-center;
}
.page .group_7 .box_26 .text_49 {
  width: 128rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 20rpx 0 0 28rpx;
}
.page .group_7 .box_26 .text_50 {
  width: 280rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.8);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 18rpx 0 0 28rpx;
}
.page .group_7 .box_26 .text-wrapper_15 {
  background-color: rgb(255, 255, 255);
  border-radius: 100px;
  height: 40rpx;
  width: 114rpx;
  margin: 24rpx 0 20rpx 28rpx;
}
.page .group_7 .box_26 .text-wrapper_15 .text_51 {
  width: 66rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(110, 141, 243);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 10rpx 0 0 20rpx;
}
.page .group_7 .box_26 .image-wrapper_10 {
  background-image: -webkit-linear-gradient(314deg, rgb(110, 141, 243) 0, rgb(134, 161, 245) 100%);
  background-image: linear-gradient(136deg, rgb(110, 141, 243) 0, rgb(134, 161, 245) 100%);
  border-radius: 10px;
  height: 168rpx;
  width: 248rpx;
  position: absolute;
  left: 92rpx;
  top: 0;
}
.page .group_7 .box_26 .image-wrapper_10 .image_6 {
  width: 248rpx;
  height: 168rpx;
}
.page .group_7 .box_27 {
  background-image: -webkit-linear-gradient(314deg, rgb(231, 153, 69) 0, rgb(246, 190, 90) 100%);
  background-image: linear-gradient(136deg, rgb(231, 153, 69) 0, rgb(246, 190, 90) 100%);
  border-radius: 10px;
  position: absolute;
  left: 384rpx;
  top: 500rpx;
  width: 340rpx;
  height: 168rpx;
  -webkit-box-pack: flex-center;
  -webkit-justify-content: flex-center;
          justify-content: flex-center;
}
.page .group_7 .box_27 .text_52 {
  width: 160rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 20rpx 0 0 30rpx;
}
.page .group_7 .box_27 .text_53 {
  width: 240rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.8);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 18rpx 0 0 30rpx;
}
.page .group_7 .box_27 .text-wrapper_16 {
  background-color: rgb(255, 255, 255);
  border-radius: 100px;
  height: 40rpx;
  width: 114rpx;
  margin: 24rpx 0 20rpx 30rpx;
}
.page .group_7 .box_27 .text-wrapper_16 .text_54 {
  width: 66rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(231, 154, 69);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 10rpx 0 0 20rpx;
}
.page .group_7 .box_27 .image-wrapper_11 {
  background-image: -webkit-linear-gradient(314deg, rgb(110, 141, 243) 0, rgb(134, 161, 245) 100%);
  background-image: linear-gradient(136deg, rgb(110, 141, 243) 0, rgb(134, 161, 245) 100%);
  border-radius: 10px;
  height: 168rpx;
  width: 248rpx;
  position: absolute;
  left: 92rpx;
  top: 0;
}
.page .group_7 .box_27 .image-wrapper_11 .image_7 {
  width: 248rpx;
  height: 168rpx;
}
