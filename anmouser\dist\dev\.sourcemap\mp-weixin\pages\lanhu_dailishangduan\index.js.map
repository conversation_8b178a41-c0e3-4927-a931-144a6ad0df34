{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_dailishangduan/index.vue?42da", "webpack:///./src/pages/lanhu_dailishangduan/index.vue?6c2e", "webpack:///./src/pages/lanhu_dailishangduan/index.vue?8939", "webpack:///./src/pages/lanhu_dailishangduan/index.vue?94ef", "uni-app:///src/pages/lanhu_dailishangduan/index.vue", "webpack:///./src/pages/lanhu_dailishangduan/index.vue?b1d9", "webpack:///./src/pages/lanhu_dailishangduan/index.vue?1ea2"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loopData0", "lanhutext0", "lanhutext1", "lanhutext2", "lanhutext3", "lanhutext4", "lanhutext5", "lanhutext6", "lanhutext7", "lanhutext8", "lanhutext9", "lanhutext10", "slot1", "slot2", "loopData1", "lanhuimage0", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAyD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFtCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCoZtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAX,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAE,KAAA;MACA,EACA;MACAC,SAAA,GACA;QACAC,WAAA,EACA;MACA,GACA;QACAA,WAAA,EACA;MACA,GACA;QACAA,WAAA,EACA;MACA,EACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;ACvcA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_dailishang<PERSON>n/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_dailishangduan/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=66baab22&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_dailishangduan/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=66baab22&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"group_1 flex-col\">\n      <view class=\"section_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNGd123943771561d022a87b263425c4041.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG2e598a65d05619bcca910a84029f0065.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG2766341d0b27d2f2376709071239a4d2.png\"\n        />\n      </view>\n      <view class=\"section_2 flex-row\">\n        <image\n          class=\"thumbnail_4\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG6ed4604c1b39778be5110bf07faac9d5.png\"\n        />\n        <text class=\"text_2\">代理商端</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG85b2c25217aac922623185782f643c68.png\"\n        />\n      </view>\n    </view>\n    <view class=\"group_2 flex-col\">\n      <view class=\"text-wrapper_1 flex-row\">\n        <text class=\"text_3\">订单管理</text>\n      </view>\n      <view class=\"box_1 flex-row justify-between\">\n        <view class=\"image-text_1 flex-col justify-between\">\n          <view class=\"group_3 flex-col\">\n            <view class=\"text-wrapper_2 flex-col\">\n              <text class=\"text_4\">3</text>\n            </view>\n          </view>\n          <text class=\"text-group_1\">待接单</text>\n        </view>\n        <view class=\"box_2 flex-col justify-between\">\n          <view class=\"image-wrapper_1 flex-col\">\n            <image\n              class=\"label_1\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG9a26b171213a1c59bcc2baa843adf72f.png\"\n            />\n          </view>\n          <text class=\"text_5\">已接单</text>\n        </view>\n        <view class=\"image-text_2 flex-col justify-between\">\n          <image\n            class=\"label_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNGa8e8d8ffa79926f73b8f9ca74b9fa045.png\"\n          />\n          <text class=\"text-group_2\">已出发</text>\n        </view>\n        <view class=\"box_3 flex-col justify-between\">\n          <view class=\"image-wrapper_2 flex-col\">\n            <image\n              class=\"label_3\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNGdaa8295c7039770e78921fee7de5ec71.png\"\n            />\n          </view>\n          <text class=\"text_6\">已到达</text>\n        </view>\n      </view>\n      <view class=\"box_4 flex-row justify-between\">\n        <view class=\"image-wrapper_3 flex-col\">\n          <image\n            class=\"label_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dailishangduan/cc365b9de16542ebbb10879d00d4740c_mergeImage.png\"\n          />\n        </view>\n        <view class=\"image-wrapper_4 flex-col\">\n          <image\n            class=\"label_5\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG6852f689e2ab416d5c8fad1281dfe68f.png\"\n          />\n        </view>\n      </view>\n      <view class=\"text-wrapper_3 flex-row justify-between\">\n        <text class=\"text_7\">服务中</text>\n        <text class=\"text_8\">已完成</text>\n      </view>\n    </view>\n    <view class=\"group_4 flex-col\">\n      <view class=\"box_5 flex-col\">\n        <view class=\"text-wrapper_4 flex-row\">\n          <text class=\"text_9\">加钟管理</text>\n        </view>\n        <view class=\"box_6 flex-row justify-between\">\n          <view class=\"image-text_3 flex-col justify-between\">\n            <image\n              class=\"label_6\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNGfee7f5f2aca7b26b012a84560623d247.png\"\n            />\n            <text class=\"text-group_3\">待接单</text>\n          </view>\n          <view class=\"box_7 flex-col justify-between\">\n            <view class=\"image-wrapper_5 flex-col\">\n              <image\n                class=\"label_7\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG9a26b171213a1c59bcc2baa843adf72f.png\"\n              />\n            </view>\n            <text class=\"text_10\">已接单</text>\n          </view>\n          <view class=\"image-text_4 flex-col justify-between\">\n            <image\n              class=\"label_8\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNGa8e8d8ffa79926f73b8f9ca74b9fa045.png\"\n            />\n            <text class=\"text-group_4\">服务中</text>\n          </view>\n          <view class=\"box_8 flex-col justify-between\">\n            <view class=\"image-wrapper_6 flex-col\">\n              <image\n                class=\"label_9\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNGdaa8295c7039770e78921fee7de5ec71.png\"\n              />\n            </view>\n            <text class=\"text_11\">已完成</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"list_1 flex-col\">\n        <view\n          class=\"list-items_1 flex-col\"\n          v-for=\"(item, index) in loopData0\"\n          :key=\"index\"\n        >\n          <view class=\"section_3 flex-row justify-between\">\n            <text class=\"text_12\" v-html=\"item.lanhutext0\"></text>\n            <view class=\"image-text_5 flex-row\">\n              <text class=\"text-group_5\" v-html=\"item.lanhutext1\"></text>\n              <image\n                v-if=\"item.slot2 === 2\"\n                class=\"thumbnail_5\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG804a5aeaa27993298a6fa9dcdf2b2972.png\"\n              />\n              <image\n                v-if=\"item.slot1 === 1\"\n                class=\"thumbnail_6\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_dailishangduan/5b76e2dedc334157a8cdbd0004d8bd1c_mergeImage.png\"\n              />\n            </view>\n          </view>\n          <view class=\"section_4 flex-row\">\n            <view class=\"group_5 flex-col justify-between\">\n              <view class=\"box_9 flex-row\">\n                <text class=\"text_13\" v-html=\"item.lanhutext2\"></text>\n                <view class=\"text-wrapper_5 flex-col\">\n                  <text class=\"text_14\" v-html=\"item.lanhutext3\"></text>\n                </view>\n              </view>\n              <text class=\"text_15\" v-html=\"item.lanhutext4\"></text>\n            </view>\n            <view class=\"text-group_6 flex-col justify-between\">\n              <text class=\"text_16\" v-html=\"item.lanhutext5\"></text>\n              <text class=\"text_17\" v-html=\"item.lanhutext6\"></text>\n            </view>\n            <view class=\"group_6 flex-col\">\n              <view class=\"box_10 flex-col\"></view>\n              <text class=\"text_18\" v-html=\"item.lanhutext7\"></text>\n            </view>\n            <view class=\"text-group_7 flex-col justify-between\">\n              <text class=\"text_19\" v-html=\"item.lanhutext8\"></text>\n              <text class=\"text_20\" v-html=\"item.lanhutext9\"></text>\n            </view>\n          </view>\n          <text class=\"text_21\" v-html=\"item.lanhutext10\"></text>\n        </view>\n      </view>\n      <image\n        class=\"image_2\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNGa911c964ed32937659ea639dfb0c05a2.png\"\n      />\n    </view>\n    <view class=\"group_7 flex-col\">\n      <view class=\"box_11 flex-row justify-between\">\n        <view class=\"group_8 flex-col\">\n          <text class=\"text_22\">绑定渠道商</text>\n          <text class=\"text_23\">直招渠道，获利更多</text>\n          <view class=\"text-wrapper_6 flex-col\">\n            <text class=\"text_24\">邀请TA</text>\n          </view>\n          <view class=\"image-wrapper_7 flex-col\">\n            <image\n              class=\"image_3\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG2f67808663910123ef5997055797c661.png\"\n            />\n          </view>\n        </view>\n        <view class=\"group_9 flex-col\">\n          <text class=\"text_25\">绑定分销经理</text>\n          <text class=\"text_26\">让专业的人帮你拉新用户</text>\n          <view class=\"text-wrapper_7 flex-col\">\n            <text class=\"text_27\">邀请TA</text>\n          </view>\n          <view class=\"image-wrapper_8 flex-col\">\n            <image\n              class=\"image_4\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG2f67808663910123ef5997055797c661.png\"\n            />\n          </view>\n        </view>\n      </view>\n      <view class=\"box_12 flex-row justify-between\">\n        <view class=\"group_10 flex-col\">\n          <view class=\"block_1 flex-row\">\n            <view class=\"text-wrapper_8 flex-col\">\n              <text class=\"text_28\">订单通知</text>\n            </view>\n          </view>\n          <view class=\"block_2 flex-row justify-between\">\n            <text class=\"text_29\">您有新的订单来啦!</text>\n            <view class=\"box_13 flex-col\"></view>\n          </view>\n        </view>\n        <view class=\"group_11 flex-col\">\n          <view class=\"box_14 flex-row\">\n            <view class=\"text-wrapper_9 flex-col\">\n              <text class=\"text_30\">退款通知</text>\n            </view>\n          </view>\n          <view class=\"box_15 flex-row justify-between\">\n            <text class=\"text_31\">您有新的订单来啦!</text>\n            <view class=\"section_5 flex-col\"></view>\n          </view>\n        </view>\n      </view>\n      <view class=\"box_16 flex-row justify-between\">\n        <view class=\"box_17 flex-col\">\n          <view class=\"text-wrapper_10 flex-col\">\n            <text class=\"text_32\">拒单通知</text>\n          </view>\n          <text class=\"text_33\">暂无数据</text>\n        </view>\n        <view class=\"box_18 flex-col\">\n          <view class=\"group_12 flex-row\">\n            <view class=\"text-wrapper_11 flex-col\">\n              <text class=\"text_34\">拒单管理</text>\n            </view>\n          </view>\n          <view class=\"group_13 flex-row justify-between\">\n            <text class=\"text_35\">你有一个待转单</text>\n            <view class=\"box_19 flex-col\"></view>\n          </view>\n        </view>\n      </view>\n      <view class=\"box_20 flex-col\">\n        <view class=\"section_6 flex-row\">\n          <view class=\"image-text_6 flex-col justify-between\">\n            <image\n              class=\"label_10\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNGd1ae8246f108d3c745eb9779596a27e4.png\"\n            />\n            <text class=\"text-group_8\">技师管理</text>\n          </view>\n          <view class=\"image-text_7 flex-col justify-between\">\n            <image\n              class=\"label_11\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG98b3e8fc2865ecd35cebfa91eb24cf76.png\"\n            />\n            <text class=\"text-group_9\">佣金信息</text>\n          </view>\n          <view class=\"image-text_8 flex-col justify-between\">\n            <image\n              class=\"thumbnail_7\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG88d2488c79e153e23ab4cc8581e1263d.png\"\n            />\n            <text class=\"text-group_10\">提现申请</text>\n          </view>\n          <view class=\"image-text_9 flex-col justify-between\">\n            <image\n              class=\"label_12\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNGfee991c8efd03d9541313c3ebbbc7e6e.png\"\n            />\n            <text class=\"text-group_11\">账号设置</text>\n          </view>\n          <view class=\"image-wrapper_9 flex-row\">\n            <image\n              class=\"label_12\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.lanhuimage0\"\n              v-for=\"(item, index) in loopData1\"\n              :key=\"index\"\n            />\n          </view>\n        </view>\n        <view class=\"section_7 flex-row\">\n          <view class=\"text-group_12 flex-col justify-between\">\n            <text class=\"text_36\">￥598</text>\n            <text class=\"text_37\">今日营收</text>\n          </view>\n          <view class=\"text-group_13 flex-col justify-between\">\n            <text class=\"text_38\">12</text>\n            <text class=\"text_39\">今日订单</text>\n          </view>\n          <view class=\"group_14 flex-col justify-between\">\n            <view class=\"group_15 flex-row\">\n              <text class=\"text_40\">598</text>\n              <view class=\"text-wrapper_12 flex-col\">\n                <text class=\"text_41\">3</text>\n              </view>\n            </view>\n            <text class=\"text_42\">今日区域新增</text>\n          </view>\n        </view>\n      </view>\n      <image\n        class=\"image_5\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNGbbf5cbb260fac6d17cd288a8481a79a7.png\"\n      />\n      <view class=\"box_21 flex-col\">\n        <view class=\"section_8 flex-row\">\n          <view class=\"box_22 flex-col justify-between\">\n            <view class=\"group_16 flex-row justify-between\">\n              <view class=\"box_23 flex-col\"></view>\n              <text class=\"text_43\">可提现金额</text>\n            </view>\n            <text class=\"text_44\">1282.35</text>\n          </view>\n          <view class=\"text-wrapper_13 flex-col\">\n            <text class=\"text_45\">我要提现</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"box_24 flex-col\">\n        <view class=\"box_25 flex-row justify-between\">\n          <text class=\"text_46\">总金额(不含手续费)</text>\n          <view class=\"image-text_10 flex-row justify-between\">\n            <text class=\"text-group_14\">未入账</text>\n            <image\n              class=\"thumbnail_8\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG1f3e3dd1955edf9509eff6a46ac8eb41.png\"\n            />\n          </view>\n        </view>\n        <view class=\"text-wrapper_14 flex-row justify-between\">\n          <text class=\"text_47\">￥759.25</text>\n          <text class=\"text_48\">0</text>\n        </view>\n      </view>\n      <view class=\"box_26 flex-col\">\n        <text class=\"text_49\">绑定技师</text>\n        <text class=\"text_50\">整合自己的技师资源，获取分润</text>\n        <view class=\"text-wrapper_15 flex-col\">\n          <text class=\"text_51\">邀请TA</text>\n        </view>\n        <view class=\"image-wrapper_10 flex-col\">\n          <image\n            class=\"image_6\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG2f67808663910123ef5997055797c661.png\"\n          />\n        </view>\n      </view>\n      <view class=\"box_27 flex-col\">\n        <text class=\"text_52\">绑定业务员</text>\n        <text class=\"text_53\">招揽人才，为自己拓宽渠道</text>\n        <view class=\"text-wrapper_16 flex-col\">\n          <text class=\"text_54\">邀请TA</text>\n        </view>\n        <view class=\"image-wrapper_11 flex-col\">\n          <image\n            class=\"image_7\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_dailishangduan/FigmaDDSSlicePNG2f67808663910123ef5997055797c661.png\"\n          />\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      loopData0: [\n        {\n          lanhutext0: '服务退款',\n          lanhutext1: '更多',\n          lanhutext2: '3',\n          lanhutext3: '3',\n          lanhutext4: '待退款',\n          lanhutext5: '5',\n          lanhutext6: '同意退款',\n          lanhutext7: '5',\n          lanhutext8: '5',\n          lanhutext9: '退款中',\n          lanhutext10: '拒绝退款',\n          slot1: 1\n        },\n        {\n          lanhutext0: '加钟退款',\n          lanhutext1: '更多',\n          lanhutext2: '3',\n          lanhutext3: '3',\n          lanhutext4: '待退款',\n          lanhutext5: '5',\n          lanhutext6: '同意退款',\n          lanhutext7: '5',\n          lanhutext8: '5',\n          lanhutext9: '退款中',\n          lanhutext10: '拒绝退款',\n          slot2: 2\n        }\n      ],\n      loopData1: [\n        {\n          lanhuimage0:\n            '/static/lanhu_dailishangduan/FigmaDDSSlicePNGfee991c8efd03d9541313c3ebbbc7e6e.png'\n        },\n        {\n          lanhuimage0:\n            '/static/lanhu_dailishangduan/FigmaDDSSlicePNGd1ae8246f108d3c745eb9779596a27e4.png'\n        },\n        {\n          lanhuimage0:\n            '/static/lanhu_dailishangduan/FigmaDDSSlicePNG98b3e8fc2865ecd35cebfa91eb24cf76.png'\n        }\n      ],\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332585429\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}