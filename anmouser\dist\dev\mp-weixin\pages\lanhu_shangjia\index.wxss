@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(247, 247, 247);
  position: relative;
  width: 750rpx;
  height: 2186rpx;
  overflow: hidden;
}
.page .box_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 172rpx;
}
.page .box_1 .block_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .block_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .block_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .block_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .block_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .block_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .block_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .box_1 .block_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .box_1 .block_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .image_2 {
  width: 750rpx;
  height: 612rpx;
}
.page .box_2 {
  background-image: -webkit-linear-gradient(top, rgba(64, 170, 127, 0.2) 0, rgba(65, 170, 127, 0) 100%);
  background-image: linear-gradient(180deg, rgba(64, 170, 127, 0.2) 0, rgba(65, 170, 127, 0) 100%);
  position: relative;
  width: 750rpx;
  height: 704rpx;
  margin: 1262rpx 0 564rpx 0;
}
.page .box_2 .section_1 {
  background-color: rgb(217, 217, 217);
  width: 76rpx;
  height: 40rpx;
  margin: 100rpx 0 0 570rpx;
}
.page .box_2 .section_2 {
  width: 466rpx;
  height: 94rpx;
  margin: 200rpx 0 0 180rpx;
}
.page .box_2 .section_2 .image-wrapper_1 {
  background-color: rgb(217, 217, 217);
  height: 40rpx;
  margin-top: 14rpx;
  width: 76rpx;
}
.page .box_2 .section_2 .image-wrapper_1 .image_3 {
  width: 76rpx;
  height: 40rpx;
}
.page .box_2 .section_2 .text_3 {
  width: 200rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgba(62, 200, 174, 0.9);
  font-size: 50rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 50rpx;
  margin-left: 20rpx;
}
.page .box_2 .section_2 .box_3 {
  background-color: rgb(217, 217, 217);
  width: 76rpx;
  height: 40rpx;
  margin: 54rpx 0 0 94rpx;
}
.page .box_2 .text_4 {
  width: 200rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgba(62, 200, 174, 0.9);
  font-size: 50rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 50rpx;
  margin: 404rpx 0 0 276rpx;
}
.page .box_2 .text_5 {
  width: 200rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgba(62, 200, 174, 0.9);
  font-size: 50rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 50rpx;
  margin: 970rpx 0 0 276rpx;
}
.page .box_2 .text_6 {
  width: 350rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgba(62, 200, 174, 0.9);
  font-size: 50rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 50rpx;
  margin: 422rpx 0 0 200rpx;
}
.page .box_2 .text_7 {
  width: 312rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 22rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 22rpx;
  margin: 118rpx 0 0 364rpx;
}
.page .box_2 .text_8 {
  width: 244rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 22rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 22rpx;
  margin: 62rpx 0 0 364rpx;
}
.page .box_2 .text_9 {
  width: 290rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 22rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 22rpx;
  margin: 56rpx 0 0 364rpx;
}
.page .box_2 .text_10 {
  width: 200rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgba(62, 200, 174, 0.9);
  font-size: 50rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 50rpx;
  margin: 162rpx 0 2270rpx 276rpx;
}
.page .box_2 .image_4 {
  position: absolute;
  left: 482rpx;
  top: 22rpx;
  width: 108rpx;
  height: 146rpx;
}
.page .box_2 .section_3 {
  background-color: rgb(217, 217, 217);
  position: absolute;
  left: 570rpx;
  top: 894rpx;
  width: 76rpx;
  height: 40rpx;
}
.page .box_2 .image_5 {
  position: absolute;
  left: 482rpx;
  top: 814rpx;
  width: 108rpx;
  height: 146rpx;
}
.page .box_2 .image-wrapper_2 {
  background-color: rgb(217, 217, 217);
  height: 40rpx;
  width: 76rpx;
  position: absolute;
  left: 180rpx;
  top: 854rpx;
}
.page .box_2 .image-wrapper_2 .image_6 {
  width: 76rpx;
  height: 40rpx;
}
.page .box_2 .image_7 {
  position: absolute;
  left: 482rpx;
  top: 316rpx;
  width: 108rpx;
  height: 146rpx;
}
.page .box_2 .section_4 {
  background-color: rgb(217, 217, 217);
  position: absolute;
  left: 570rpx;
  top: 1934rpx;
  width: 76rpx;
  height: 40rpx;
}
.page .box_2 .image_8 {
  position: absolute;
  left: 482rpx;
  top: 1854rpx;
  width: 108rpx;
  height: 146rpx;
}
.page .box_2 .image-wrapper_3 {
  background-color: rgb(217, 217, 217);
  height: 40rpx;
  width: 76rpx;
  position: absolute;
  left: 180rpx;
  top: 1894rpx;
}
.page .box_2 .image-wrapper_3 .image_9 {
  width: 76rpx;
  height: 40rpx;
}
.page .box_2 .section_5 {
  background-color: rgb(217, 217, 217);
  position: absolute;
  left: 656rpx;
  top: 2426rpx;
  width: 76rpx;
  height: 40rpx;
}
.page .box_2 .image_10 {
  position: absolute;
  left: 568rpx;
  top: 2346rpx;
  width: 108rpx;
  height: 146rpx;
}
.page .box_2 .image-wrapper_4 {
  background-color: rgb(217, 217, 217);
  height: 40rpx;
  width: 76rpx;
  position: absolute;
  left: 96rpx;
  top: 2386rpx;
}
.page .box_2 .image-wrapper_4 .image_11 {
  width: 76rpx;
  height: 40rpx;
}
.page .box_2 .section_6 {
  background-color: rgba(62, 200, 174, 0.9);
  border-radius: 10px;
  position: absolute;
  left: 42rpx;
  top: 452rpx;
  width: 670rpx;
  height: 346rpx;
}
.page .box_2 .section_6 .image-text_1 {
  width: 728rpx;
  height: 346rpx;
}
.page .box_2 .section_6 .image-text_1 .image_12 {
  width: 304rpx;
  height: 346rpx;
}
.page .box_2 .section_6 .image-text_1 .text-group_1 {
  width: 396rpx;
  height: 258rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  margin-top: 26rpx;
}
.page .box_2 .text-wrapper_1 {
  background-color: rgba(62, 200, 174, 0.9);
  border-radius: 10px;
  height: 122rpx;
  width: 670rpx;
  position: absolute;
  left: 42rpx;
  top: 166rpx;
}
.page .box_2 .text-wrapper_1 .paragraph_1 {
  width: 420rpx;
  height: 84rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  margin: 18rpx 0 0 124rpx;
}
.page .box_2 .section_7 {
  background-color: rgba(62, 200, 174, 0.9);
  border-radius: 10px;
  position: absolute;
  left: 42rpx;
  top: 948rpx;
  width: 670rpx;
  height: 364rpx;
}
.page .box_2 .section_7 .image-text_2 {
  width: 694rpx;
  height: 364rpx;
}
.page .box_2 .section_7 .image-text_2 .image-wrapper_5 {
  height: 364rpx;
  background: url(/static/lanhu_shangjia/FigmaDDSSlicePNG25cec729911aaa5102cf0d3208beb9f2.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 304rpx;
}
.page .box_2 .section_7 .image-text_2 .image-wrapper_5 .image_13 {
  width: 304rpx;
  height: 364rpx;
}
.page .box_2 .section_7 .image-text_2 .text-group_2 {
  width: 362rpx;
  height: 292rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  margin-top: 36rpx;
}
.page .box_2 .section_8 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 486rpx;
  border: 1px solid rgba(62, 200, 174, 0.9);
  width: 670rpx;
  position: absolute;
  left: 42rpx;
  top: 1356rpx;
}
.page .box_2 .section_8 .text-wrapper_2 {
  width: 640rpx;
  height: 426rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  line-height: 314rpx;
  margin: 26rpx 0 0 14rpx;
}
.page .box_2 .section_8 .text-wrapper_2 .paragraph_2 {
  width: 640rpx;
  height: 426rpx;
  overflow-wrap: break-word;
  color: rgba(62, 200, 174, 0.9);
  font-size: 28rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  line-height: 314rpx;
}
.page .box_2 .section_8 .text-wrapper_2 .paragraph_3 {
  width: 640rpx;
  height: 426rpx;
  overflow-wrap: break-word;
  color: rgba(62, 200, 174, 0.9);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 314rpx;
}
.page .box_2 .text-wrapper_3 {
  background-color: rgba(62, 200, 174, 0.9);
  border-radius: 10px;
  height: 330rpx;
  width: 670rpx;
  position: absolute;
  left: 42rpx;
  top: 1986rpx;
}
.page .box_2 .text-wrapper_3 .paragraph_4 {
  width: 608rpx;
  height: 264rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 40rpx;
  margin: 26rpx 0 0 30rpx;
}
.page .box_2 .section_9 {
  box-shadow: 0px 8px 16px 0px rgba(65, 170, 127, 0.2);
  background-color: rgb(241, 252, 248);
  position: absolute;
  left: 40rpx;
  top: 2076rpx;
  width: 690rpx;
  height: 282rpx;
  border: 1px solid rgb(255, 255, 255);
}
.page .box_2 .section_9 .group_1 {
  box-shadow: 0px 8px 16px 0px rgba(65, 170, 127, 0.2);
  background-color: rgb(255, 255, 255);
  border-radius: 5px;
  width: 358rpx;
  height: 52rpx;
  border: 1px solid rgb(255, 255, 255);
  margin: 38rpx 0 0 290rpx;
}
.page .box_2 .section_9 .group_2 {
  box-shadow: 0px 8px 16px 0px rgba(65, 170, 127, 0.2);
  background-color: rgb(255, 255, 255);
  border-radius: 5px;
  width: 358rpx;
  height: 52rpx;
  border: 1px solid rgb(255, 255, 255);
  margin: 22rpx 0 0 290rpx;
}
.page .box_2 .section_9 .group_3 {
  box-shadow: 0px 8px 16px 0px rgba(65, 170, 127, 0.2);
  background-color: rgb(255, 255, 255);
  border-radius: 5px;
  width: 358rpx;
  height: 52rpx;
  border: 1px solid rgb(255, 255, 255);
  margin: 16rpx 0 50rpx 290rpx;
}
.page .box_2 .image-wrapper_6 {
  height: 458rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
  width: 458rpx;
  position: absolute;
  left: -40rpx;
  top: 2434rpx;
}
.page .box_2 .image-wrapper_6 .image_14 {
  width: 138rpx;
  height: 138rpx;
  margin: 168rpx 0 0 172rpx;
}
.page .box_2 .section_10 {
  background-color: rgb(217, 217, 217);
  position: absolute;
  left: 570rpx;
  top: 2960rpx;
  width: 76rpx;
  height: 40rpx;
}
.page .box_2 .image_15 {
  position: absolute;
  left: 482rpx;
  top: 2880rpx;
  width: 108rpx;
  height: 146rpx;
}
.page .box_2 .image-wrapper_7 {
  background-color: rgb(217, 217, 217);
  height: 40rpx;
  width: 76rpx;
  position: absolute;
  left: 180rpx;
  top: 2920rpx;
}
.page .box_2 .image-wrapper_7 .image_16 {
  width: 76rpx;
  height: 40rpx;
}
.page .box_2 .text-wrapper_4 {
  background-color: rgb(240, 241, 245);
  border-radius: 10px;
  height: 896rpx;
  width: 670rpx;
  position: absolute;
  left: 42rpx;
  top: 3030rpx;
}
.page .box_2 .text-wrapper_4 .paragraph_5 {
  width: 592rpx;
  height: 756rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 40rpx;
  margin: 28rpx 0 0 38rpx;
}
.page .box_2 .section_11 {
  background-color: rgb(255, 255, 255);
  height: 126rpx;
  width: 750rpx;
  position: absolute;
  left: 0;
  top: 0;
}
.page .box_2 .section_11 .text-wrapper_5 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 88rpx;
  width: 690rpx;
  margin: 20rpx 0 0 30rpx;
}
.page .box_2 .section_11 .text-wrapper_5 .text_11 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 286rpx;
}
.page .box_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  position: absolute;
  left: 0;
  top: 746rpx;
  width: 750rpx;
  height: 798rpx;
}
.page .box_4 .group_4 {
  width: 704rpx;
  height: 36rpx;
  margin: 26rpx 0 0 24rpx;
}
.page .box_4 .group_4 .text_12 {
  width: 144rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_4 .group_4 .box_5 {
  background-color: rgb(11, 206, 148);
  width: 32rpx;
  height: 32rpx;
  margin: 2rpx 0 0 442rpx;
}
.page .box_4 .group_4 .text_13 {
  width: 78rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 0 0 8rpx;
}
.page .box_4 .group_5 {
  width: 702rpx;
  height: 40rpx;
  margin: 28rpx 0 0 24rpx;
}
.page .box_4 .group_5 .text-wrapper_6 {
  width: 96rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_4 .group_5 .text-wrapper_6 .text_14 {
  width: 96rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_4 .group_5 .text-wrapper_6 .text_15 {
  width: 96rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_4 .group_5 .text-wrapper_7 {
  background-image: -webkit-linear-gradient(left, rgb(255, 239, 190) 0, rgb(254, 230, 157) 100%);
  background-image: linear-gradient(90deg, rgb(255, 239, 190) 0, rgb(254, 230, 157) 100%);
  border-radius: 10px 10px 0px 10px;
  height: 30rpx;
  width: 104rpx;
  margin: 4rpx 0 0 4rpx;
}
.page .box_4 .group_5 .text-wrapper_7 .text_16 {
  width: 80rpx;
  height: 16rpx;
  overflow-wrap: break-word;
  color: rgb(98, 59, 4);
  font-size: 16rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 8rpx 0 0 12rpx;
}
.page .box_4 .group_5 .text_17 {
  width: 104rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  text-decoration: line-through;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 8rpx 0 0 12rpx;
}
.page .box_4 .group_5 .text_18 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 8rpx 0 0 246rpx;
}
.page .box_4 .text-wrapper_8 {
  background-color: rgb(245, 244, 239);
  border-radius: 10px;
  height: 104rpx;
  width: 700rpx;
  margin: 28rpx 0 0 24rpx;
}
.page .box_4 .text-wrapper_8 .text_19 {
  width: 644rpx;
  height: 66rpx;
  overflow-wrap: break-word;
  color: rgb(173, 153, 128);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 320rpx;
  margin: 18rpx 0 0 26rpx;
}
.page .box_4 .group_6 {
  background-color: rgb(247, 247, 247);
  width: 750rpx;
  height: 20rpx;
  margin-top: 32rpx;
}
.page .box_4 .text_20 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 36rpx 0 418rpx 24rpx;
}
.page .box_6 {
  background-color: rgb(255, 255, 255);
  height: 896rpx;
  width: 750rpx;
  position: absolute;
  left: 0;
  top: 1150rpx;
}
.page .box_6 .block_3 {
  background-color: rgba(62, 200, 174, 0.9);
  height: 896rpx;
  width: 750rpx;
  position: relative;
}
.page .box_6 .block_3 .image_17 {
  width: 750rpx;
  height: 586rpx;
}
.page .box_6 .block_3 .section_12 {
  background-color: rgba(62, 200, 174, 0.9);
  position: absolute;
  left: 0;
  top: 0;
  width: 750rpx;
  height: 896rpx;
}
.page .box_6 .block_3 .section_12 .text-group_3 {
  width: 660rpx;
  height: 258rpx;
  margin: 102rpx 0 0 46rpx;
}
.page .box_6 .block_3 .section_12 .text-group_3 .text_21 {
  width: 510rpx;
  height: 84rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 60rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 60rpx;
  margin-left: 74rpx;
}
.page .box_6 .block_3 .section_12 .text-group_3 .text_22 {
  width: 660rpx;
  height: 154rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 38rpx;
  margin-top: 20rpx;
}
.page .box_6 .block_3 .section_12 .image_18 {
  width: 684rpx;
  height: 458rpx;
  margin: 28rpx 0 50rpx 34rpx;
}
