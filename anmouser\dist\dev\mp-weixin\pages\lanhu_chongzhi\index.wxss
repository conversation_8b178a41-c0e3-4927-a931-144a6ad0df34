@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(255, 255, 255);
  position: relative;
  width: 750rpx;
  height: 1786rpx;
  overflow: hidden;
}
.page .group_1 {
  background-image: -webkit-linear-gradient(top, rgb(62, 200, 174) 82.399964%);
  background-image: linear-gradient(180deg, rgb(62, 200, 174) 82.399964%);
  width: 750rpx;
  height: 518rpx;
}
.page .group_1 .group_2 {
  width: 688rpx;
  height: 38rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .group_1 .group_2 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
}
.page .group_1 .group_2 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin-left: 502rpx;
}
.page .group_1 .group_2 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin-left: 6rpx;
}
.page .group_1 .group_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin-left: 6rpx;
}
.page .group_1 .group_3 {
  width: 702rpx;
  height: 64rpx;
  margin: 34rpx 0 0 36rpx;
}
.page .group_1 .group_3 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin-top: 16rpx;
}
.page .group_1 .group_3 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 10rpx 0 0 22rpx;
}
.page .group_1 .group_3 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin-left: 360rpx;
}
.page .group_1 .group_4 {
  background-color: rgba(255, 255, 255, 0.73);
  border-radius: 10px 10px 0px 0px;
  position: relative;
  width: 702rpx;
  height: 278rpx;
  border: 2px solid rgba(255, 255, 255, 0.37);
  margin: 48rpx 0 42rpx 24rpx;
}
.page .group_1 .group_4 .text-group_1 {
  width: 158rpx;
  height: 114rpx;
  margin: 44rpx 0 0 36rpx;
}
.page .group_1 .group_4 .text-group_1 .text_3 {
  width: 150rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
}
.page .group_1 .group_4 .text-group_1 .text_4 {
  width: 158rpx;
  height: 74rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 50rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 50rpx;
  margin-top: 8rpx;
}
.page .group_1 .group_4 .image_2 {
  width: 138rpx;
  height: 138rpx;
  margin: 32rpx 48rpx 0 322rpx;
}
.page .group_1 .group_4 .group_5 {
  background-color: rgb(21, 159, 133);
  border-radius: 10px 10px 0px 0px;
  position: absolute;
  left: -2rpx;
  top: 182rpx;
  width: 702rpx;
  height: 138rpx;
}
.page .group_1 .group_4 .group_5 .image-text_1 {
  width: 96rpx;
  height: 82rpx;
  margin: 32rpx 0 0 70rpx;
}
.page .group_1 .group_4 .group_5 .image-text_1 .group_6 {
  background-color: rgb(255, 255, 255);
  width: 44rpx;
  height: 44rpx;
  margin-left: 26rpx;
}
.page .group_1 .group_4 .group_5 .image-text_1 .text-group_2 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Pinyon Script-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 14rpx;
}
.page .group_1 .group_4 .group_5 .box_1 {
  width: 96rpx;
  height: 88rpx;
  margin: 26rpx 0 0 138rpx;
}
.page .group_1 .group_4 .group_5 .box_1 .image-wrapper_1 {
  height: 48rpx;
  margin-left: 24rpx;
  width: 48rpx;
}
.page .group_1 .group_4 .group_5 .box_1 .image-wrapper_1 .label_1 {
  width: 44rpx;
  height: 46rpx;
  margin: 2rpx 0 0 2rpx;
}
.page .group_1 .group_4 .group_5 .box_1 .text_5 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Pinyon Script-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 16rpx;
}
.page .group_1 .group_4 .group_5 .box_2 {
  width: 96rpx;
  height: 88rpx;
  margin: 26rpx 68rpx 0 138rpx;
}
.page .group_1 .group_4 .group_5 .box_2 .image-wrapper_2 {
  height: 48rpx;
  margin-left: 24rpx;
  width: 48rpx;
}
.page .group_1 .group_4 .group_5 .box_2 .image-wrapper_2 .label_2 {
  width: 46rpx;
  height: 42rpx;
  margin: 4rpx 0 0 2rpx;
}
.page .group_1 .group_4 .group_5 .box_2 .text_6 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Pinyon Script-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 16rpx;
}
.page .group_7 {
  position: relative;
  width: 750rpx;
  height: 1270rpx;
  margin-bottom: 2rpx;
}
.page .group_7 .text_7 {
  width: 210rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 30rpx;
  margin: 42rpx 0 0 26rpx;
}
.page .group_7 .text_8 {
  width: 200rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 40rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 50rpx 0 0 26rpx;
}
.page .group_7 .text_9 {
  width: 210rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 30rpx;
  margin: 58rpx 0 0 26rpx;
}
.page .group_7 .box_3 {
  width: 664rpx;
  height: 114rpx;
  margin: 32rpx 0 0 24rpx;
}
.page .group_7 .box_3 .box_4 {
  background-color: rgb(216, 244, 239);
  border-radius: 5px;
  width: 216rpx;
  height: 114rpx;
  border: 1px solid rgb(11, 206, 148);
}
.page .group_7 .box_3 .box_4 .text-group_3 {
  width: 138rpx;
  height: 84rpx;
  margin: 16rpx 0 0 40rpx;
}
.page .group_7 .box_3 .box_4 .text-group_3 .text-wrapper_1 {
  width: 96rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
  margin-left: 20rpx;
}
.page .group_7 .box_3 .box_4 .text-group_3 .text-wrapper_1 .text_10 {
  width: 96rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 36rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
}
.page .group_7 .box_3 .box_4 .text-group_3 .text-wrapper_1 .text_11 {
  width: 96rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 28rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .group_7 .box_3 .box_4 .text-group_3 .text_12 {
  width: 138rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 4rpx;
}
.page .group_7 .box_3 .text_13 {
  width: 170rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 64rpx 0 0 50rpx;
}
.page .group_7 .box_3 .text-wrapper_2 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
  margin: 16rpx 0 0 -132rpx;
}
.page .group_7 .box_3 .text-wrapper_2 .text_14 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 36rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
}
.page .group_7 .box_3 .text-wrapper_2 .text_15 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .group_7 .box_3 .text_16 {
  width: 138rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 64rpx 0 0 126rpx;
}
.page .group_7 .box_3 .text-wrapper_3 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
  margin: 16rpx 20rpx 0 -116rpx;
}
.page .group_7 .box_3 .text-wrapper_3 .text_17 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 36rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
}
.page .group_7 .box_3 .text-wrapper_3 .text_18 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .group_7 .box_5 {
  width: 624rpx;
  height: 84rpx;
  margin: 40rpx 0 0 64rpx;
}
.page .group_7 .box_5 .text_19 {
  width: 138rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 48rpx;
}
.page .group_7 .box_5 .text-wrapper_4 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
  margin-left: -118rpx;
}
.page .group_7 .box_5 .text-wrapper_4 .text_20 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 36rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
}
.page .group_7 .box_5 .text-wrapper_4 .text_21 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .group_7 .box_5 .text_22 {
  width: 138rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 48rpx 0 0 128rpx;
}
.page .group_7 .box_5 .text-wrapper_5 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
  margin-left: -118rpx;
}
.page .group_7 .box_5 .text-wrapper_5 .text_23 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 36rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
}
.page .group_7 .box_5 .text-wrapper_5 .text_24 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .group_7 .box_5 .text_25 {
  width: 138rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 48rpx 0 0 126rpx;
}
.page .group_7 .box_5 .text-wrapper_6 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
  margin: 0 20rpx 0 -116rpx;
}
.page .group_7 .box_5 .text-wrapper_6 .text_26 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 36rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
}
.page .group_7 .box_5 .text-wrapper_6 .text_27 {
  width: 96rpx;
  height: 50rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .group_7 .box_6 {
  background-color: rgb(247, 247, 247);
  width: 750rpx;
  height: 20rpx;
  margin-top: 56rpx;
}
.page .group_7 .box_7 {
  width: 700rpx;
  height: 28rpx;
  margin: 38rpx 0 0 24rpx;
}
.page .group_7 .box_7 .text_28 {
  width: 240rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.page .group_7 .box_7 .image-text_2 {
  width: 108rpx;
  height: 28rpx;
}
.page .group_7 .box_7 .image-text_2 .text-group_4 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .group_7 .box_7 .image-text_2 .thumbnail_5 {
  width: 12rpx;
  height: 20rpx;
  margin-top: 4rpx;
}
.page .group_7 .box_8 {
  width: 702rpx;
  height: 52rpx;
  margin: 48rpx 0 0 26rpx;
}
.page .group_7 .box_8 .image-text_3 {
  width: 174rpx;
  height: 52rpx;
}
.page .group_7 .box_8 .image-text_3 .label_3 {
  width: 52rpx;
  height: 52rpx;
}
.page .group_7 .box_8 .image-text_3 .text-group_5 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: Pinyon Script-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 12rpx;
}
.page .group_7 .box_8 .thumbnail_6 {
  width: 40rpx;
  height: 40rpx;
  margin-top: 6rpx;
}
.page .group_7 .box_9 {
  width: 702rpx;
  height: 52rpx;
  margin: 30rpx 0 0 26rpx;
}
.page .group_7 .box_9 .label_4 {
  width: 52rpx;
  height: 52rpx;
}
.page .group_7 .box_9 .text_29 {
  width: 140rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: Pinyon Script-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 12rpx 0 0 10rpx;
}
.page .group_7 .box_9 .thumbnail_7 {
  width: 40rpx;
  height: 40rpx;
  margin: 6rpx 0 0 460rpx;
}
.page .group_7 .paragraph_1 {
  width: 644rpx;
  height: 96rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 32rpx;
  margin: 22rpx 0 0 26rpx;
}
.page .group_7 .box_10 {
  background-color: rgb(247, 247, 247);
  height: 258rpx;
  width: 750rpx;
  margin: 16rpx 0 2rpx 0;
}
.page .group_7 .box_10 .group_8 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 134rpx;
  margin-top: 124rpx;
}
.page .group_7 .box_10 .group_8 .text-wrapper_7 {
  width: 160rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 54rpx 0 0 24rpx;
}
.page .group_7 .box_10 .group_8 .text-wrapper_7 .text_30 {
  width: 160rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .group_7 .box_10 .group_8 .text-wrapper_7 .text_31 {
  width: 160rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(220, 78, 76);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .group_7 .box_10 .group_8 .text-wrapper_8 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 90rpx;
  width: 284rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .group_7 .box_10 .group_8 .text-wrapper_8 .text_32 {
  width: 60rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
  margin: 30rpx 0 0 112rpx;
}
.page .group_7 .text-wrapper_9 {
  background-color: rgb(11, 206, 148);
  border-radius: 10px;
  height: 88rpx;
  width: 702rpx;
  position: absolute;
  left: 24rpx;
  top: 1282rpx;
}
.page .group_7 .text-wrapper_9 .text_33 {
  width: 128rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 48rpx;
  margin: 20rpx 0 0 288rpx;
}
.page .group_7 .box_11 {
  border-radius: 5px;
  position: absolute;
  left: 24rpx;
  top: 452rpx;
  width: 216rpx;
  height: 114rpx;
  border: 1px solid rgb(221, 221, 221);
}
.page .group_7 .box_12 {
  border-radius: 5px;
  position: absolute;
  left: 268rpx;
  top: 314rpx;
  width: 216rpx;
  height: 114rpx;
  border: 1px solid rgb(221, 221, 221);
}
.page .group_7 .box_13 {
  border-radius: 5px;
  position: absolute;
  left: 268rpx;
  top: 452rpx;
  width: 216rpx;
  height: 114rpx;
  border: 1px solid rgb(221, 221, 221);
}
.page .group_7 .box_14 {
  border-radius: 5px;
  position: absolute;
  left: 512rpx;
  top: 314rpx;
  width: 216rpx;
  height: 114rpx;
  border: 1px solid rgb(221, 221, 221);
}
.page .group_7 .box_15 {
  border-radius: 5px;
  position: absolute;
  left: 512rpx;
  top: 452rpx;
  width: 216rpx;
  height: 114rpx;
  border: 1px solid rgb(221, 221, 221);
}
