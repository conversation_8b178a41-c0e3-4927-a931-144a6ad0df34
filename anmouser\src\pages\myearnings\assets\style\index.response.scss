.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 100vw;
  height: 216.54vw;
  overflow: hidden;
  .block_1 {
    width: 100vw;
    height: 127.2vw;
    .box_1 {
      width: 100vw;
      height: 37.87vw;
      .section_1 {
        background-color: rgba(255, 255, 255, 1);
        width: 100vw;
        height: 8.54vw;
        .text_1 {
          width: 8.54vw;
          height: 4.8vw;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 3.2vw;
          font-family: Inter-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 40vw;
          margin: 1.86vw 0 0 4.26vw;
        }
        .thumbnail_1 {
          width: 4.8vw;
          height: 4.8vw;
          margin: 1.86vw 0 0 66.93vw;
        }
        .thumbnail_2 {
          width: 4.8vw;
          height: 4.8vw;
          margin: 1.86vw 0 0 0.8vw;
        }
        .thumbnail_3 {
          width: 5.07vw;
          height: 5.07vw;
          margin: 1.86vw 4vw 0 0.8vw;
        }
      }
      .section_2 {
        background-color: rgba(255, 255, 255, 1);
        width: 100vw;
        height: 14.4vw;
        .thumbnail_4 {
          width: 2.4vw;
          height: 4.54vw;
          margin: 5.06vw 0 0 4.8vw;
        }
        .text_2 {
          width: 17.07vw;
          height: 5.87vw;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 4.26vw;
          font-family: Inter-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 5.87vw;
          margin: 4.26vw 0 0 2.93vw;
        }
        .image_1 {
          width: 23.2vw;
          height: 8.54vw;
          margin: 2.93vw 1.6vw 0 48vw;
        }
      }
      .text-wrapper_1 {
        background-color: rgba(255, 255, 255, 1);
        width: 100vw;
        height: 11.74vw;
        margin: 0.26vw 0 2.93vw 0;
        .text_3 {
          width: 14.94vw;
          height: 3.74vw;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 3.73vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 4vw 0 0 17.6vw;
        }
        .text_4 {
          width: 18.67vw;
          height: 3.74vw;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 3.73vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 4vw 15.46vw 0 33.33vw;
        }
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      width: 93.6vw;
      height: 89.6vw;
      margin: 37.6vw 3.2vw 0 -96.8vw;
      .box_3 {
        width: 87.2vw;
        height: 4vw;
        margin: 4.8vw 0 0 3.2vw;
        .text-wrapper_2 {
          width: 25.87vw;
          height: 4vw;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          .text_5 {
            width: 25.87vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 3.46vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
          }
          .text_6 {
            width: 25.87vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 4vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
          }
        }
        .text_7 {
          width: 10.4vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
      }
      .text-wrapper_3 {
        width: 61.07vw;
        height: 4vw;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 4.26vw 0 0 3.2vw;
        .text_8 {
          width: 61.07vw;
          height: 4vw;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text_9 {
          width: 61.07vw;
          height: 4vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 4vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
      }
      .text_10 {
        width: 35.2vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 4.26vw 0 0 3.2vw;
      }
      .text_11 {
        width: 35.2vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 4.26vw 0 0 3.2vw;
      }
      .text_12 {
        width: 55.47vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 4.26vw 0 0 3.2vw;
      }
      .box_4 {
        background-color: rgba(246, 246, 246, 1);
        border-radius: 10px;
        height: 23.74vw;
        width: 87.47vw;
        margin: 7.2vw 0 0 3.2vw;
        .box_5 {
          width: 82.14vw;
          height: 9.6vw;
          margin: 3.46vw 0 0 3.2vw;
          .text-group_1 {
            width: 28vw;
            height: 9.6vw;
            .text_13 {
              width: 28vw;
              height: 3.47vw;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 3.46vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_14 {
              width: 13.87vw;
              height: 3.47vw;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 3.46vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin-top: 2.67vw;
            }
          }
          .text-group_2 {
            width: 10.67vw;
            height: 9.6vw;
            .text_15 {
              width: 9.87vw;
              height: 3.47vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 3.46vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin-left: 0.8vw;
            }
            .text_16 {
              width: 10.67vw;
              height: 3.47vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 3.46vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin-top: 2.67vw;
            }
          }
        }
        .box_6 {
          width: 82.14vw;
          height: 3.47vw;
          margin: 2.93vw 0 4.26vw 3.2vw;
          .text_17 {
            width: 13.87vw;
            height: 3.47vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.46vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
          }
          .text-wrapper_4 {
            width: 34.4vw;
            height: 3.47vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            .text_18 {
              width: 34.4vw;
              height: 3.47vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 3.46vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_19 {
              width: 34.4vw;
              height: 3.47vw;
              overflow-wrap: break-word;
              color: rgba(24, 200, 99, 1);
              font-size: 3.46vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
          }
        }
      }
      .text-wrapper_5 {
        width: 31.47vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 3.73vw 0 0 3.2vw;
        .text_20 {
          width: 31.47vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text_21 {
          width: 31.47vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
      }
      .text-wrapper_6 {
        width: 40.54vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 3.2vw 0 4.53vw 3.2vw;
        .text_22 {
          width: 40.54vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text_23 {
          width: 40.54vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
      }
    }
  }
}
