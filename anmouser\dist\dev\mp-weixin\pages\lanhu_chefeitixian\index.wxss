@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(245, 245, 245);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .box_1 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 172rpx;
}
.page .box_1 .group_1 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .group_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .group_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .group_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .group_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .group_2 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .group_2 .text_2 {
  width: 64rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 76rpx;
}
.page .box_1 .group_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 424rpx;
}
.page .box_2 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 380rpx;
  margin-top: -2rpx;
}
.page .box_2 .group_3 {
  width: 128rpx;
  height: 24rpx;
  margin: 30rpx 0 0 26rpx;
}
.page .box_2 .group_3 .block_1 {
  background-color: rgb(255, 255, 255);
  width: 22rpx;
  height: 22rpx;
}
.page .box_2 .group_3 .text_3 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .text-wrapper_1 {
  width: 310rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 26rpx 0 230rpx 24rpx;
}
.page .box_2 .text-wrapper_1 .text_4 {
  width: 310rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 40rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .text-wrapper_1 .text_5 {
  width: 310rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 70rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 702rpx;
  height: 462rpx;
  margin: 220rpx 0 0 24rpx;
}
.page .box_3 .text_6 {
  width: 112rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
  margin: 38rpx 0 0 30rpx;
}
.page .box_3 .section_1 {
  width: 650rpx;
  height: 40rpx;
  margin: 62rpx 0 0 28rpx;
}
.page .box_3 .section_1 .image-text_1 {
  width: 196rpx;
  height: 40rpx;
}
.page .box_3 .section_1 .image-text_1 .thumbnail_4 {
  width: 40rpx;
  height: 40rpx;
}
.page .box_3 .section_1 .image-text_1 .text-group_1 {
  width: 140rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .box_3 .section_1 .image-text_2 {
  width: 152rpx;
  height: 40rpx;
}
.page .box_3 .section_1 .image-text_2 .text-group_2 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .box_3 .section_1 .image-text_2 .thumbnail_5 {
  width: 40rpx;
  height: 40rpx;
}
.page .box_3 .section_2 {
  width: 650rpx;
  height: 40rpx;
  margin: 52rpx 0 0 28rpx;
}
.page .box_3 .section_2 .image-text_3 {
  width: 590rpx;
  height: 40rpx;
}
.page .box_3 .section_2 .image-text_3 .thumbnail_6 {
  width: 40rpx;
  height: 40rpx;
}
.page .box_3 .section_2 .image-text_3 .text-group_3 {
  width: 534rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .box_3 .section_2 .block_2 {
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  border: 1px solid rgb(197, 197, 197);
}
.page .box_3 .section_3 {
  width: 654rpx;
  height: 50rpx;
  margin: 44rpx 0 0 24rpx;
}
.page .box_3 .section_3 .image-text_4 {
  width: 234rpx;
  height: 48rpx;
}
.page .box_3 .section_3 .image-text_4 .label_1 {
  width: 48rpx;
  height: 48rpx;
}
.page .box_3 .section_3 .image-text_4 .text-group_4 {
  width: 174rpx;
  height: 36rpx;
  margin-top: 10rpx;
}
.page .box_3 .section_3 .image-text_4 .text-group_4 .text_7 {
  width: 174rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_3 .section_3 .image-text_4 .text-group_4 .text_8 {
  width: 48rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: #000;
  font-size: 24rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: -22rpx 0 0 112rpx;
}
.page .box_3 .section_3 .block_3 {
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  border: 1px solid rgb(197, 197, 197);
  margin-top: 10rpx;
}
.page .box_3 .section_4 {
  width: 654rpx;
  height: 48rpx;
  margin: 36rpx 0 32rpx 24rpx;
}
.page .box_3 .section_4 .image-text_5 {
  width: 586rpx;
  height: 48rpx;
}
.page .box_3 .section_4 .image-text_5 .label_2 {
  width: 48rpx;
  height: 48rpx;
}
.page .box_3 .section_4 .image-text_5 .text-group_5 {
  position: relative;
  width: 526rpx;
  height: 32rpx;
  margin-top: 10rpx;
}
.page .box_3 .section_4 .image-text_5 .text-group_5 .text_9 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 4rpx 0 0 442rpx;
}
.page .box_3 .section_4 .image-text_5 .text-group_5 .text_10 {
  position: absolute;
  left: 0;
  top: 0;
  width: 526rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_3 .section_4 .box_4 {
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  border: 1px solid rgb(197, 197, 197);
  margin-top: 8rpx;
}
.page .text-wrapper_2 {
  background-color: rgb(11, 206, 148);
  border-radius: 50px;
  height: 86rpx;
  width: 690rpx;
  margin: 236rpx 0 70rpx 30rpx;
}
.page .text-wrapper_2 .text_11 {
  width: 60rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 316rpx;
}
.page .box_5 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 380rpx;
  width: 702rpx;
  position: absolute;
  left: 24rpx;
  top: 370rpx;
}
.page .box_5 .text-wrapper_3 {
  width: 112rpx;
  height: 20rpx;
  margin: 36rpx 0 0 34rpx;
}
.page .box_5 .text-wrapper_3 .text_12 {
  width: 112rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
}
.page .box_5 .text-wrapper_4 {
  width: 640rpx;
  height: 30rpx;
  margin: 72rpx 0 0 28rpx;
}
.page .box_5 .text-wrapper_4 .text_13 {
  width: 40rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 40rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
}
.page .box_5 .text-wrapper_4 .text_14 {
  width: 280rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 40rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
  margin-left: 6rpx;
}
.page .box_5 .text-wrapper_4 .text_15 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
  margin: 12rpx 0 0 218rpx;
}
.page .box_5 .box_6 {
  width: 632rpx;
  height: 18rpx;
  margin: 62rpx 0 0 34rpx;
}
.page .box_5 .box_6 .text-wrapper_5 {
  width: 204rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
}
.page .box_5 .box_6 .text-wrapper_5 .text_16 {
  width: 204rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
}
.page .box_5 .box_6 .text-wrapper_5 .text_17 {
  width: 204rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
}
.page .box_5 .box_6 .text_18 {
  width: 242rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
}
.page .box_5 .box_7 {
  width: 224rpx;
  height: 24rpx;
  margin: 70rpx 0 48rpx 34rpx;
}
.page .box_5 .box_7 .text-wrapper_6 {
  width: 224rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
}
.page .box_5 .box_7 .text-wrapper_6 .text_19 {
  width: 224rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
}
.page .box_5 .box_7 .text-wrapper_6 .text_20 {
  width: 224rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 60rpx;
}
