<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试我的收益页面重命名</title>
</head>
<body>
    <h1>我的收益页面重命名测试</h1>
    <p>测试新的我的收益页面路径：</p>
    <ul>
        <li>原路径：pages/lanhu_wodeshouyi_3/index</li>
        <li>新路径：pages/myearnings/index</li>
        <li>页面名称：myearnings</li>
    </ul>
    
    <h2>验证项目：</h2>
    <ul>
        <li>✅ 页面目录已重命名：lanhu_wodeshouyi_3 → myearnings</li>
        <li>✅ pages.json 配置已更新</li>
        <li>✅ 静态资源路径已更新</li>
        <li>✅ 静态资源目录已移动</li>
        <li>✅ 保持了之前修复的行高样式（27个行高问题已修复）</li>
    </ul>
    
    <h2>页面访问方式：</h2>
    <p>页面应该可以通过以下方式访问：</p>
    <code>uni.navigateTo({ url: '/pages/myearnings/index' })</code>
    
    <h2>页面特点：</h2>
    <ul>
        <li>页面标题：我的收益</li>
        <li>包含订单收益和推荐费收益两个标签</li>
        <li>所有行高问题已修复（24rpx、26rpx、28rpx、30rpx等合理值）</li>
        <li>静态资源路径：/static/myearnings/</li>
    </ul>
</body>
</html>
