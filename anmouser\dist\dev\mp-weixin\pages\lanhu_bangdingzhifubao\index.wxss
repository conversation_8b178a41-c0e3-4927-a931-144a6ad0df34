@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(244, 247, 254);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .box_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 172rpx;
}
.page .box_1 .block_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .block_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .block_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .block_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .block_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .block_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .block_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .box_1 .block_2 .text_2 {
  width: 224rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .box_1 .block_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 264rpx;
}
.page .box_2 {
  background-color: rgb(37, 103, 232);
  height: 1452rpx;
  width: 750rpx;
  position: absolute;
  left: 0;
  top: 172rpx;
}
.page .box_2 .image-wrapper_1 {
  height: 1384rpx;
  background: url(/static/lanhu_bangdingzhifubao/FigmaDDSSlicePNG36da2483519509699e351382dba731c5.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 2080rpx;
  margin: -324rpx 0 0 -642rpx;
}
.page .box_2 .image-wrapper_1 .image_2 {
  width: 1706rpx;
  height: 1064rpx;
  margin: 162rpx 0 0 190rpx;
}
.page .box_3 {
  height: 1230rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
  width: 2070rpx;
  position: absolute;
  left: -630rpx;
  top: 4rpx;
}
.page .box_3 .section_1 {
  width: 2070rpx;
  height: 1230rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABOEAAAMoCAYAAACJZfQZAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAABAsSURBVHgB7dgxkeRAEETR6ggBaAYjJjcQlsFBWAgHQRAW2kLJK0WM1X7Les/IzyFHtSRnAQAAAABbHJ+mAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYDGSzO5VAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8aySZ3asAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHjWSHJ2fwoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgGeNJGf3pwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAx7klyFgAAAACwxegDbnavAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgMW4J8lZAAAAAMAWRx9ws/u3AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYDHuSfIuAAAAAGCLow+42X0VAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALEaS2f0qAAAAAGCL49PfAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgMVIMrvfBQAAAABsMe7pI+5VAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAsBhJZvcqAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACeNZLM7lUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADxrJDm7/woAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgGeNJLN7FQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACzGPUnOAgAAAAC2GH3Aze5VAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAsBj3JHkXAAAAALDF0Qfc7P4pAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWIx7krwLAAAAANji6ANudl8FAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAi5Fkdr8KAAAAANji+PS3AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYDGSzO53AQAAAABbjHv6iHsVAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALEaS2b0KAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYL//QtZiC2B1FbEAAAAASUVORK5CYII=) 100% no-repeat;
  background-size: 100% 100%;
}
.page .box_3 .section_1 .image-text_1 {
  width: 362rpx;
  height: 240rpx;
  margin: 264rpx 0 0 826rpx;
}
.page .box_3 .section_1 .image-text_1 .group_1 {
  background-color: rgb(255, 255, 255);
  width: 168rpx;
  height: 168rpx;
  margin-left: 98rpx;
}
.page .box_3 .section_1 .image-text_1 .text-group_1 {
  width: 362rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 52rpx;
  letter-spacing: -1px;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 40rpx;
}
.page .box_3 .section_1 .section_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 702rpx;
  height: 554rpx;
  margin: 56rpx 0 116rpx 654rpx;
}
.page .box_3 .section_1 .section_2 .group_2 {
  box-shadow: 0px 1px 2px 0px rgba(228, 229, 231, 0.24);
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 92rpx;
  border: 1px solid rgb(237, 241, 243);
  width: 654rpx;
  margin: 78rpx 0 0 28rpx;
}
.page .box_3 .section_1 .section_2 .group_2 .text-wrapper_1 {
  width: 586rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  font-size: 0;
  letter-spacing: -1px;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 278rpx;
  margin: 26rpx 0 0 28rpx;
}
.page .box_3 .section_1 .section_2 .group_2 .text-wrapper_1 .text_3 {
  width: 586rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgb(26, 28, 30);
  font-size: 28rpx;
  letter-spacing: -1px;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 278rpx;
}
.page .box_3 .section_1 .section_2 .group_2 .text-wrapper_1 .text_4 {
  width: 586rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  letter-spacing: -1px;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 278rpx;
}
.page .box_3 .section_1 .section_2 .group_3 {
  box-shadow: 0px 1px 2px 0px rgba(228, 229, 231, 0.24);
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 92rpx;
  border: 1px solid rgb(237, 241, 243);
  width: 654rpx;
  margin: 36rpx 0 0 28rpx;
}
.page .box_3 .section_1 .section_2 .group_3 .text-wrapper_2 {
  width: 546rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  font-size: 0;
  letter-spacing: -1px;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 278rpx;
  margin: 26rpx 0 0 28rpx;
}
.page .box_3 .section_1 .section_2 .group_3 .text-wrapper_2 .text_5 {
  width: 546rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgb(26, 28, 30);
  font-size: 28rpx;
  letter-spacing: -1px;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 278rpx;
}
.page .box_3 .section_1 .section_2 .group_3 .text-wrapper_2 .text_6 {
  width: 546rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  letter-spacing: -1px;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 278rpx;
}
.page .box_3 .section_1 .section_2 .text-wrapper_3 {
  box-shadow: 0px 1px 2px 0px rgba(37, 62, 167, 0.48);
  border-radius: 10px;
  height: 96rpx;
  border: 1px gradient;
  width: 654rpx;
  margin: 86rpx 0 74rpx 24rpx;
}
.page .box_3 .section_1 .section_2 .text-wrapper_3 .text_7 {
  width: 112rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  letter-spacing: -1px;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 278rpx;
  margin: 28rpx 0 0 272rpx;
}
