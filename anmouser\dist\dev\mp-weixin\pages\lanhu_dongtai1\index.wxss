@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(247, 247, 247);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .box_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 172rpx;
}
.page .box_1 .block_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .block_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .block_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .block_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .block_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .block_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .block_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .box_1 .block_2 .text_2 {
  width: 64rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .box_1 .block_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 424rpx;
}
.page .box_2 {
  width: 750rpx;
  height: 1326rpx;
}
.page .box_2 .box_3 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 120rpx;
}
.page .box_2 .box_3 .group_1 {
  background-color: rgb(243, 243, 244);
  border-radius: 32px;
  width: 550rpx;
  height: 64rpx;
  margin: 28rpx 0 0 24rpx;
}
.page .box_2 .box_3 .group_1 .image-text_1 {
  width: 188rpx;
  height: 44rpx;
  margin: 10rpx 0 0 44rpx;
}
.page .box_2 .box_3 .group_1 .image-text_1 .box_4 {
  background-color: rgb(153, 153, 153);
  width: 24rpx;
  height: 26rpx;
  margin-top: 8rpx;
}
.page .box_2 .box_3 .group_1 .image-text_1 .text-group_1 {
  width: 150rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 30rpx;
}
.page .box_2 .box_3 .group_2 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  width: 136rpx;
  height: 56rpx;
  border: 1px solid rgb(11, 206, 148);
  margin: 30rpx 22rpx 0 18rpx;
}
.page .box_2 .box_3 .group_2 .image-text_2 {
  width: 94rpx;
  height: 34rpx;
  margin: 12rpx 0 0 22rpx;
}
.page .box_2 .box_3 .group_2 .image-text_2 .group_3 {
  background-color: rgb(255, 255, 255);
  width: 20rpx;
  height: 20rpx;
  margin-top: 6rpx;
}
.page .box_2 .box_3 .group_2 .image-text_2 .text-group_2 {
  width: 72rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
}
.page .box_2 .text-wrapper_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 72rpx;
}
.page .box_2 .text-wrapper_1 .text_3 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 0 0 132rpx;
}
.page .box_2 .text-wrapper_1 .text_4 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 172rpx 0 334rpx;
}
.page .box_2 .image-text_3 {
  width: 360rpx;
  height: 160rpx;
  margin: 354rpx 0 620rpx 194rpx;
}
.page .box_2 .image-text_3 .group_4 {
  position: relative;
  width: 360rpx;
  height: 126rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
}
.page .box_2 .image-text_3 .group_4 .image_2 {
  width: 32rpx;
  height: 60rpx;
  margin: -4rpx 0 0 30rpx;
}
.page .box_2 .image-text_3 .group_4 .image_3 {
  width: 22rpx;
  height: 42rpx;
  margin: 4rpx 0 0 232rpx;
}
.page .box_2 .image-text_3 .group_4 .thumbnail_5 {
  width: 18rpx;
  height: 32rpx;
  margin: 30rpx 12rpx 0 14rpx;
}
.page .box_2 .image-text_3 .group_4 .box_5 {
  position: absolute;
  left: 60rpx;
  top: -126rpx;
  width: 196rpx;
  height: 184rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
}
.page .box_2 .image-text_3 .group_4 .box_5 .thumbnail_6 {
  width: 20rpx;
  height: 20rpx;
  margin: 6rpx 0 0 116rpx;
}
.page .box_2 .image-text_3 .group_4 .box_5 .group_5 {
  background-color: rgb(191, 191, 191);
  width: 68rpx;
  height: 22rpx;
  margin: 122rpx 0 14rpx 120rpx;
}
.page .box_2 .image-text_3 .group_4 .box_5 .group_6 {
  background-color: rgb(255, 255, 255);
  position: absolute;
  left: 68rpx;
  top: 32rpx;
  width: 122rpx;
  height: 138rpx;
}
.page .box_2 .image-text_3 .group_4 .box_5 .group_6 .group_7 {
  background-color: rgb(241, 241, 241);
  width: 96rpx;
  height: 8rpx;
  margin: 18rpx 0 0 12rpx;
}
.page .box_2 .image-text_3 .group_4 .box_5 .group_6 .group_8 {
  background-color: rgb(241, 241, 241);
  width: 96rpx;
  height: 8rpx;
  margin: 6rpx 0 0 12rpx;
}
.page .box_2 .image-text_3 .group_4 .box_5 .group_6 .group_9 {
  background-color: rgb(241, 241, 241);
  width: 52rpx;
  height: 8rpx;
  margin: 8rpx 0 0 12rpx;
}
.page .box_2 .image-text_3 .group_4 .box_5 .group_6 .group_10 {
  background-color: rgb(241, 241, 241);
  width: 22rpx;
  height: 8rpx;
  margin: 54rpx 0 20rpx 12rpx;
}
.page .box_2 .image-text_3 .text-group_3 {
  width: 196rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin-left: 82rpx;
}
.page .box_6 {
  background-color: rgb(255, 255, 255);
  height: 126rpx;
  width: 750rpx;
  margin: -2rpx 0 2rpx 0;
}
.page .box_6 .text-wrapper_2 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 88rpx;
  width: 690rpx;
  margin: 20rpx 0 0 30rpx;
}
.page .box_6 .text-wrapper_2 .text_5 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 286rpx;
}
