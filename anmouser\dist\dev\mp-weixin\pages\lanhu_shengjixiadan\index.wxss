@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .block_1 {
  width: 750rpx;
  height: 1624rpx;
}
.page .block_1 .group_1 {
  width: 750rpx;
  height: 1518rpx;
}
.page .block_1 .group_1 .group_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .block_1 .group_1 .group_2 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .block_1 .group_1 .group_2 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .block_1 .group_1 .group_2 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .block_1 .group_1 .group_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .block_1 .group_1 .group_3 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .block_1 .group_1 .group_3 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .block_1 .group_1 .group_3 .text_2 {
  width: 64rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .block_1 .group_1 .group_3 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 424rpx;
}
.page .block_1 .group_1 .group_4 {
  height: 200rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAXcAAABkCAYAAAB99XB/AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFuSURBVHgB7dRBFQAQAEAxXNWXSyV6/LeF2Nz3vAFAyhoA5MgdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEEfr/IDNP1pi14AAAAASUVORK5CYII=) 100% no-repeat;
  background-size: 100% 100%;
  width: 750rpx;
}
.page .block_1 .group_1 .group_4 .group_5 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  position: relative;
  width: 702rpx;
  height: 206rpx;
  -webkit-box-pack: flex-center;
  -webkit-justify-content: flex-center;
          justify-content: flex-center;
  margin: 78rpx 0 0 24rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .image_2 {
  width: 152rpx;
  height: 152rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .box_1 {
  width: 198rpx;
  height: 138rpx;
  margin: 28rpx 0 0 24rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .box_1 .text_3 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .box_1 .text-group_1 {
  width: 198rpx;
  height: 84rpx;
  margin-top: 24rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .box_1 .text-group_1 .text-wrapper_1 {
  width: 198rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 200rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .box_1 .text-group_1 .text-wrapper_1 .text_4 {
  width: 198rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 16rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 200rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .box_1 .text-group_1 .text-wrapper_1 .text_5 {
  width: 198rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .box_1 .text-group_1 .text-wrapper_1 .text_6 {
  width: 198rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(152, 152, 152);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .box_1 .text-group_1 .text_7 {
  width: 144rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(152, 152, 152);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 24rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .box_2 {
  border-radius: 2px;
  width: 40rpx;
  height: 40rpx;
  border: 1px solid rgb(11, 206, 148);
  margin: 82rpx 0 0 144rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .text_8 {
  width: 12rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 88rpx 0 0 18rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .thumbnail_5 {
  width: 40rpx;
  height: 40rpx;
  margin: 82rpx 24rpx 0 26rpx;
}
.page .block_1 .group_1 .group_4 .group_5 .image_3 {
  position: absolute;
  left: 550rpx;
  top: 88rpx;
  width: 122rpx;
  height: 26rpx;
}
.page .block_1 .group_1 .list_1 {
  width: 702rpx;
  height: 176rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
  margin: 104rpx 0 0 24rpx;
}
.page .block_1 .group_1 .list_1 .text-wrapper_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  width: 702rpx;
  height: 88rpx;
}
.page .block_1 .group_1 .list_1 .text-wrapper_2 .text_9 {
  width: 168rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 24rpx;
}
.page .block_1 .group_1 .list_1 .text-wrapper_2 .text_10 {
  width: 266rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 24rpx 0 0;
}
.page .block_1 .group_1 .group_6 {
  width: 702rpx;
  height: 160rpx;
  margin: 20rpx 0 686rpx 24rpx;
}
.page .block_1 .group_1 .group_6 .group_7 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px 8px 0px 0px;
  width: 702rpx;
  height: 80rpx;
}
.page .block_1 .group_1 .group_6 .group_7 .thumbnail_6 {
  width: 40rpx;
  height: 40rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .block_1 .group_1 .group_6 .group_7 .image-text_1 {
  width: 594rpx;
  height: 40rpx;
  margin: 20rpx 24rpx 0 0;
}
.page .block_1 .group_1 .group_6 .group_7 .image-text_1 .text-group_2 {
  width: 534rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .block_1 .group_1 .group_6 .group_7 .image-text_1 .box_3 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 40rpx;
}
.page .block_1 .group_1 .group_6 .group_8 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 0px 8px 8px;
  width: 702rpx;
  height: 80rpx;
}
.page .block_1 .group_1 .group_6 .group_8 .image-text_2 {
  width: 586rpx;
  height: 40rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .block_1 .group_1 .group_6 .group_8 .image-text_2 .thumbnail_7 {
  width: 40rpx;
  height: 40rpx;
}
.page .block_1 .group_1 .group_6 .group_8 .image-text_2 .text-group_3 {
  width: 526rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .block_1 .group_1 .group_6 .group_8 .image-text_2 .text-group_3 .text_11 {
  width: 526rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .group_6 .group_8 .image-text_2 .text-group_3 .text_12 {
  width: 526rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .group_6 .group_8 .group_9 {
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  border: 1px solid rgb(197, 197, 197);
  margin: 20rpx 26rpx 0 26rpx;
}
.page .block_1 .group_10 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
  margin: 1516rpx 0 0 -750rpx;
}
.page .block_1 .group_10 .text-wrapper_3 {
  width: 148rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 38rpx 0 0 24rpx;
}
.page .block_1 .group_10 .text-wrapper_3 .text_13 {
  width: 148rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_10 .text-wrapper_3 .text_14 {
  width: 148rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_10 .text-wrapper_4 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 74rpx;
  width: 182rpx;
  margin: 18rpx 24rpx 0 0;
}
.page .block_1 .group_10 .text-wrapper_4 .text_15 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 32rpx;
}
