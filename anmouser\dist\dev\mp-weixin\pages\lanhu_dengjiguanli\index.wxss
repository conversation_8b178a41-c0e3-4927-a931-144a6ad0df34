@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(245, 245, 245);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .block_1 {
  background-color: rgb(47, 47, 47);
  width: 750rpx;
  height: 172rpx;
}
.page .block_1 .block_2 {
  background-color: rgb(47, 47, 47);
  width: 750rpx;
  height: 64rpx;
}
.page .block_1 .block_2 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .block_1 .block_2 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .block_1 .block_2 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .block_1 .block_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .block_1 .block_3 {
  background-color: rgb(47, 47, 47);
  width: 750rpx;
  height: 108rpx;
}
.page .block_1 .block_3 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 76rpx;
}
.page .block_1 .block_3 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .block_4 {
  width: 750rpx;
  height: 1454rpx;
  margin-bottom: 2rpx;
}
.page .block_4 .block_5 {
  background-color: rgb(47, 47, 47);
  height: 276rpx;
  width: 750rpx;
}
.page .block_4 .block_5 .section_1 {
  background-image: -webkit-linear-gradient(315deg, rgb(237, 213, 175) 0, rgb(207, 174, 126) 100%);
  background-image: linear-gradient(135deg, rgb(237, 213, 175) 0, rgb(207, 174, 126) 100%);
  border-radius: 10px;
  position: relative;
  width: 700rpx;
  height: 264rpx;
  margin: 84rpx 0 0 24rpx;
}
.page .block_4 .block_5 .section_1 .text-wrapper_1 {
  width: 160rpx;
  height: 122rpx;
  margin: 70rpx 0 0 54rpx;
}
.page .block_4 .block_5 .section_1 .text-wrapper_1 .text_3 {
  width: 160rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(89, 40, 3);
  font-size: 40rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
}
.page .block_4 .block_5 .section_1 .text-wrapper_1 .text_4 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(89, 40, 3);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin-top: 74rpx;
}
.page .block_4 .block_5 .section_1 .section_2 {
  height: 266rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
  margin-left: 46rpx;
  width: 440rpx;
}
.page .block_4 .block_5 .section_1 .section_2 .box_1 {
  background-color: rgb(195, 124, 95);
  border-radius: 30px;
  height: 14rpx;
  width: 596rpx;
  margin: 130rpx 0 0 -206rpx;
}
.page .block_4 .block_5 .section_1 .section_2 .box_1 .box_2 {
  background-color: rgb(61, 19, 2);
  border-radius: 30px;
  width: 256rpx;
  height: 14rpx;
}
.page .block_4 .block_5 .section_1 .text_5 {
  position: absolute;
  left: 226rpx;
  top: 82rpx;
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(89, 40, 3);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
}
.page .block_4 .block_6 {
  width: 770rpx;
  height: 138rpx;
  margin: 92rpx 0 0 24rpx;
}
.page .block_4 .block_6 .block_7 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 138rpx;
  width: 702rpx;
}
.page .block_4 .block_6 .block_7 .list_1 {
  width: 534rpx;
  height: 72rpx;
  margin: 28rpx 0 0 48rpx;
}
.page .block_4 .block_6 .block_7 .list_1 .text-wrapper_2 {
  width: 96rpx;
  height: 72rpx;
  margin-right: 116rpx;
}
.page .block_4 .block_6 .block_7 .list_1 .text-wrapper_2 .text_6 {
  width: 22rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(89, 40, 3);
  font-size: 36rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-left: 38rpx;
}
.page .block_4 .block_6 .block_7 .list_1 .text-wrapper_2 .text_7 {
  width: 120rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 28rpx;
}
.page .block_4 .block_6 .text_8 {
  width: 120rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 82rpx 0 0 -52rpx;
}
.page .block_4 .image-text_1 {
  width: 146rpx;
  height: 22rpx;
  margin: 48rpx 0 0 24rpx;
}
.page .block_4 .image-text_1 .text-group_1 {
  width: 120rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .block_4 .image-text_1 .thumbnail_4 {
  width: 22rpx;
  height: 22rpx;
}
.page .block_4 .list_2 {
  width: 702rpx;
  height: 704rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
  margin: 24rpx 0 150rpx 24rpx;
}
.page .block_4 .list_2 .text-wrapper_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  width: 702rpx;
  height: 88rpx;
}
.page .block_4 .list_2 .text-wrapper_3 .text_9 {
  width: 104rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 34rpx 0 0 62rpx;
}
.page .block_4 .list_2 .text-wrapper_3 .text_10 {
  width: 106rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 34rpx 0 0 96rpx;
}
.page .block_4 .list_2 .text-wrapper_3 .text_11 {
  width: 78rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 34rpx 0 0 84rpx;
}
.page .block_4 .list_2 .text-wrapper_3 .text_12 {
  width: 122rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 34rpx 38rpx 0 84rpx;
}
