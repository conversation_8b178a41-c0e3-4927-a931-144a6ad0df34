@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 1586rpx;
  overflow: hidden;
}
.page .box_1 {
  width: 750rpx;
  height: 458rpx;
}
.page .box_1 .box_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .box_2 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .box_2 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .box_2 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .box_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .box_3 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .box_3 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .box_1 .box_3 .text_2 {
  width: 64rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .box_1 .box_3 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 424rpx;
}
.page .box_1 .box_4 {
  background-image: -webkit-linear-gradient(269deg, rgb(62, 200, 174) 0, rgb(62, 200, 174) 100%);
  background-image: linear-gradient(181deg, rgb(62, 200, 174) 0, rgb(62, 200, 174) 100%);
  height: 188rpx;
  margin-bottom: 98rpx;
  width: 750rpx;
}
.page .box_1 .box_4 .section_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  width: 702rpx;
  height: 196rpx;
  margin: 68rpx 0 0 24rpx;
}
.page .box_1 .box_4 .section_1 .image-text_1 {
  width: 568rpx;
  height: 140rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_1 .box_4 .section_1 .image-text_1 .image_2 {
  width: 140rpx;
  height: 140rpx;
}
.page .box_1 .box_4 .section_1 .image-text_1 .text-group_1 {
  width: 404rpx;
  height: 138rpx;
}
.page .box_1 .box_4 .section_1 .image-text_1 .text-group_1 .text_3 {
  width: 198rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.page .box_1 .box_4 .section_1 .image-text_1 .text-group_1 .box_5 {
  width: 404rpx;
  height: 36rpx;
  margin-top: 22rpx;
}
.page .box_1 .box_4 .section_1 .image-text_1 .text-group_1 .box_5 .text-wrapper_1 {
  width: 80rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1;
}
.page .box_1 .box_4 .section_1 .image-text_1 .text-group_1 .box_5 .text-wrapper_1 .text_4 {
  width: 80rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
}
.page .box_1 .box_4 .section_1 .image-text_1 .text-group_1 .box_5 .text-wrapper_1 .text_5 {
  width: 80rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 36rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 36rpx;
}
.page .box_1 .box_4 .section_1 .image-text_1 .text-group_1 .box_5 .text_6 {
  width: 312rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 10rpx;
}
.page .box_1 .box_4 .section_1 .image-text_1 .text-group_1 .text_7 {
  width: 216rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 26rpx 0 0 4rpx;
}
.page .box_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 702rpx;
  height: 462rpx;
  margin: 510rpx 0 0 24rpx;
}
.page .box_6 .section_2 {
  background-color: rgb(246, 246, 246);
  border-radius: 10px;
  height: 298rpx;
  width: 650rpx;
  -webkit-box-pack: flex-center;
  -webkit-justify-content: flex-center;
          justify-content: flex-center;
  margin: 34rpx 0 0 26rpx;
}
.page .box_6 .section_2 .group_1 {
  width: 412rpx;
  height: 68rpx;
  margin: 36rpx 0 0 32rpx;
}
.page .box_6 .section_2 .group_1 .text_8 {
  width: 182rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 2rpx;
}
.page .box_6 .section_2 .group_1 .text-wrapper_2 {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  height: 68rpx;
  width: 188rpx;
}
.page .box_6 .section_2 .group_1 .text-wrapper_2 .text_9 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 20rpx 0 0 38rpx;
}
.page .box_6 .section_2 .text-wrapper_3 {
  width: 146rpx;
  height: 26rpx;
  margin: 132rpx 0 36rpx 442rpx;
}
.page .box_6 .section_2 .text-wrapper_3 .text_10 {
  width: 146rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
}
.page .box_6 .section_3 {
  width: 426rpx;
  height: 40rpx;
  margin: 28rpx 0 62rpx 24rpx;
}
.page .box_6 .section_3 .text-wrapper_4 {
  background-color: rgb(11, 206, 148);
  border-radius: 50px;
  height: 40rpx;
  width: 66rpx;
}
.page .box_6 .section_3 .text-wrapper_4 .text_11 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 8rpx 0 0 22rpx;
}
.page .box_6 .section_3 .text-wrapper_5 {
  background-color: rgb(232, 232, 232);
  border-radius: 50px;
  height: 40rpx;
  margin-left: 24rpx;
  width: 66rpx;
}
.page .box_6 .section_3 .text-wrapper_5 .text_12 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 8rpx 0 0 22rpx;
}
.page .box_6 .section_3 .text-wrapper_6 {
  background-color: rgb(232, 232, 232);
  border-radius: 50px;
  height: 40rpx;
  margin-left: 24rpx;
  width: 66rpx;
}
.page .box_6 .section_3 .text-wrapper_6 .text_13 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 8rpx 0 0 22rpx;
}
.page .box_6 .section_3 .text-wrapper_7 {
  background-color: rgb(232, 232, 232);
  border-radius: 50px;
  height: 40rpx;
  margin-left: 24rpx;
  width: 66rpx;
}
.page .box_6 .section_3 .text-wrapper_7 .text_14 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 8rpx 0 0 22rpx;
}
.page .box_6 .section_3 .text-wrapper_8 {
  background-color: rgb(232, 232, 232);
  border-radius: 50px;
  height: 40rpx;
  margin-left: 24rpx;
  width: 66rpx;
}
.page .box_6 .section_3 .text-wrapper_8 .text_15 {
  width: 24rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 8rpx 0 0 22rpx;
}
.page .box_7 {
  background-color: rgb(255, 255, 255);
  height: 126rpx;
  margin-top: 30rpx;
  width: 750rpx;
}
.page .box_7 .text-wrapper_9 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 88rpx;
  width: 690rpx;
  margin: 20rpx 0 0 30rpx;
}
.page .box_7 .text-wrapper_9 .text_16 {
  width: 60rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 30rpx;
  margin: 30rpx 0 0 316rpx;
}
.page .box_8 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 492rpx;
  width: 702rpx;
  position: absolute;
  left: 24rpx;
  top: 456rpx;
}
.page .box_8 .text-wrapper_10 {
  width: 120rpx;
  height: 30rpx;
  margin: 36rpx 0 0 30rpx;
}
.page .box_8 .text-wrapper_10 .text_17 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.page .box_8 .box_9 {
  width: 644rpx;
  height: 44rpx;
  margin: 30rpx 0 0 30rpx;
}
.page .box_8 .box_9 .image-wrapper_1 {
  width: 300rpx;
  height: 44rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.page .box_8 .box_9 .image-wrapper_1 .label_1 {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
}
.page .box_8 .box_9 .text_18 {
  width: 140rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 8rpx;
}
.page .box_8 .box_10 {
  width: 164rpx;
  height: 76rpx;
  margin: 42rpx 0 0 30rpx;
}
.page .box_8 .box_10 .text-group_2 {
  width: 164rpx;
  height: 76rpx;
}
.page .box_8 .box_10 .text-group_2 .text_19 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.page .box_8 .box_10 .text-group_2 .text_20 {
  width: 164rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 20rpx;
}
.page .box_8 .box_11 {
  width: 300rpx;
  height: 36rpx;
  margin: 22rpx 0 0 30rpx;
}
.page .box_8 .box_11 .section_4 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 36rpx;
}
.page .box_8 .box_11 .section_5 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 36rpx;
  margin-left: 28rpx;
}
.page .box_8 .box_11 .section_6 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 36rpx;
  margin-left: 24rpx;
}
.page .box_8 .box_11 .section_7 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 36rpx;
  margin-left: 24rpx;
}
.page .box_8 .box_11 .section_8 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 36rpx;
  margin-left: 24rpx;
}
.page .box_8 .text-wrapper_11 {
  width: 104rpx;
  height: 26rpx;
  margin: 30rpx 0 0 30rpx;
}
.page .box_8 .text-wrapper_11 .text_21 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
}
.page .box_8 .box_12 {
  width: 300rpx;
  height: 36rpx;
  margin: 22rpx 0 62rpx 28rpx;
}
.page .box_8 .box_12 .group_2 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 36rpx;
}
.page .box_8 .box_12 .group_3 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 36rpx;
  margin-left: 28rpx;
}
.page .box_8 .box_12 .group_4 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 36rpx;
  margin-left: 24rpx;
}
.page .box_8 .box_12 .group_5 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 36rpx;
  margin-left: 24rpx;
}
.page .box_8 .box_12 .group_6 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 36rpx;
  margin-left: 24rpx;
}
.page .box_8 .text-wrapper_12 {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  height: 154rpx;
  width: 218rpx;
  position: absolute;
  left: 264rpx;
  top: 412rpx;
}
.page .box_8 .text-wrapper_12 .text_22 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 64rpx 0 0 68rpx;
}
