@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 2286rpx;
  overflow: hidden;
}
.page .box_1 {
  width: 750rpx;
  height: 174rpx;
}
.page .box_1 .section_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .section_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .section_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .section_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .section_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .section_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
  margin-bottom: 2rpx;
}
.page .box_1 .section_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .box_1 .section_2 .text_2 {
  width: 64rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .box_1 .section_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 424rpx;
}
.page .box_2 {
  position: relative;
  width: 750rpx;
  height: 1436rpx;
  margin-top: 570rpx;
}
.page .box_2 .block_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 0px 10px 10px;
  width: 702rpx;
  height: 88rpx;
  -webkit-box-pack: flex-center;
  -webkit-justify-content: flex-center;
          justify-content: flex-center;
  margin: 70rpx 0 0 24rpx;
}
.page .box_2 .block_1 .text_3 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 24rpx;
}
.page .box_2 .block_1 .thumbnail_5 {
  width: 28rpx;
  height: 28rpx;
  margin: 30rpx 0 0 222rpx;
}
.page .box_2 .block_1 .text_4 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 12rpx;
}
.page .box_2 .block_1 .thumbnail_6 {
  width: 28rpx;
  height: 28rpx;
  margin: 30rpx 0 0 32rpx;
}
.page .box_2 .block_1 .text_5 {
  width: 126rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 24rpx 0 10rpx;
}
.page .box_2 .block_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px 8px 0px 0px;
  width: 702rpx;
  height: 100rpx;
  margin: 20rpx 0 0 26rpx;
}
.page .box_2 .block_2 .text_6 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_2 .block_2 .image-text_1 {
  width: 124rpx;
  height: 28rpx;
  margin: 36rpx 24rpx 0 0;
}
.page .box_2 .block_2 .image-text_1 .text-group_1 {
  width: 100rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .block_2 .image-text_1 .thumbnail_7 {
  width: 12rpx;
  height: 20rpx;
  margin-top: 4rpx;
}
.page .box_2 .block_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 0px 8px 8px;
  width: 702rpx;
  height: 220rpx;
  margin-left: 26rpx;
}
.page .box_2 .block_3 .text-wrapper_1 {
  width: 654rpx;
  height: 28rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_2 .block_3 .text-wrapper_1 .text_7 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .block_3 .text-wrapper_1 .text_8 {
  width: 44rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .block_3 .text_9 {
  width: 646rpx;
  height: 128rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 276rpx;
  margin: 28rpx 0 0 26rpx;
}
.page .box_2 .text-wrapper_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px 8px 0px 0px;
  height: 100rpx;
  width: 702rpx;
  margin: 20rpx 0 0 26rpx;
}
.page .box_2 .text-wrapper_2 .text_10 {
  width: 128rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_2 .text-wrapper_3 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 88rpx;
  margin-left: 26rpx;
}
.page .box_2 .text-wrapper_3 .text_11 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 24rpx;
}
.page .box_2 .text-wrapper_3 .text_12 {
  width: 120rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 22rpx 0 424rpx;
}
.page .box_2 .text-wrapper_4 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 88rpx;
  margin-left: 24rpx;
}
.page .box_2 .text-wrapper_4 .text_13 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 24rpx;
}
.page .box_2 .text-wrapper_4 .text_14 {
  width: 62rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 24rpx 0 0;
}
.page .box_2 .text-wrapper_5 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 72rpx;
  margin: 64rpx 0 0 26rpx;
}
.page .box_2 .text-wrapper_5 .text_15 {
  width: 56rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .box_2 .text-wrapper_5 .text_16 {
  width: 98rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .box_2 .text-wrapper_6 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 72rpx;
  margin-left: 26rpx;
}
.page .box_2 .text-wrapper_6 .text_17 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .box_2 .text-wrapper_6 .text_18 {
  width: 114rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .box_2 .block_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 0px 10px 10px;
  height: 72rpx;
  margin-left: 26rpx;
  width: 702rpx;
}
.page .box_2 .block_4 .text-wrapper_7 {
  width: 404rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 274rpx;
}
.page .box_2 .block_4 .text-wrapper_7 .text_19 {
  width: 404rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .block_4 .text-wrapper_7 .text_20 {
  width: 404rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .block_4 .text-wrapper_7 .text_21 {
  width: 404rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .text-wrapper_8 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  height: 88rpx;
  width: 702rpx;
  margin: 42rpx 0 0 26rpx;
}
.page .box_2 .text-wrapper_8 .text_22 {
  width: 272rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 20rpx;
}
.page .box_2 .block_5 {
  width: 702rpx;
  height: 160rpx;
  margin: 20rpx 0 52rpx 24rpx;
}
.page .box_2 .block_5 .block_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px 8px 0px 0px;
  width: 702rpx;
  height: 80rpx;
}
.page .box_2 .block_5 .block_6 .image-text_2 {
  width: 594rpx;
  height: 40rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_2 .block_5 .block_6 .image-text_2 .thumbnail_8 {
  width: 40rpx;
  height: 40rpx;
}
.page .box_2 .block_5 .block_6 .image-text_2 .text-group_2 {
  width: 534rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .box_2 .block_5 .block_6 .box_3 {
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  border: 1px solid rgb(197, 197, 197);
  margin: 20rpx 24rpx 0 0;
}
.page .box_2 .block_5 .block_7 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 0px 8px 8px;
  width: 702rpx;
  height: 80rpx;
}
.page .box_2 .block_5 .block_7 .image-text_3 {
  width: 586rpx;
  height: 40rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_2 .block_5 .block_7 .image-text_3 .thumbnail_9 {
  width: 40rpx;
  height: 40rpx;
}
.page .box_2 .block_5 .block_7 .image-text_3 .text-group_3 {
  width: 526rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .box_2 .block_5 .block_7 .image-text_3 .text-group_3 .text_23 {
  width: 526rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .block_5 .block_7 .image-text_3 .text-group_3 .text_24 {
  width: 526rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .block_5 .block_7 .box_4 {
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  border: 1px solid rgb(197, 197, 197);
  margin: 20rpx 26rpx 0 26rpx;
}
.page .box_2 .text-wrapper_9 {
  background-color: rgb(255, 255, 255);
  position: absolute;
  left: 26rpx;
  top: 786rpx;
  width: 702rpx;
  height: 72rpx;
}
.page .box_2 .text-wrapper_9 .text_25 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .box_2 .text-wrapper_9 .text_26 {
  width: 86rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .box_5 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
  margin-top: -2rpx;
}
.page .box_5 .text-wrapper_10 {
  width: 206rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 40rpx 0 0 24rpx;
}
.page .box_5 .text-wrapper_10 .text_27 {
  width: 206rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_5 .text-wrapper_10 .text_28 {
  width: 206rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 32rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_5 .text-wrapper_11 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 74rpx;
  width: 182rpx;
  margin: 18rpx 24rpx 0 0;
}
.page .box_5 .text-wrapper_11 .text_29 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 32rpx;
}
.page .box_6 {
  background-image: -webkit-linear-gradient(269deg, rgb(62, 200, 174) 0, rgba(62, 200, 174, 0) 100%);
  background-image: linear-gradient(181deg, rgb(62, 200, 174) 0, rgba(62, 200, 174, 0) 100%);
  position: absolute;
  left: 0;
  top: 172rpx;
  width: 750rpx;
  height: 572rpx;
}
.page .box_6 .group_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  width: 702rpx;
  height: 128rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_6 .group_1 .image-text_4 {
  width: 270rpx;
  height: 32rpx;
  margin: 48rpx 0 0 216rpx;
}
.page .box_6 .group_1 .image-text_4 .thumbnail_10 {
  width: 26rpx;
  height: 26rpx;
  margin-top: 4rpx;
}
.page .box_6 .group_1 .image-text_4 .text-group_4 {
  width: 224rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_6 .group_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  width: 702rpx;
  height: 196rpx;
  -webkit-box-pack: flex-center;
  -webkit-justify-content: flex-center;
          justify-content: flex-center;
  margin: 20rpx 0 0 24rpx;
}
.page .box_6 .group_2 .image-text_5 {
  width: 568rpx;
  height: 140rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_6 .group_2 .image-text_5 .image_2 {
  width: 140rpx;
  height: 140rpx;
}
.page .box_6 .group_2 .image-text_5 .text-group_5 {
  width: 404rpx;
  height: 138rpx;
}
.page .box_6 .group_2 .image-text_5 .text-group_5 .text_30 {
  width: 198rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_6 .group_2 .image-text_5 .text-group_5 .box_7 {
  width: 404rpx;
  height: 36rpx;
  margin-top: 22rpx;
}
.page .box_6 .group_2 .image-text_5 .text-group_5 .box_7 .text-wrapper_12 {
  width: 80rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_6 .group_2 .image-text_5 .text-group_5 .box_7 .text-wrapper_12 .text_31 {
  width: 80rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_6 .group_2 .image-text_5 .text-group_5 .box_7 .text-wrapper_12 .text_32 {
  width: 80rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 36rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_6 .group_2 .image-text_5 .text-group_5 .box_7 .text_33 {
  width: 312rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 10rpx;
}
.page .box_6 .group_2 .image-text_5 .text-group_5 .text_34 {
  width: 216rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 26rpx 0 0 4rpx;
}
.page .box_6 .group_2 .thumbnail_11 {
  width: 38rpx;
  height: 38rpx;
  margin: 126rpx 0 0 -60rpx;
}
.page .box_6 .group_2 .text_35 {
  width: 18rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 128rpx 0 0 26rpx;
}
.page .box_6 .group_2 .thumbnail_12 {
  width: 38rpx;
  height: 38rpx;
  margin: 126rpx 24rpx 0 26rpx;
}
.page .box_6 .group_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px 8px 0px 0px;
  width: 702rpx;
  height: 100rpx;
  -webkit-box-pack: flex-center;
  -webkit-justify-content: flex-center;
          justify-content: flex-center;
  margin: 20rpx 0 88rpx 24rpx;
}
.page .box_6 .group_3 .text_36 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .box_6 .group_3 .thumbnail_13 {
  width: 28rpx;
  height: 28rpx;
  margin: 36rpx 0 0 196rpx;
}
.page .box_6 .group_3 .text_37 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 36rpx 0 0 18rpx;
}
.page .box_6 .group_3 .thumbnail_14 {
  width: 28rpx;
  height: 28rpx;
  margin: 36rpx 0 0 30rpx;
}
.page .box_6 .group_3 .text_38 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 36rpx 24rpx 0 18rpx;
}
.page .box_6 .group_4 {
  background-color: rgb(255, 255, 255);
  height: 158rpx;
  width: 702rpx;
  position: absolute;
  left: 24rpx;
  top: 484rpx;
}
.page .box_6 .group_4 .section_3 {
  width: 654rpx;
  height: 28rpx;
  margin: 30rpx 0 0 24rpx;
}
.page .box_6 .group_4 .section_3 .text_39 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_6 .group_4 .section_3 .thumbnail_15 {
  width: 28rpx;
  height: 28rpx;
  margin-left: 222rpx;
}
.page .box_6 .group_4 .section_3 .text_40 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 12rpx;
}
.page .box_6 .group_4 .section_3 .thumbnail_16 {
  width: 28rpx;
  height: 28rpx;
  margin-left: 18rpx;
}
.page .box_6 .group_4 .section_3 .text_41 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 10rpx;
}
.page .box_6 .group_4 .section_4 {
  width: 242rpx;
  height: 28rpx;
  margin: 38rpx 0 34rpx 436rpx;
}
.page .box_6 .group_4 .section_4 .image-text_6 {
  width: 242rpx;
  height: 28rpx;
}
.page .box_6 .group_4 .section_4 .image-text_6 .text-group_6 {
  width: 212rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_6 .group_4 .section_4 .image-text_6 .thumbnail_17 {
  width: 12rpx;
  height: 20rpx;
  margin-top: 4rpx;
}
