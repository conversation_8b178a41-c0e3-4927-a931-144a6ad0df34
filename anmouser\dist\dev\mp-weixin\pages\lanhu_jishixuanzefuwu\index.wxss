@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(248, 248, 248);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .box_1 {
  background-color: rgb(0, 0, 0);
  width: 750rpx;
  height: 172rpx;
}
.page .box_1 .section_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .section_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .section_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .section_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .section_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .section_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .section_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 24rpx;
}
.page .box_1 .section_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .box_2 {
  position: relative;
  width: 750rpx;
  height: 1454rpx;
  margin-bottom: 2rpx;
}
.page .box_2 .group_1 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 324rpx;
}
.page .box_2 .group_1 .box_3 {
  width: 700rpx;
  height: 46rpx;
  margin: 30rpx 0 0 26rpx;
}
.page .box_2 .group_1 .box_3 .thumbnail_4 {
  width: 32rpx;
  height: 34rpx;
  margin-top: 2rpx;
}
.page .box_2 .group_1 .box_3 .image-text_1 {
  width: 422rpx;
  height: 30rpx;
  margin: 4rpx 0 0 22rpx;
}
.page .box_2 .group_1 .box_3 .image-text_1 .text-group_1 {
  width: 372rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .group_1 .box_3 .image-text_1 .box_4 {
  background-color: rgb(255, 255, 255);
  width: 32rpx;
  height: 20rpx;
  margin-top: 4rpx;
}
.page .box_2 .group_1 .box_3 .group_2 {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px 0px 2px 0px;
  width: 92rpx;
  height: 46rpx;
  margin-left: 40rpx;
}
.page .box_2 .group_1 .box_3 .group_2 .image-text_2 {
  width: 64rpx;
  height: 18rpx;
  margin: 14rpx 0 0 16rpx;
}
.page .box_2 .group_1 .box_3 .group_2 .image-text_2 .thumbnail_5 {
  width: 18rpx;
  height: 18rpx;
}
.page .box_2 .group_1 .box_3 .group_2 .image-text_2 .text-group_2 {
  width: 40rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 52rpx;
}
.page .box_2 .group_1 .box_3 .group_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 2px 0px 2px;
  width: 92rpx;
  height: 46rpx;
  border: 1px solid rgb(224, 224, 224);
}
.page .box_2 .group_1 .box_3 .group_3 .image-text_3 {
  width: 62rpx;
  height: 18rpx;
  margin: 14rpx 0 0 18rpx;
}
.page .box_2 .group_1 .box_3 .group_3 .image-text_3 .thumbnail_6 {
  width: 18rpx;
  height: 18rpx;
}
.page .box_2 .group_1 .box_3 .group_3 .image-text_3 .text-group_3 {
  width: 40rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 52rpx;
}
.page .box_2 .group_1 .box_5 {
  background-color: rgb(255, 255, 255);
  border-radius: 50px;
  width: 702rpx;
  height: 76rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .box_2 .group_1 .box_5 .image-text_4 {
  width: 140rpx;
  height: 26rpx;
  margin: 26rpx 0 0 34rpx;
}
.page .box_2 .group_1 .box_5 .image-text_4 .text-group_4 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .group_1 .box_5 .image-text_4 .box_6 {
  background-color: rgb(2, 2, 2);
  width: 20rpx;
  height: 12rpx;
  margin-top: 6rpx;
}
.page .box_2 .group_1 .box_5 .text_3 {
  width: 234rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 26rpx 0 0 54rpx;
}
.page .box_2 .group_1 .box_5 .label_1 {
  width: 48rpx;
  height: 48rpx;
  margin: 14rpx 22rpx 0 170rpx;
}
.page .box_2 .group_1 .box_7 {
  width: 608rpx;
  height: 46rpx;
  margin: 28rpx 0 0 30rpx;
}
.page .box_2 .group_1 .box_7 .text-wrapper_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 100px;
  height: 46rpx;
  width: 140rpx;
}
.page .box_2 .group_1 .box_7 .text-wrapper_1 .text_4 {
  width: 104rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 4rpx 0 0 18rpx;
}
.page .box_2 .group_1 .box_7 .text-wrapper_2 {
  background-color: rgba(34, 34, 34, 0.1);
  border-radius: 100px;
  height: 46rpx;
  margin-left: 16rpx;
  width: 140rpx;
}
.page .box_2 .group_1 .box_7 .text-wrapper_2 .text_5 {
  width: 104rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 4rpx 0 0 18rpx;
}
.page .box_2 .group_1 .box_7 .text-wrapper_3 {
  background-color: rgba(34, 34, 34, 0.1);
  border-radius: 100px;
  height: 46rpx;
  margin-left: 16rpx;
  width: 140rpx;
}
.page .box_2 .group_1 .box_7 .text-wrapper_3 .text_6 {
  width: 104rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 4rpx 0 0 18rpx;
}
.page .box_2 .group_1 .box_7 .text-wrapper_4 {
  background-color: rgba(34, 34, 34, 0.1);
  border-radius: 100px;
  height: 46rpx;
  margin-left: 16rpx;
  width: 140rpx;
}
.page .box_2 .group_1 .box_7 .text-wrapper_4 .text_7 {
  width: 104rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 4rpx 0 0 18rpx;
}
.page .box_2 .group_1 .box_8 {
  width: 700rpx;
  height: 36rpx;
  margin: 22rpx 0 18rpx 34rpx;
}
.page .box_2 .group_1 .box_8 .text_8 {
  width: 96rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
}
.page .box_2 .group_1 .box_8 .box_9 {
  background-color: rgb(255, 255, 255);
  width: 18rpx;
  height: 12rpx;
  margin: 24rpx 0 0 22rpx;
}
.page .box_2 .group_1 .box_8 .text_9 {
  width: 96rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin-left: 52rpx;
}
.page .box_2 .group_1 .box_8 .box_10 {
  background-color: rgb(255, 255, 255);
  width: 18rpx;
  height: 12rpx;
  margin: 24rpx 0 0 22rpx;
}
.page .box_2 .group_1 .box_8 .text_10 {
  width: 96rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin-left: 52rpx;
}
.page .box_2 .group_1 .box_8 .box_11 {
  background-color: rgb(255, 255, 255);
  width: 18rpx;
  height: 12rpx;
  margin: 24rpx 0 0 22rpx;
}
.page .box_2 .group_1 .box_8 .text_11 {
  width: 96rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin-left: 52rpx;
}
.page .box_2 .group_1 .box_8 .box_12 {
  background-color: rgb(255, 255, 255);
  width: 18rpx;
  height: 12rpx;
  margin: 24rpx 0 0 22rpx;
}
.page .box_2 .image-wrapper_1 {
  width: 702rpx;
  height: 336rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_2 .image-wrapper_1 .image_2 {
  width: 340rpx;
  height: 336rpx;
}
.page .box_2 .image-wrapper_1 .image_3 {
  width: 340rpx;
  height: 336rpx;
}
.page .box_2 .text-wrapper_5 {
  width: 236rpx;
  height: 24rpx;
  margin: 1324rpx 0 574rpx 444rpx;
}
.page .box_2 .text-wrapper_5 .text_12 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .text-wrapper_5 .text_13 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_2 .text-wrapper_6 {
  position: absolute;
  left: -790rpx;
  top: 1768rpx;
  width: 336rpx;
  height: 336rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
}
.page .box_2 .text-wrapper_6 .text_14 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 236rpx 0 0 860rpx;
}
.page .box_2 .text-wrapper_6 .text_15 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 236rpx -758rpx 0 138rpx;
}
.page .box_2 .text-wrapper_7 {
  background-color: rgba(16, 16, 16, 0.1);
  border-radius: 100px;
  height: 46rpx;
  width: 140rpx;
  position: absolute;
  left: 656rpx;
  top: 202rpx;
}
.page .box_2 .text-wrapper_7 .text_16 {
  width: 104rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 4rpx 0 0 18rpx;
}
.page .box_13 {
  background-color: rgba(0, 0, 0, 0.3);
  height: 1452rpx;
  width: 750rpx;
  position: absolute;
  left: 0;
  top: 172rpx;
}
.page .box_13 .group_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  width: 750rpx;
  height: 866rpx;
  margin-top: 586rpx;
}
.page .box_13 .group_4 .box_14 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 232rpx;
  margin-left: 24rpx;
  width: 702rpx;
}
.page .box_13 .group_4 .box_14 .box_15 {
  width: 654rpx;
  height: 124rpx;
  margin: 28rpx 0 0 24rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .image_4 {
  width: 124rpx;
  height: 124rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_16 {
  width: 202rpx;
  height: 78rpx;
  margin-left: 24rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_16 .box_17 {
  width: 202rpx;
  height: 32rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_16 .box_17 .text_17 {
  width: 92rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_16 .box_17 .text-wrapper_8 {
  background-color: rgb(11, 206, 148);
  border-radius: 2px;
  height: 32rpx;
  width: 100rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_16 .box_17 .text-wrapper_8 .text_18 {
  width: 80rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 0 0 10rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_16 .box_18 {
  width: 192rpx;
  height: 24rpx;
  margin-top: 22rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_16 .box_18 .image-text_5 {
  width: 46rpx;
  height: 24rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_16 .box_18 .image-text_5 .group_5 {
  width: 22rpx;
  height: 22rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEmSURBVHgBXVAxTsNAEJy93J0diQJ+YHiBoUI0iUSNaHgAfACFH1CFElIgRMkHAIkHIDrK/ADzAaBAcQDnlr21HUgs7a33ZnZnboGl7/MsPyyHmzwZ5oNljP4X5fl2hhAeAc4k3mHsVvfkuWhxM594uZOTM3dIXIbEgxK/GuvySga0kyfX/QEZ7BNCj5moleMaj4nkGFtDF1Te7L7J1VqkCJWZG2vawQtODbwbwXtIBntLYgNIrNQSYkf/Yzh7qm3l7Z48Cj1RoHq6JOJGRRSZX9KDhw0bycH6kYB9dVwbjgbm+hz4KGYlG+c+0FH4z3Pkx245bbM0JWMlBWY/UBNxAboKUn1dRr2kmjytMO6mrhCokOv7ir+fMDO5dZ3jwLRefU1fI+8XlnVmBHapEbUAAAAASUVORK5CYII=) 100% no-repeat;
  background-size: 100% 100%;
  margin-top: 2rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_16 .box_18 .image-text_5 .text-group_5 {
  width: 14rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_16 .box_18 .text_19 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_19 {
  width: 172rpx;
  height: 82rpx;
  margin-left: 132rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_19 .text-wrapper_9 {
  background-color: rgba(62, 200, 174, 0.2);
  border-radius: 2px;
  height: 32rpx;
  width: 172rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_19 .text-wrapper_9 .text_20 {
  width: 160rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 0 0 6rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_19 .text-wrapper_10 {
  background-color: rgba(27, 199, 99, 0.2);
  border-radius: 2px;
  height: 32rpx;
  width: 102rpx;
  margin: 18rpx 0 0 70rpx;
}
.page .box_13 .group_4 .box_14 .box_15 .box_19 .text-wrapper_10 .text_21 {
  width: 80rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(24, 200, 99);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 0 0 12rpx;
}
.page .box_13 .group_4 .box_14 .box_20 {
  width: 642rpx;
  height: 52rpx;
  margin: 8rpx 0 20rpx 36rpx;
}
.page .box_13 .group_4 .box_14 .box_20 .text-wrapper_11 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 32rpx;
  margin-top: 8rpx;
  width: 100rpx;
}
.page .box_13 .group_4 .box_14 .box_20 .text-wrapper_11 .text_22 {
  width: 60rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 0 0 20rpx;
}
.page .box_13 .group_4 .box_14 .box_20 .thumbnail_7 {
  width: 28rpx;
  height: 30rpx;
  margin: 10rpx 0 0 36rpx;
}
.page .box_13 .group_4 .box_14 .box_20 .text_23 {
  width: 16rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(162, 162, 162);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 12rpx 0 0 10rpx;
}
.page .box_13 .group_4 .box_14 .box_20 .group_6 {
  background-color: rgb(162, 162, 162);
  width: 28rpx;
  height: 28rpx;
  margin: 12rpx 0 0 20rpx;
}
.page .box_13 .group_4 .box_14 .box_20 .text_24 {
  width: 16rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(162, 162, 162);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 10rpx;
}
.page .box_13 .group_4 .box_14 .box_20 .thumbnail_8 {
  width: 28rpx;
  height: 28rpx;
  margin: 10rpx 0 0 20rpx;
}
.page .box_13 .group_4 .box_14 .box_20 .text_25 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(162, 162, 162);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 12rpx 0 0 8rpx;
}
.page .box_13 .group_4 .box_14 .box_20 .text-wrapper_12 {
  background-color: rgb(11, 206, 148);
  border-radius: 5px;
  height: 52rpx;
  margin-left: 142rpx;
  width: 132rpx;
}
.page .box_13 .group_4 .box_14 .box_20 .text-wrapper_12 .text_26 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 18rpx;
}
.page .box_13 .group_4 .box_21 {
  background-color: rgb(247, 247, 247);
  width: 750rpx;
  height: 630rpx;
  margin-top: 4rpx;
}
.page .box_13 .group_4 .box_21 .list_1 {
  width: 702rpx;
  height: 484rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
  margin: 20rpx 0 0 24rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  position: relative;
  width: 702rpx;
  height: 232rpx;
  margin-bottom: 20rpx;
  -webkit-box-pack: flex-center;
  -webkit-justify-content: flex-center;
          justify-content: flex-center;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 {
  position: relative;
  width: 482rpx;
  height: 182rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .image_5 {
  width: 182rpx;
  height: 182rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .text-group_6 {
  width: 248rpx;
  height: 176rpx;
  margin: 4rpx 0 0 22rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .text-group_6 .text_27 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .text-group_6 .text_28 {
  width: 248rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(152, 152, 152);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 20rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .text-group_6 .group_7 {
  width: 206rpx;
  height: 36rpx;
  margin-top: 66rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .text-group_6 .group_7 .text-wrapper_13 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 200rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .text-group_6 .group_7 .text-wrapper_13 .text_29 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 16rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 200rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .text-group_6 .group_7 .text-wrapper_13 .text_30 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 36rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .text-group_6 .group_7 .text_31 {
  width: 104rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  text-decoration: line-through;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .box_22 {
  border-radius: 4px;
  width: 154rpx;
  height: 30rpx;
  border: 1px solid rgb(231, 96, 81);
  margin: 92rpx 0 0 -124rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .box_22 .section_3 {
  background-color: rgb(231, 96, 81);
  border-radius: 4px 0px 4px 0px;
  height: 30rpx;
  width: 34rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .box_22 .section_3 .section_4 {
  background-color: rgb(255, 207, 202);
  width: 16rpx;
  height: 20rpx;
  margin: 4rpx 0 0 10rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .box_22 .text_32 {
  width: 106rpx;
  height: 16rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 6rpx 0 8rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .box_23 {
  background-image: -webkit-linear-gradient(top, rgb(59, 41, 0) 0, rgb(86, 54, 0) 100%);
  background-image: linear-gradient(180deg, rgb(59, 41, 0) 0, rgb(86, 54, 0) 100%);
  border-radius: 4px;
  position: absolute;
  left: 204rpx;
  top: 92rpx;
  width: 112rpx;
  height: 30rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .box_23 .image-text_7 {
  width: 90rpx;
  height: 20rpx;
  margin: 6rpx 0 0 12rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .box_23 .image-text_7 .group_8 {
  background-color: rgb(255, 234, 184);
  width: 20rpx;
  height: 20rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image-text_6 .box_23 .image-text_7 .text-group_7 {
  width: 64rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(255, 234, 184);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .box_24 {
  border-radius: 2px;
  width: 40rpx;
  height: 40rpx;
  border: 1px solid rgb(11, 206, 148);
  margin: 94rpx 0 0 36rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .text_33 {
  width: 12rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 100rpx 0 0 18rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .thumbnail_9 {
  width: 40rpx;
  height: 40rpx;
  margin: 94rpx 24rpx 0 26rpx;
}
.page .box_13 .group_4 .box_21 .list_1 .list-items_1 .image_6 {
  position: absolute;
  left: 550rpx;
  top: 100rpx;
  width: 122rpx;
  height: 26rpx;
}
.page .box_13 .group_4 .box_21 .box_25 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
  margin: 16rpx 0 2rpx 0;
}
.page .box_13 .group_4 .box_21 .box_25 .text-wrapper_14 {
  width: 150rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 40rpx 0 0 24rpx;
}
.page .box_13 .group_4 .box_21 .box_25 .text-wrapper_14 .text_34 {
  width: 150rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .group_4 .box_21 .box_25 .text-wrapper_14 .text_35 {
  width: 150rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 32rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .group_4 .box_21 .box_25 .text-wrapper_15 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 74rpx;
  width: 182rpx;
  margin: 18rpx 24rpx 0 0;
}
.page .box_13 .group_4 .box_21 .box_25 .text-wrapper_15 .text_36 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 32rpx;
}
