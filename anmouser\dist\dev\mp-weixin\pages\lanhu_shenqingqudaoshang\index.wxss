@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(245, 245, 245);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .group_1 {
  background-color: rgb(247, 247, 247);
  width: 750rpx;
  height: 172rpx;
}
.page .group_1 .box_1 {
  background-color: rgb(247, 247, 247);
  width: 750rpx;
  height: 64rpx;
}
.page .group_1 .box_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .group_1 .box_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .group_1 .box_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .group_1 .box_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .group_1 .box_2 {
  background-color: rgb(247, 247, 247);
  width: 750rpx;
  height: 108rpx;
}
.page .group_1 .box_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .group_1 .box_2 .text_2 {
  width: 160rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .group_1 .box_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 328rpx;
}
.page .group_2 {
  background-color: rgb(11, 206, 148);
  height: 512rpx;
  margin-top: 2rpx;
  width: 750rpx;
}
.page .group_2 .block_1 {
  width: 750rpx;
  height: 424rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
}
.page .group_2 .block_1 .image-text_1 {
  width: 456rpx;
  height: 46rpx;
  margin: 48rpx 0 0 24rpx;
}
.page .group_2 .block_1 .image-text_1 .thumbnail_5 {
  width: 40rpx;
  height: 40rpx;
  margin-top: 6rpx;
}
.page .group_2 .block_1 .image-text_1 .text-group_1 {
  width: 396rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 36rpx;
  font-family: Source Han Sans CN-Bold;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .text-wrapper_1 {
  background-color: rgb(11, 206, 148);
  border-radius: 50px;
  height: 86rpx;
  width: 690rpx;
  margin: 834rpx 0 18rpx 30rpx;
}
.page .text-wrapper_1 .text_3 {
  width: 120rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 286rpx;
}
.page .group_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  position: absolute;
  left: 24rpx;
  top: 320rpx;
  width: 702rpx;
  height: 1022rpx;
}
.page .group_3 .text_4 {
  width: 120rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 0 38rpx;
}
.page .group_3 .text-wrapper_2 {
  background-color: rgb(245, 245, 245);
  border-radius: 10px;
  height: 108rpx;
  width: 626rpx;
  margin: 30rpx 0 0 38rpx;
}
.page .group_3 .text-wrapper_2 .text_5 {
  width: 270rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 0 24rpx;
}
.page .group_3 .text_6 {
  width: 120rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 38rpx 0 0 38rpx;
}
.page .group_3 .text-wrapper_3 {
  background-color: rgb(245, 245, 245);
  border-radius: 10px;
  height: 108rpx;
  width: 626rpx;
  margin: 30rpx 0 0 38rpx;
}
.page .group_3 .text-wrapper_3 .text_7 {
  width: 270rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 0 24rpx;
}
.page .group_3 .text_8 {
  width: 120rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 42rpx 0 0 38rpx;
}
.page .group_3 .block_2 {
  background-color: rgb(245, 245, 245);
  border-radius: 10px;
  height: 108rpx;
  width: 626rpx;
  margin: 30rpx 0 0 38rpx;
}
.page .group_3 .block_2 .text-wrapper_4 {
  width: 300rpx;
  height: 22rpx;
  margin: 44rpx 0 0 24rpx;
}
.page .group_3 .block_2 .text-wrapper_4 .text_9 {
  width: 300rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_3 .block_2 .image-wrapper_1 {
  width: 18rpx;
  height: 34rpx;
  margin: 10rpx 0 2rpx 588rpx;
}
.page .group_3 .block_2 .image-wrapper_1 .thumbnail_6 {
  width: 18rpx;
  height: 34rpx;
}
.page .group_3 .text_10 {
  width: 120rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 40rpx 0 0 38rpx;
}
.page .group_3 .block_3 {
  background-color: rgb(245, 245, 245);
  border-radius: 10px;
  height: 278rpx;
  width: 626rpx;
  margin: 38rpx 0 40rpx 38rpx;
}
.page .group_3 .block_3 .text-wrapper_5 {
  width: 180rpx;
  height: 22rpx;
  margin: 44rpx 0 0 24rpx;
}
.page .group_3 .block_3 .text-wrapper_5 .text_11 {
  width: 180rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_3 .block_3 .text-wrapper_6 {
  width: 80rpx;
  height: 22rpx;
  margin: 164rpx 0 26rpx 506rpx;
}
.page .group_3 .block_3 .text-wrapper_6 .text_12 {
  width: 80rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
