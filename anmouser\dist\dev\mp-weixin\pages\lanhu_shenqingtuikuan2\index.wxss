@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 1748rpx;
  overflow: hidden;
}
.page .section_1 {
  width: 750rpx;
  height: 194rpx;
}
.page .section_1 .box_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .section_1 .box_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .section_1 .box_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .section_1 .box_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .section_1 .box_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .section_1 .box_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
  margin-bottom: 22rpx;
}
.page .section_1 .box_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .section_1 .box_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .section_1 .box_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .section_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 410rpx;
  width: 702rpx;
  position: relative;
  margin: -2rpx 0 0 24rpx;
}
.page .section_2 .text-wrapper_1 {
  width: 656rpx;
  height: 26rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .section_2 .text-wrapper_1 .text_3 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .text-wrapper_1 .text_4 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .section_2 .text-wrapper_2 {
  width: 180rpx;
  height: 30rpx;
  margin: 74rpx 0 0 278rpx;
}
.page .section_2 .text-wrapper_2 .text_5 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .group_1 {
  width: 390rpx;
  height: 40rpx;
  margin: 18rpx 0 0 24rpx;
}
.page .section_2 .group_1 .thumbnail_5 {
  width: 40rpx;
  height: 40rpx;
}
.page .section_2 .group_1 .text_6 {
  width: 136rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .section_2 .text-wrapper_3 {
  width: 176rpx;
  height: 24rpx;
  margin-left: 278rpx;
}
.page .section_2 .text-wrapper_3 .text_7 {
  width: 176rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .group_2 {
  width: 178rpx;
  height: 30rpx;
  margin: 14rpx 0 0 278rpx;
}
.page .section_2 .group_2 .text-wrapper_4 {
  width: 178rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .group_2 .text-wrapper_4 .text_8 {
  width: 178rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .group_2 .text-wrapper_4 .text_9 {
  width: 178rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .group_2 .text-wrapper_4 .text_10 {
  width: 178rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .text-wrapper_5 {
  width: 654rpx;
  height: 30rpx;
  margin: 60rpx 0 28rpx 24rpx;
}
.page .section_2 .text-wrapper_5 .text_11 {
  width: 52rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .section_2 .text-wrapper_5 .text_12 {
  width: 130rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .text-wrapper_6 {
  position: absolute;
  left: 194rpx;
  top: 134rpx;
  width: 482rpx;
  height: 30rpx;
}
.page .section_2 .text-wrapper_6 .text_13 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .text-wrapper_6 .text_14 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .section_2 .image_2 {
  position: absolute;
  left: 102rpx;
  top: 134rpx;
  width: 156rpx;
  height: 156rpx;
}
.page .section_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  width: 702rpx;
  height: 470rpx;
  margin: 140rpx 0 0 24rpx;
}
.page .section_3 .text_15 {
  width: 120rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 38rpx 0 0 24rpx;
}
.page .section_3 .box_3 {
  background-color: rgb(245, 245, 245);
  border-radius: 10px;
  height: 356rpx;
  width: 654rpx;
  margin: 32rpx 0 22rpx 24rpx;
}
.page .section_3 .box_3 .text-wrapper_7 {
  width: 210rpx;
  height: 22rpx;
  margin: 44rpx 0 0 24rpx;
}
.page .section_3 .box_3 .text-wrapper_7 .text_16 {
  width: 210rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .section_3 .box_3 .text-wrapper_8 {
  width: 80rpx;
  height: 22rpx;
  margin: 240rpx 0 28rpx 544rpx;
}
.page .section_3 .box_3 .text-wrapper_8 .text_17 {
  width: 80rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .section_4 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
  -webkit-box-pack: flex-center;
  -webkit-justify-content: flex-center;
          justify-content: flex-center;
  margin: 426rpx 0 2rpx 0;
}
.page .section_4 .section_5 {
  background-color: rgb(11, 206, 148);
  border-radius: 50%;
  width: 34rpx;
  height: 34rpx;
  border: 1px solid rgb(11, 206, 148);
  margin: 38rpx 0 0 24rpx;
}
.page .section_4 .text_18 {
  width: 52rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 42rpx 0 0 12rpx;
}
.page .section_4 .text-wrapper_9 {
  width: 220rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 38rpx 0 0 222rpx;
}
.page .section_4 .text-wrapper_9 .text_19 {
  width: 220rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_4 .text-wrapper_9 .text_20 {
  width: 220rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_4 .text-wrapper_9 .text_21 {
  width: 220rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_4 .text-wrapper_9 .text_22 {
  width: 220rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_4 .text-wrapper_9 .text_23 {
  width: 220rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_4 .text-wrapper_10 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 64rpx;
  width: 150rpx;
  margin: 22rpx 24rpx 0 12rpx;
}
.page .section_4 .text-wrapper_10 .text_24 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .section_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  height: 352rpx;
  width: 702rpx;
  position: absolute;
  left: 24rpx;
  top: 1226rpx;
}
.page .section_6 .text-wrapper_11 {
  width: 120rpx;
  height: 22rpx;
  margin: 48rpx 0 0 24rpx;
}
.page .section_6 .text-wrapper_11 .text_25 {
  width: 120rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .section_6 .box_4 {
  width: 658rpx;
  height: 238rpx;
  margin: 16rpx 0 28rpx 24rpx;
}
.page .section_6 .box_4 .group_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  width: 194rpx;
  height: 194rpx;
  border: 0.8083333373px solid rgb(235, 238, 245);
  margin-top: 24rpx;
}
.page .section_6 .box_4 .group_3 .image-text_1 {
  width: 92rpx;
  height: 98rpx;
  margin: 48rpx 0 0 52rpx;
}
.page .section_6 .box_4 .group_3 .image-text_1 .label_1 {
  width: 62rpx;
  height: 62rpx;
  margin-left: 16rpx;
}
.page .section_6 .box_4 .group_3 .image-text_1 .text-group_1 {
  width: 92rpx;
  height: 16rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 22rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 38rpx;
  margin-top: 20rpx;
}
.page .section_6 .box_4 .group_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  position: relative;
  width: 194rpx;
  height: 194rpx;
  border: 0.8083333373px solid rgb(235, 238, 245);
  margin: 24rpx 0 0 30rpx;
}
.page .section_6 .box_4 .group_4 .image-text_2 {
  width: 92rpx;
  height: 98rpx;
  margin: 48rpx 0 0 52rpx;
}
.page .section_6 .box_4 .group_4 .image-text_2 .label_2 {
  width: 62rpx;
  height: 62rpx;
  margin-left: 16rpx;
}
.page .section_6 .box_4 .group_4 .image-text_2 .text-group_2 {
  width: 92rpx;
  height: 16rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 22rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 38rpx;
  margin-top: 20rpx;
}
.page .section_6 .box_4 .group_4 .thumbnail_6 {
  position: absolute;
  left: 166rpx;
  top: -14rpx;
  width: 28rpx;
  height: 28rpx;
}
.page .section_6 .box_4 .group_5 {
  width: 218rpx;
  height: 238rpx;
  margin-left: 22rpx;
}
.page .section_6 .box_4 .group_5 .text-wrapper_12 {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  height: 154rpx;
  width: 218rpx;
}
.page .section_6 .box_4 .group_5 .text-wrapper_12 .text_26 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 64rpx 0 0 68rpx;
}
.page .section_6 .box_4 .group_5 .text-wrapper_13 {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  height: 68rpx;
  width: 188rpx;
  margin: 16rpx 0 0 18rpx;
}
.page .section_6 .box_4 .group_5 .text-wrapper_13 .text_27 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 38rpx;
}
.page .section_6 .text-wrapper_14 {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  height: 68rpx;
  width: 248rpx;
  position: absolute;
  left: 444rpx;
  top: -24rpx;
}
.page .section_6 .text-wrapper_14 .text_28 {
  width: 196rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 26rpx;
}
