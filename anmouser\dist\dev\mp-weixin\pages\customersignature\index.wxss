@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 1586rpx;
  overflow: hidden;
}
.page .group_1 {
  width: 750rpx;
  height: 222rpx;
}
.page .group_1 .group_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .group_1 .group_2 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .group_1 .group_2 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .group_1 .group_2 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .group_1 .group_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .group_1 .group_3 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
  margin-bottom: 50rpx;
}
.page .group_1 .group_3 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .group_1 .group_3 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .group_1 .group_3 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .group_4 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 514rpx;
  border: 1px solid rgb(177, 177, 177);
  margin: -2rpx 0 0 24rpx;
}
.page .group_5 {
  position: relative;
  width: 750rpx;
  height: 728rpx;
}
.page .group_5 .group_6 {
  width: 208rpx;
  height: 28rpx;
  margin: 38rpx 0 0 30rpx;
}
.page .group_5 .group_6 .group_7 {
  background-color: rgb(51, 51, 51);
  border-radius: 50%;
  width: 12rpx;
  height: 12rpx;
  margin-top: 8rpx;
}
.page .group_5 .group_6 .group_8 {
  background-color: rgb(24, 200, 99);
  border-radius: 50%;
  width: 16rpx;
  height: 16rpx;
  margin: 6rpx 0 0 30rpx;
}
.page .group_5 .group_6 .group_9 {
  background-color: rgb(51, 51, 51);
  border-radius: 50%;
  width: 20rpx;
  height: 20rpx;
  margin: 4rpx 0 0 28rpx;
}
.page .group_5 .group_6 .group_10 {
  background-color: rgb(51, 51, 51);
  border-radius: 50%;
  width: 24rpx;
  height: 24rpx;
  margin: 2rpx 0 0 26rpx;
}
.page .group_5 .group_6 .group_11 {
  background-color: rgb(51, 51, 51);
  border-radius: 50%;
  width: 28rpx;
  height: 28rpx;
  margin-left: 24rpx;
}
.page .group_5 .group_12 {
  width: 332rpx;
  height: 42rpx;
  margin: 28rpx 0 592rpx 24rpx;
}
.page .group_5 .group_12 .section_1 {
  background-color: rgb(51, 51, 51);
  border-radius: 4px;
  width: 42rpx;
  height: 42rpx;
}
.page .group_5 .group_12 .section_2 {
  background-color: rgb(244, 66, 54);
  border-radius: 4px;
  height: 42rpx;
  margin-left: 16rpx;
  width: 42rpx;
}
.page .group_5 .group_12 .section_2 .box_1 {
  width: 20rpx;
  height: 14rpx;
  border: 1px solid rgb(255, 255, 255);
  margin: 14rpx 0 0 12rpx;
}
.page .group_5 .group_12 .section_3 {
  background-color: rgb(63, 81, 181);
  border-radius: 4px;
  width: 42rpx;
  height: 42rpx;
  margin-left: 16rpx;
}
.page .group_5 .group_12 .section_4 {
  background-color: rgb(32, 149, 244);
  border-radius: 4px;
  width: 42rpx;
  height: 42rpx;
  margin-left: 16rpx;
}
.page .group_5 .group_12 .section_5 {
  background-color: rgb(255, 235, 60);
  border-radius: 4px;
  width: 42rpx;
  height: 42rpx;
  margin-left: 16rpx;
}
.page .group_5 .group_12 .section_6 {
  background-color: rgb(254, 153, 0);
  border-radius: 4px;
  width: 42rpx;
  height: 42rpx;
  margin-left: 16rpx;
}
.page .group_5 .group_13 {
  border-radius: 50%;
  position: absolute;
  left: 64rpx;
  top: 36rpx;
  width: 32rpx;
  height: 32rpx;
  border: 1px solid rgb(24, 200, 99);
}
.page .group_14 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 126rpx;
  margin-top: -2rpx;
}
.page .group_14 .text-wrapper_1 {
  border-radius: 100px;
  height: 88rpx;
  border: 1px solid rgb(153, 153, 153);
  width: 330rpx;
  margin: 20rpx 0 0 30rpx;
}
.page .group_14 .text-wrapper_1 .text_3 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 30rpx;
  margin: 30rpx 0 0 106rpx;
}
.page .group_14 .text-wrapper_2 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 88rpx;
  width: 330rpx;
  margin: 20rpx 30rpx 0 0;
}
.page .group_14 .text-wrapper_2 .text_4 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 30rpx;
  margin: 30rpx 0 0 106rpx;
}
