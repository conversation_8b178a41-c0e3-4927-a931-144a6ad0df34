{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_fankuixiaoxi/index.vue?1dd0", "webpack:///./src/pages/lanhu_fankuixiaoxi/index.vue?095f", "webpack:///./src/pages/lanhu_fankuixiaoxi/index.vue?036f", "webpack:///./src/pages/lanhu_fankuixiaoxi/index.vue?b058", "uni-app:///src/pages/lanhu_fankuixiaoxi/index.vue", "webpack:///./src/pages/lanhu_fankuixiaoxi/index.vue?19d8", "webpack:///./src/pages/lanhu_fankuixiaoxi/index.vue?0581"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAuD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFpCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCsItd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;AC7IA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_fankuixiaoxi/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_fankuixiaoxi/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=6c9cc251&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_fankuixiaoxi/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=6c9cc251&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"block_1 flex-col\">\n      <view class=\"box_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_fankuixiaoxi/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_fankuixiaoxi/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_fankuixiaoxi/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"box_2 flex-row\">\n        <image\n          class=\"thumbnail_4\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_fankuixiaoxi/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n        />\n        <text class=\"text_2\">反馈中心</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_fankuixiaoxi/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n        />\n      </view>\n      <view class=\"box_3 flex-col\">\n        <view class=\"section_1 flex-row\">\n          <view class=\"text-wrapper_1\">\n            <text class=\"text_3\">*</text>\n            <text class=\"text_4\">反馈类型</text>\n          </view>\n        </view>\n        <view class=\"section_2 flex-row justify-between\">\n          <view class=\"text-wrapper_2 flex-col\">\n            <text class=\"text_5\">订单问题</text>\n          </view>\n          <view class=\"text-wrapper_3 flex-col\">\n            <text class=\"text_6\">问题反馈</text>\n          </view>\n          <view class=\"text-wrapper_4 flex-col\">\n            <text class=\"text_7\">账号问题</text>\n          </view>\n        </view>\n        <view class=\"section_3 flex-row justify-between\">\n          <view class=\"text-wrapper_5 flex-col\">\n            <text class=\"text_8\">操作问题</text>\n          </view>\n          <view class=\"text-wrapper_6 flex-col\">\n            <text class=\"text_9\">BUG反馈</text>\n          </view>\n          <view class=\"text-wrapper_7 flex-col\">\n            <text class=\"text_10\">其他</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"box_4 flex-col\">\n        <view class=\"text-wrapper_8\">\n          <text class=\"text_11\">*</text>\n          <text class=\"text_12\">订单标号</text>\n        </view>\n        <text class=\"text_13\">\n          若涉及订单,填入订单号有助解决问题(订单详情页可复制)\n        </text>\n        <view class=\"text-wrapper_9 flex-col\">\n          <text class=\"text_14\">请输入订单号</text>\n        </view>\n      </view>\n    </view>\n    <view class=\"block_2 flex-col\">\n      <view class=\"text-wrapper_10\">\n        <text class=\"text_15\">*</text>\n        <text class=\"text_16\">反馈内容</text>\n      </view>\n      <view class=\"text-wrapper_11 flex-col\">\n        <text class=\"paragraph_1\">\n          您宝贵的建议，是我们不断进步的动力!请详细\n          <br />\n          描述遇到的问题\n        </text>\n        <text class=\"text_17\">0/1000</text>\n      </view>\n      <view class=\"group_1 flex-row\">\n        <view class=\"image-text_1 flex-col justify-between\">\n          <image\n            class=\"label_1\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_fankuixiaoxi/FigmaDDSSlicePNGdb5a020eea38aeb6ca330d03d5c24743.png\"\n          />\n          <view class=\"text-group_1 flex-col justify-between\">\n            <text class=\"text_18\">上传图片</text>\n            <text class=\"text_19\">0/3</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"group_2 flex-row\">\n        <view class=\"image-text_2 flex-col justify-between\">\n          <image\n            class=\"label_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_fankuixiaoxi/FigmaDDSSlicePNGdb5a020eea38aeb6ca330d03d5c24743.png\"\n          />\n          <view class=\"text-group_2 flex-col justify-between\">\n            <text class=\"text_20\">上传图片</text>\n            <text class=\"text_21\">0/3</text>\n          </view>\n        </view>\n      </view>\n      <text class=\"text_22\">\n        最多只能上传3张图片，1个视频（视频只支持3兆以内）\n      </text>\n    </view>\n    <view class=\"text-wrapper_12 flex-col\">\n      <text class=\"text_23\">注：你反馈的意见不会透露给他人，保护您的隐私</text>\n    </view>\n    <view class=\"block_3 flex-row justify-between\">\n      <view class=\"text-wrapper_13 flex-col\">\n        <text class=\"text_24\">反馈记录</text>\n      </view>\n      <view class=\"text-wrapper_14 flex-col\">\n        <text class=\"text_25\">提交反馈</text>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332578941\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}