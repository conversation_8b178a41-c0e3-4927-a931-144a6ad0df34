(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/cityselector/index"],{

/***/ 1676:
/*!*************************************************************!*\
  !*** ./src/main.js?{"page":"pages%2Fcityselector%2Findex"} ***!
  \*************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(createPage) {

__webpack_require__(/*! uni-pages */ 5);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 3));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pages/cityselector/index.vue */ 1677));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 1)["createPage"]))

/***/ }),

/***/ 1677:
/*!******************************************!*\
  !*** ./src/pages/cityselector/index.vue ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_19395a86___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=19395a86& */ 1678);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 1680);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&lang=scss& */ 1683);
/* harmony import */ var _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 11);

var renderjs





/* normalize component */

var component = Object(_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_19395a86___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_19395a86___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _index_vue_vue_type_template_id_19395a86___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/cityselector/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1678:
/*!*************************************************************************!*\
  !*** ./src/pages/cityselector/index.vue?vue&type=template&id=19395a86& ***!
  \*************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_15_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_19395a86___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=19395a86& */ 1679);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_15_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_19395a86___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_15_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_19395a86___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_15_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_19395a86___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_15_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_19395a86___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 1679:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--15-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/cityselector/index.vue?vue&type=template&id=19395a86& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 1680:
/*!*******************************************************************!*\
  !*** ./src/pages/cityselector/index.vue?vue&type=script&lang=js& ***!
  \*******************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 1681);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_babel_loader_lib_index_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_babel_loader_lib_index_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_babel_loader_lib_index_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1681:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/cityselector/index.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _mapConfig = __webpack_require__(/*! @/config/map-config.js */ 1682);
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a '" + o + "' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }
function _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }
function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; } //
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = exports.default = {
  data: function data() {
    return {
      constants: {},
      cities: [],
      // 城市列表数据
      allCities: [],
      // 所有城市数据（用于搜索）
      loading: false,
      // 加载状态
      refreshing: false,
      // 下拉刷新状态
      currentPage: 1,
      // 当前页码
      pageSize: 100,
      // 每页数量
      total: 0,
      // 总数量
      selectedCity: null,
      // 当前选中的城市
      scrollIntoView: '',
      // 滚动到指定元素
      searchKeyword: '',
      // 搜索关键词
      searchTimer: null,
      // 搜索防抖定时器
      isSearching: false,
      // 是否正在搜索
      isLocating: false // 是否正在定位
    };
  },
  computed: {
    // 按首字母分组的城市数据
    groupedCities: function groupedCities() {
      var citiesToGroup = this.isSearching ? this.cities : this.allCities;
      if (!citiesToGroup.length) return [];
      var groups = {};

      // 按首字母分组
      citiesToGroup.forEach(function (city) {
        var letter = city.initial || '#';
        if (!groups[letter]) {
          groups[letter] = [];
        }
        groups[letter].push(city);
      });

      // 转换为数组并排序
      return Object.keys(groups).sort().map(function (letter) {
        return {
          letter: letter,
          cities: groups[letter]
        };
      });
    },
    // 字母索引列表
    alphabetList: function alphabetList() {
      return this.groupedCities.map(function (group) {
        return group.letter;
      });
    }
  },
  onLoad: function onLoad() {
    // 页面加载时获取城市数据
    this.getCities();
    // 尝试从本地存储获取已选择的城市
    var savedCity = uni.getStorageSync('selectedCity');
    if (savedCity) {
      this.selectedCity = savedCity;
    }
  },
  methods: {
    // 获取城市数据
    getCities: function getCities() {
      var _this = this;
      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {
        var response, _response, error, res, apiResponse, _t;
        return _regenerator().w(function (_context) {
          while (1) switch (_context.p = _context.n) {
            case 0:
              _this.loading = true;
              _context.p = 1;
              _context.n = 2;
              return uni.request({
                url: "http://localhost:8000/api/v1/cities/?page=".concat(_this.currentPage, "&page_size=").concat(_this.pageSize),
                method: 'GET',
                header: {
                  'Content-Type': 'application/json'
                }
              });
            case 2:
              response = _context.v;
              // uni.request 返回数组格式: [error, response]
              _response = _slicedToArray(response, 2), error = _response[0], res = _response[1];
              if (!error) {
                _context.n = 3;
                break;
              }
              throw new Error("\u8BF7\u6C42\u9519\u8BEF: ".concat(error.message || error));
            case 3:
              if (!(res.statusCode === 200 && res.data)) {
                _context.n = 4;
                break;
              }
              apiResponse = res.data; // 检查API响应是否成功
              if (apiResponse.success && apiResponse.data) {
                _this.allCities = apiResponse.data; // 保存所有城市数据
                _this.cities = apiResponse.data; // 当前显示的城市数据
                _this.total = apiResponse.total || 0;
                console.log('获取城市数据成功:', {
                  count: _this.cities.length,
                  total: _this.total,
                  page: apiResponse.page,
                  pageSize: apiResponse.page_size
                });
              } else {
                console.error('API返回错误:', apiResponse.message || '未知错误');
                uni.showToast({
                  title: apiResponse.message || '获取数据失败',
                  icon: 'none'
                });
              }
              _context.n = 5;
              break;
            case 4:
              throw new Error("\u8BF7\u6C42\u5931\u8D25: ".concat(res.statusCode));
            case 5:
              _context.n = 7;
              break;
            case 6:
              _context.p = 6;
              _t = _context.v;
              console.error('获取城市数据失败:', _t);
              uni.showToast({
                title: '获取城市数据失败',
                icon: 'none'
              });
            case 7:
              _context.p = 7;
              _this.loading = false;
              return _context.f(7);
            case 8:
              return _context.a(2);
          }
        }, _callee, null, [[1, 6, 7, 8]]);
      }))();
    },
    // 选择城市
    selectCity: function selectCity(city) {
      console.log('选择的城市:', city);

      // 确认选择，保存到本地存储和当前状态
      this.selectedCity = city;
      uni.setStorageSync('selectedCity', city);
      uni.showToast({
        title: "\u5DF2\u9009\u62E9: ".concat(city.name),
        icon: 'success'
      });
    },
    // 清除选中的城市
    clearSelectedCity: function clearSelectedCity() {
      this.selectedCity = null;
      uni.removeStorageSync('selectedCity');
      uni.showToast({
        title: '已清除选择',
        icon: 'success'
      });
    },
    // 处理定位操作（清除选择或重新定位）
    handleLocationAction: function handleLocationAction() {
      // 如果正在定位中，则忽略点击
      if (this.isLocating) {
        return;
      }
      if (this.selectedCity) {
        // 如果已选择城市，则清除选择
        this.clearSelectedCity();
      } else {
        // 如果未选择城市，则重新定位
        this.getCurrentLocation();
      }
    },
    // 滚动到指定字母
    scrollToLetter: function scrollToLetter(letter) {
      var _this2 = this;
      this.scrollIntoView = "letter-".concat(letter);
      // 清除滚动状态，以便下次点击同一字母时也能滚动
      setTimeout(function () {
        _this2.scrollIntoView = '';
      }, 500);
    },
    // 下拉刷新
    onRefresh: function onRefresh() {
      var _this3 = this;
      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {
        return _regenerator().w(function (_context2) {
          while (1) switch (_context2.p = _context2.n) {
            case 0:
              _this3.refreshing = true;
              _this3.currentPage = 1;
              _context2.p = 1;
              _context2.n = 2;
              return _this3.getCities();
            case 2:
              _context2.p = 2;
              _this3.refreshing = false;
              return _context2.f(2);
            case 3:
              return _context2.a(2);
          }
        }, _callee2, null, [[1,, 2, 3]]);
      }))();
    },
    // 加载更多数据（如果需要分页）
    loadMore: function loadMore() {
      var _this4 = this;
      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {
        var response, _response2, error, res, apiResponse, newCities, _t2;
        return _regenerator().w(function (_context3) {
          while (1) switch (_context3.p = _context3.n) {
            case 0:
              if (!(_this4.loading || _this4.refreshing || _this4.cities.length >= _this4.total || _this4.isSearching)) {
                _context3.n = 1;
                break;
              }
              return _context3.a(2);
            case 1:
              _this4.currentPage++;
              _this4.loading = true;
              _context3.p = 2;
              _context3.n = 3;
              return uni.request({
                url: "http://localhost:8000/api/v1/cities/?page=".concat(_this4.currentPage, "&page_size=").concat(_this4.pageSize),
                method: 'GET',
                header: {
                  'Content-Type': 'application/json'
                }
              });
            case 3:
              response = _context3.v;
              // uni.request 返回数组格式: [error, response]
              _response2 = _slicedToArray(response, 2), error = _response2[0], res = _response2[1];
              if (!error) {
                _context3.n = 4;
                break;
              }
              throw new Error("\u8BF7\u6C42\u9519\u8BEF: ".concat(error.message || error));
            case 4:
              if (res.statusCode === 200 && res.data) {
                apiResponse = res.data;
                if (apiResponse.success && apiResponse.data) {
                  newCities = apiResponse.data; // 追加新数据
                  _this4.allCities = [].concat(_toConsumableArray(_this4.allCities), _toConsumableArray(newCities));
                  _this4.cities = [].concat(_toConsumableArray(_this4.cities), _toConsumableArray(newCities));
                  _this4.total = apiResponse.total || _this4.total;
                  console.log('加载更多数据成功:', {
                    newCount: newCities.length,
                    totalCount: _this4.cities.length,
                    total: _this4.total
                  });
                } else {
                  console.error('加载更多数据API返回错误:', apiResponse.message || '未知错误');
                  uni.showToast({
                    title: apiResponse.message || '加载失败',
                    icon: 'none'
                  });
                }
              }
              _context3.n = 6;
              break;
            case 5:
              _context3.p = 5;
              _t2 = _context3.v;
              console.error('加载更多数据失败:', _t2);
              _this4.currentPage--; // 回退页码
            case 6:
              _context3.p = 6;
              _this4.loading = false;
              return _context3.f(6);
            case 7:
              return _context3.a(2);
          }
        }, _callee3, null, [[2, 5, 6, 7]]);
      }))();
    },
    // 搜索输入处理（防抖）
    onSearchInput: function onSearchInput() {
      var _this5 = this;
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置新的定时器，300ms后执行搜索
      this.searchTimer = setTimeout(function () {
        _this5.performSearch();
      }, 300);
    },
    // 搜索确认（用户按回车或点击搜索按钮）
    onSearchConfirm: function onSearchConfirm() {
      // 清除防抖定时器，立即执行搜索
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      this.performSearch();
    },
    // 执行搜索
    performSearch: function performSearch() {
      var _this6 = this;
      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {
        var keyword, response, _response3, error, res, apiResponse, _t3;
        return _regenerator().w(function (_context4) {
          while (1) switch (_context4.p = _context4.n) {
            case 0:
              keyword = _this6.searchKeyword.trim();
              if (keyword) {
                _context4.n = 1;
                break;
              }
              // 如果搜索关键词为空，显示所有城市
              _this6.isSearching = false;
              _this6.cities = _this6.allCities;
              return _context4.a(2);
            case 1:
              _this6.isSearching = true;
              _this6.loading = true;
              _context4.p = 2;
              _context4.n = 3;
              return uni.request({
                url: "http://localhost:8000/api/v1/cities/?search=".concat(encodeURIComponent(keyword), "&page_size=50"),
                method: 'GET',
                header: {
                  'Content-Type': 'application/json'
                }
              });
            case 3:
              response = _context4.v;
              // uni.request 返回数组格式: [error, response]
              _response3 = _slicedToArray(response, 2), error = _response3[0], res = _response3[1];
              if (!error) {
                _context4.n = 4;
                break;
              }
              throw new Error("\u641C\u7D22\u8BF7\u6C42\u9519\u8BEF: ".concat(error.message || error));
            case 4:
              if (!(res.statusCode === 200 && res.data)) {
                _context4.n = 5;
                break;
              }
              apiResponse = res.data;
              if (apiResponse.success && apiResponse.data) {
                _this6.cities = apiResponse.data;
                console.log('搜索城市成功:', {
                  keyword: keyword,
                  count: _this6.cities.length
                });
                if (_this6.cities.length === 0) {
                  uni.showToast({
                    title: '未找到匹配的城市',
                    icon: 'none'
                  });
                }
              } else {
                console.error('搜索API返回错误:', apiResponse.message || '未知错误');
                uni.showToast({
                  title: apiResponse.message || '搜索失败',
                  icon: 'none'
                });
              }
              _context4.n = 6;
              break;
            case 5:
              throw new Error("\u641C\u7D22\u8BF7\u6C42\u5931\u8D25: ".concat(res.statusCode));
            case 6:
              _context4.n = 8;
              break;
            case 7:
              _context4.p = 7;
              _t3 = _context4.v;
              console.error('搜索城市失败:', _t3);
              uni.showToast({
                title: '搜索失败，请重试',
                icon: 'none'
              });
            case 8:
              _context4.p = 8;
              _this6.loading = false;
              return _context4.f(8);
            case 9:
              return _context4.a(2);
          }
        }, _callee4, null, [[2, 7, 8, 9]]);
      }))();
    },
    // 清除搜索
    clearSearch: function clearSearch() {
      this.searchKeyword = '';
      this.isSearching = false;
      this.cities = this.allCities;

      // 清除防抖定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
    },
    // 获取当前位置
    getCurrentLocation: function getCurrentLocation() {
      var _this7 = this;
      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {
        var platform, isH5, location, _t4;
        return _regenerator().w(function (_context5) {
          while (1) switch (_context5.p = _context5.n) {
            case 0:
              if (!_this7.isLocating) {
                _context5.n = 1;
                break;
              }
              return _context5.a(2);
            case 1:
              _this7.isLocating = true;
              _context5.p = 2;
              // 显示加载提示
              uni.showLoading({
                title: '正在定位...',
                mask: true
              });

              // 检测运行环境
              platform = uni.getSystemInfoSync().platform;
              isH5 = platform === 'h5' || typeof window !== 'undefined';
              if (!isH5) {
                _context5.n = 4;
                break;
              }
              _context5.n = 3;
              return _this7.getH5Location();
            case 3:
              location = _context5.v;
              _context5.n = 6;
              break;
            case 4:
              _context5.n = 5;
              return _this7.getUniLocation();
            case 5:
              location = _context5.v;
            case 6:
              if (!location) {
                _context5.n = 7;
                break;
              }
              _context5.n = 7;
              return _this7.getCityByLocation(location.latitude, location.longitude);
            case 7:
              _context5.n = 9;
              break;
            case 8:
              _context5.p = 8;
              _t4 = _context5.v;
              console.error('定位失败:', _t4);
              _this7.handleLocationError(_t4);
            case 9:
              _context5.p = 9;
              _this7.isLocating = false;
              uni.hideLoading();
              return _context5.f(9);
            case 10:
              return _context5.a(2);
          }
        }, _callee5, null, [[2, 8, 9, 10]]);
      }))();
    },
    // H5环境定位
    getH5Location: function getH5Location() {
      return new Promise(function (resolve, reject) {
        if (!navigator.geolocation) {
          reject(new Error('浏览器不支持定位功能'));
          return;
        }
        navigator.geolocation.getCurrentPosition(function (position) {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        }, function (error) {
          var message = '定位失败';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              message = '用户拒绝了定位请求';
              break;
            case error.POSITION_UNAVAILABLE:
              message = '位置信息不可用';
              break;
            case error.TIMEOUT:
              message = '定位请求超时';
              break;
          }
          reject(new Error(message));
        }, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        });
      });
    },
    // 小程序环境定位
    getUniLocation: function getUniLocation() {
      return new Promise(function (resolve, reject) {
        uni.getLocation({
          type: 'gcj02',
          // 使用国测局坐标系
          success: function success(res) {
            resolve({
              latitude: res.latitude,
              longitude: res.longitude
            });
          },
          fail: function fail(error) {
            var message = '定位失败';
            if (error.errMsg) {
              if (error.errMsg.includes('auth deny')) {
                message = '用户拒绝了定位权限';
              } else if (error.errMsg.includes('timeout')) {
                message = '定位超时，请重试';
              } else if (error.errMsg.includes('system deny')) {
                message = '系统拒绝定位，请检查设置';
              }
            }
            reject(new Error(message));
          }
        });
      });
    },
    // 根据经纬度获取城市信息
    getCityByLocation: function getCityByLocation(latitude, longitude) {
      var _this8 = this;
      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6() {
        var city, matchedCity, tempCity, _t5, _t6;
        return _regenerator().w(function (_context6) {
          while (1) switch (_context6.p = _context6.n) {
            case 0:
              _context6.p = 0;
              // 优先尝试使用地理编码API
              city = null;
              _context6.p = 1;
              _context6.n = 2;
              return _this8.getTencentCityInfo(latitude, longitude);
            case 2:
              city = _context6.v;
              _context6.n = 4;
              break;
            case 3:
              _context6.p = 3;
              _t5 = _context6.v;
              console.warn('腾讯地图API调用失败，使用备用方案:', _t5);
              // 方案2: 备用 - 根据坐标范围估算城市
              city = _this8.estimateCityByCoordinates(latitude, longitude);
            case 4:
              if (!city) {
                _context6.n = 5;
                break;
              }
              // 在城市列表中查找匹配的城市
              matchedCity = _this8.findCityInList(city);
              if (matchedCity) {
                _this8.selectedCity = matchedCity;
                uni.setStorageSync('selectedCity', matchedCity);
                uni.showToast({
                  title: "\u5B9A\u4F4D\u6210\u529F: ".concat(matchedCity.name),
                  icon: 'success'
                });
              } else {
                // 如果在列表中找不到，创建一个临时城市对象
                tempCity = {
                  code: '000000',
                  name: city,
                  province: '未知省份',
                  pinyin: '',
                  initial: city.charAt(0).toUpperCase()
                };
                _this8.selectedCity = tempCity;
                uni.setStorageSync('selectedCity', tempCity);
                uni.showToast({
                  title: "\u5B9A\u4F4D\u6210\u529F: ".concat(city),
                  icon: 'success'
                });
              }
              _context6.n = 6;
              break;
            case 5:
              throw new Error('无法获取城市信息');
            case 6:
              _context6.n = 8;
              break;
            case 7:
              _context6.p = 7;
              _t6 = _context6.v;
              console.error('获取城市信息失败:', _t6);
              uni.showToast({
                title: '获取城市信息失败',
                icon: 'none'
              });
            case 8:
              return _context6.a(2);
          }
        }, _callee6, null, [[1, 3], [0, 7]]);
      }))();
    },
    // 腾讯地图逆地理编码API（需要申请密钥）
    getTencentCityInfo: function getTencentCityInfo(latitude, longitude) {
      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7() {
        var config, url, response, _response4, error, res, data, addressComponent, cityName, _t7;
        return _regenerator().w(function (_context7) {
          while (1) switch (_context7.p = _context7.n) {
            case 0:
              if ((0, _mapConfig.isApiKeyConfigured)('TENCENT')) {
                _context7.n = 1;
                break;
              }
              throw new Error('请在 config/map-config.js 中配置腾讯地图API密钥');
            case 1:
              config = _mapConfig.MAP_CONFIG.TENCENT;
              url = "".concat(config.GEOCODER_URL, "?location=").concat(latitude, ",").concat(longitude, "&key=").concat(config.KEY, "&get_poi=0");
              _context7.p = 2;
              _context7.n = 3;
              return uni.request({
                url: url,
                method: 'GET',
                timeout: 10000
              });
            case 3:
              response = _context7.v;
              _response4 = _slicedToArray(response, 2), error = _response4[0], res = _response4[1];
              if (!error) {
                _context7.n = 4;
                break;
              }
              throw new Error("\u8BF7\u6C42\u5931\u8D25: ".concat(error.message || error));
            case 4:
              if (!(res.statusCode === 200 && res.data)) {
                _context7.n = 7;
                break;
              }
              data = res.data;
              if (!(data.status === 0 && data.result)) {
                _context7.n = 5;
                break;
              }
              addressComponent = data.result.address_component; // 优先使用city，如果没有则使用district
              cityName = addressComponent.city || addressComponent.district || '未知城市';
              return _context7.a(2, cityName.replace('市', '') + '市');
            case 5:
              throw new Error("API\u8FD4\u56DE\u9519\u8BEF: ".concat(data.message || '未知错误'));
            case 6:
              _context7.n = 8;
              break;
            case 7:
              throw new Error("HTTP\u9519\u8BEF: ".concat(res.statusCode));
            case 8:
              _context7.n = 10;
              break;
            case 9:
              _context7.p = 9;
              _t7 = _context7.v;
              console.error('腾讯地图API调用失败:', _t7);
              throw _t7;
            case 10:
              return _context7.a(2);
          }
        }, _callee7, null, [[2, 9]]);
      }))();
    },
    // 高德地图逆地理编码API（备用方案）
    getAmapCityInfo: function getAmapCityInfo(latitude, longitude) {
      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee8() {
        var config, url, response, _response5, error, res, data, addressComponent, cityName, _t8;
        return _regenerator().w(function (_context8) {
          while (1) switch (_context8.p = _context8.n) {
            case 0:
              if ((0, _mapConfig.isApiKeyConfigured)('AMAP')) {
                _context8.n = 1;
                break;
              }
              throw new Error('请在 config/map-config.js 中配置高德地图API密钥');
            case 1:
              config = _mapConfig.MAP_CONFIG.AMAP;
              url = "".concat(config.GEOCODER_URL, "?location=").concat(longitude, ",").concat(latitude, "&key=").concat(config.KEY, "&radius=1000&extensions=base");
              _context8.p = 2;
              _context8.n = 3;
              return uni.request({
                url: url,
                method: 'GET',
                timeout: 10000
              });
            case 3:
              response = _context8.v;
              _response5 = _slicedToArray(response, 2), error = _response5[0], res = _response5[1];
              if (!error) {
                _context8.n = 4;
                break;
              }
              throw new Error("\u8BF7\u6C42\u5931\u8D25: ".concat(error.message || error));
            case 4:
              if (!(res.statusCode === 200 && res.data)) {
                _context8.n = 7;
                break;
              }
              data = res.data;
              if (!(data.status === '1' && data.regeocode)) {
                _context8.n = 5;
                break;
              }
              addressComponent = data.regeocode.addressComponent;
              cityName = addressComponent.city || addressComponent.district || '未知城市';
              return _context8.a(2, cityName.replace('市', '') + '市');
            case 5:
              throw new Error("API\u8FD4\u56DE\u9519\u8BEF: ".concat(data.info || '未知错误'));
            case 6:
              _context8.n = 8;
              break;
            case 7:
              throw new Error("HTTP\u9519\u8BEF: ".concat(res.statusCode));
            case 8:
              _context8.n = 10;
              break;
            case 9:
              _context8.p = 9;
              _t8 = _context8.v;
              console.error('高德地图API调用失败:', _t8);
              throw _t8;
            case 10:
              return _context8.a(2);
          }
        }, _callee8, null, [[2, 9]]);
      }))();
    },
    // 简化的坐标城市估算（仅作演示，实际项目建议使用地理编码API）
    estimateCityByCoordinates: function estimateCityByCoordinates(lat, lng) {
      // 这里只是一个简化的示例，实际应该使用专业的地理编码服务
      var cityRanges = [{
        name: '北京市',
        lat: [39.4, 41.1],
        lng: [115.4, 117.5]
      }, {
        name: '上海市',
        lat: [30.7, 31.9],
        lng: [120.9, 122.0]
      }, {
        name: '广州市',
        lat: [22.5, 23.9],
        lng: [112.9, 114.5]
      }, {
        name: '深圳市',
        lat: [22.4, 22.9],
        lng: [113.7, 114.6]
      }, {
        name: '杭州市',
        lat: [29.8, 30.6],
        lng: [119.7, 120.9]
      }, {
        name: '成都市',
        lat: [30.1, 31.4],
        lng: [103.5, 104.9]
      }, {
        name: '重庆市',
        lat: [28.1, 32.2],
        lng: [105.2, 110.2]
      }, {
        name: '武汉市',
        lat: [29.9, 31.4],
        lng: [113.7, 115.1]
      }, {
        name: '西安市',
        lat: [33.7, 34.8],
        lng: [107.9, 109.5]
      }, {
        name: '南京市',
        lat: [31.1, 32.9],
        lng: [118.2, 119.2]
      }];
      for (var _i = 0, _cityRanges = cityRanges; _i < _cityRanges.length; _i++) {
        var city = _cityRanges[_i];
        if (lat >= city.lat[0] && lat <= city.lat[1] && lng >= city.lng[0] && lng <= city.lng[1]) {
          return city.name;
        }
      }
      return '未知城市';
    },
    // 在城市列表中查找匹配的城市
    findCityInList: function findCityInList(cityName) {
      return this.allCities.find(function (city) {
        return city.name === cityName || city.name.includes(cityName.replace('市', '')) || cityName.includes(city.name.replace('市', ''));
      });
    },
    // 处理定位错误
    handleLocationError: function handleLocationError(error) {
      var _this9 = this;
      var message = error.message || '定位失败';
      uni.showModal({
        title: '定位失败',
        content: "".concat(message, "\n\n\u60A8\u53EF\u4EE5\uFF1A\n1. \u68C0\u67E5\u5B9A\u4F4D\u6743\u9650\u8BBE\u7F6E\n2. \u624B\u52A8\u641C\u7D22\u9009\u62E9\u57CE\u5E02\n3. \u7A0D\u540E\u91CD\u8BD5"),
        showCancel: true,
        cancelText: '手动选择',
        confirmText: '重试',
        success: function success(res) {
          if (res.confirm) {
            // 用户选择重试
            setTimeout(function () {
              _this9.getCurrentLocation();
            }, 1000);
          } else {
            // 用户选择手动选择，聚焦到搜索框
            // 注意：小程序中input的focus需要特殊处理
            _this9.$nextTick(function () {
              // 可以在这里添加聚焦搜索框的逻辑
            });
          }
        }
      });
    }
  }
};
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 1)["default"]))

/***/ }),

/***/ 1683:
/*!****************************************************************************!*\
  !*** ./src/pages/cityselector/index.vue?vue&type=style&index=0&lang=scss& ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_cli_service_node_modules_mini_css_extract_plugin_dist_loader_js_ref_9_oneOf_1_0_node_modules_vue_cli_service_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_9_oneOf_1_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss& */ 1684);
/* harmony import */ var _node_modules_vue_cli_service_node_modules_mini_css_extract_plugin_dist_loader_js_ref_9_oneOf_1_0_node_modules_vue_cli_service_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_9_oneOf_1_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_cli_service_node_modules_mini_css_extract_plugin_dist_loader_js_ref_9_oneOf_1_0_node_modules_vue_cli_service_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_9_oneOf_1_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_cli_service_node_modules_mini_css_extract_plugin_dist_loader_js_ref_9_oneOf_1_0_node_modules_vue_cli_service_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_9_oneOf_1_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_cli_service_node_modules_mini_css_extract_plugin_dist_loader_js_ref_9_oneOf_1_0_node_modules_vue_cli_service_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_9_oneOf_1_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_vue_cli_service_node_modules_mini_css_extract_plugin_dist_loader_js_ref_9_oneOf_1_0_node_modules_vue_cli_service_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_9_oneOf_1_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_9_oneOf_1_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1684:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!./node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--9-oneOf-1-2!./node_modules/postcss-loader/src??ref--9-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--9-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/pages/cityselector/index.vue?vue&type=style&index=0&lang=scss& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[1676,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/cityselector/index.js.map