@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(245, 245, 245);
  position: relative;
  width: 750rpx;
  height: 2262rpx;
  overflow: hidden;
}
.page .box_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 172rpx;
}
.page .box_1 .box_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .box_2 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .box_2 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .box_2 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .box_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .box_3 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .box_3 .text_2 {
  width: 160rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 12rpx;
}
.page .box_1 .box_3 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 0;
}
.page .box_4 {
  width: 750rpx;
  height: 1058rpx;
}
.page .box_4 .group_1 {
  background-color: rgb(62, 200, 174);
  height: 324rpx;
  width: 750rpx;
}
.page .box_4 .group_1 .box_5 {
  background-color: rgba(0, 0, 0, 0.2);
  width: 750rpx;
  height: 90rpx;
}
.page .box_4 .group_1 .box_5 .image-text_1 {
  width: 236rpx;
  height: 30rpx;
  margin: 32rpx 0 0 16rpx;
}
.page .box_4 .group_1 .box_5 .image-text_1 .group_2 {
  background-color: rgb(255, 255, 255);
  width: 38rpx;
  height: 30rpx;
}
.page .box_4 .group_1 .box_5 .image-text_1 .text-group_1 {
  width: 180rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 2rpx;
}
.page .box_4 .group_1 .box_5 .text_3 {
  width: 306rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 34rpx 16rpx 0 0;
}
.page .box_4 .group_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 702rpx;
  height: 142rpx;
  margin: 296rpx 0 0 24rpx;
}
.page .box_4 .group_3 .image-text_2 {
  width: 400rpx;
  height: 64rpx;
  margin: 40rpx 0 0 24rpx;
}
.page .box_4 .group_3 .image-text_2 .label_1 {
  width: 60rpx;
  height: 60rpx;
  margin-top: 2rpx;
}
.page .box_4 .group_3 .image-text_2 .text-group_2 {
  width: 326rpx;
  height: 64rpx;
}
.page .box_4 .group_3 .image-text_2 .text-group_2 .text_4 {
  width: 270rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .group_3 .image-text_2 .text-group_2 .text_5 {
  width: 326rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 18rpx;
}
.page .box_4 .group_3 .box_6 {
  background-color: rgb(245, 245, 245);
  border-radius: 60px;
  height: 34rpx;
  width: 68rpx;
  margin: 54rpx 24rpx 0 0;
}
.page .box_4 .group_3 .box_6 .section_1 {
  background-color: rgb(62, 200, 174);
  border-radius: 50%;
  width: 30rpx;
  height: 30rpx;
  margin: 2rpx 0 0 2rpx;
}
.page .box_4 .group_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 252rpx;
  width: 706rpx;
  -webkit-box-pack: flex-center;
  -webkit-justify-content: flex-center;
          justify-content: flex-center;
  margin: 20rpx 0 24rpx 22rpx;
}
.page .box_4 .group_4 .box_7 {
  width: 652rpx;
  height: 28rpx;
  margin: 32rpx 0 0 26rpx;
}
.page .box_4 .group_4 .box_7 .text_6 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_4 .group_4 .box_7 .text_7 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Pinyon Script-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 2rpx 0 0 424rpx;
}
.page .box_4 .group_4 .box_7 .thumbnail_4 {
  width: 10rpx;
  height: 16rpx;
  margin: 6rpx 0 0 10rpx;
}
.page .box_4 .group_4 .box_8 {
  width: 606rpx;
  height: 124rpx;
  margin: 36rpx 0 32rpx 50rpx;
}
.page .box_4 .group_4 .box_8 .box_9 {
  width: 80rpx;
  height: 124rpx;
}
.page .box_4 .group_4 .box_8 .box_9 .box_10 {
  background-color: rgb(254, 238, 238);
  border-radius: 50%;
  height: 80rpx;
  width: 80rpx;
  position: relative;
}
.page .box_4 .group_4 .box_8 .box_9 .box_10 .label_2 {
  width: 48rpx;
  height: 50rpx;
  margin: 16rpx 0 0 16rpx;
}
.page .box_4 .group_4 .box_8 .box_9 .box_10 .text-wrapper_1 {
  background-color: rgb(238, 12, 12);
  border-radius: 50%;
  height: 28rpx;
  width: 28rpx;
  position: absolute;
  left: 60rpx;
  top: -8rpx;
}
.page .box_4 .group_4 .box_8 .box_9 .box_10 .text-wrapper_1 .text_8 {
  width: 12rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 4rpx 0 0 8rpx;
}
.page .box_4 .group_4 .box_8 .box_9 .text_9 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 4rpx;
}
.page .box_4 .group_4 .box_8 .box_11 {
  width: 80rpx;
  height: 124rpx;
  margin-left: 96rpx;
}
.page .box_4 .group_4 .box_8 .box_11 .section_2 {
  background-color: rgb(227, 248, 243);
  border-radius: 50%;
  height: 80rpx;
  width: 80rpx;
  position: relative;
}
.page .box_4 .group_4 .box_8 .box_11 .section_2 .label_3 {
  width: 48rpx;
  height: 48rpx;
  margin: 16rpx 0 0 16rpx;
}
.page .box_4 .group_4 .box_8 .box_11 .section_2 .text-wrapper_2 {
  background-color: rgb(238, 12, 12);
  border-radius: 50%;
  height: 28rpx;
  width: 28rpx;
  position: absolute;
  left: 56rpx;
  top: -6rpx;
}
.page .box_4 .group_4 .box_8 .box_11 .section_2 .text-wrapper_2 .text_10 {
  width: 12rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 4rpx 0 0 8rpx;
}
.page .box_4 .group_4 .box_8 .box_11 .text_11 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 4rpx;
}
.page .box_4 .group_4 .box_8 .image-text_3 {
  width: 80rpx;
  height: 124rpx;
  margin-left: 94rpx;
}
.page .box_4 .group_4 .box_8 .image-text_3 .image-wrapper_1 {
  height: 80rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKVSURBVHgBzZmNcdpAEIXfHrFnSJwMLiCDVEFIBZZLSAdJBcEVgCsIriCkApdguQOlAkRSAMwEw8QZbrN7/MQQAYIo4r4ZG6ST4M27Y293RTiQ3g+OTAVviBEx0AC4Jqdr8+Gh/KUESmUssRb34UuKcQC0z8U95poZ26bc9vGJmLzflDJzzMZch1VK89+WV9iDbYOcsH+Gwd28QncK7I+mTRHWwr6O7f7mVD63Xa/Sl+2XYau4TlGubYSpUz+jq03DmQJ1SisT3MqaiVAOiX2ky/CchusDJutqM+G7EsUpjcoJ32YN/CXQTStr2CgXJkT9EX9aP78yxb0RvzfEn3FELPNVeFbpLI6XAnsTDgzznbgX4LgMZT2Gi/W4nOKKRcsDcUrNnKC1OHAOOvcs9+AR4uK5uugcdO55hjmFi79OIFOpISUn3NT/5LISIz8OD7GWLo0xuICnaDpniMoPynnR3UxCHwfwFAkxDf2RBPCXmgosNs8rlpqB56jAIfxFdxLyViAzUg0zCXxF6hYjdWsMT5G6OjF2iq/wFNnlYpdu9R/sAP6Fm7T+woSzMGOoA8+Q4j7W11nCOpDOwSkP4BHztD91Drr8n/xxUVsjKk7f/ymaZi5q2n/stZjOi/hUD5ZbnXOR+RrHhqm9EKes7MV1rUePOdXk+jQrzaTM3sy3sZXWByKUSyJh5e36ycxsZvqT3ukNKAkxI9Z1lzW2vf02ll4Jz6qr/4ZO6/PN7bet+aDeaKf0Qd6mKBqGhrbmNnHKzoQ1fEVdtV827i6KQlyzvygUcTc7L8UeSKwMKqdoSSCNsG8tM3OsYx9xk9WoLETgU75P+EIeL8gjCNayNSBtPNE8yDsxGOojCLIUm2dIXlfpHgfwGw4l+tNIjBT5AAAAAElFTkSuQmCC) 100% no-repeat;
  background-size: 100% 100%;
  width: 80rpx;
}
.page .box_4 .group_4 .box_8 .image-text_3 .image-wrapper_1 .label_4 {
  width: 44rpx;
  height: 44rpx;
  margin: 18rpx 0 0 18rpx;
}
.page .box_4 .group_4 .box_8 .image-text_3 .text-group_3 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 4rpx;
}
.page .box_4 .group_4 .box_8 .box_12 {
  width: 80rpx;
  height: 124rpx;
  margin-left: 96rpx;
}
.page .box_4 .group_4 .box_8 .box_12 .image-wrapper_2 {
  background-color: rgb(239, 236, 255);
  border-radius: 50%;
  height: 80rpx;
  width: 80rpx;
}
.page .box_4 .group_4 .box_8 .box_12 .image-wrapper_2 .label_5 {
  width: 48rpx;
  height: 48rpx;
  margin: 16rpx 0 0 16rpx;
}
.page .box_4 .group_4 .box_8 .box_12 .text_12 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 4rpx;
}
.page .box_13 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 496rpx;
  width: 700rpx;
  position: relative;
  margin: -2rpx 0 0 24rpx;
}
.page .box_13 .text-wrapper_3 {
  width: 112rpx;
  height: 44rpx;
  margin: 32rpx 0 0 36rpx;
}
.page .box_13 .text-wrapper_3 .text_13 {
  width: 112rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_13 .group_5 {
  width: 660rpx;
  height: 190rpx;
  margin: 20rpx 0 210rpx 22rpx;
}
.page .box_13 .group_5 .box_14 {
  background-color: rgb(85, 101, 240);
  border-radius: 9px;
  width: 322rpx;
  height: 190rpx;
}
.page .box_13 .group_5 .box_14 .block_1 {
  width: 130rpx;
  height: 142rpx;
  margin: 26rpx 0 0 26rpx;
}
.page .box_13 .group_5 .box_14 .block_1 .text-group_4 {
  width: 130rpx;
  height: 74rpx;
}
.page .box_13 .group_5 .box_14 .block_1 .text-group_4 .text_14 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .group_5 .box_14 .block_1 .text-group_4 .text_15 {
  width: 130rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 36rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .box_13 .group_5 .box_14 .block_1 .text-wrapper_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 50px;
  height: 46rpx;
  margin-top: 22rpx;
  width: 108rpx;
}
.page .box_13 .group_5 .box_14 .block_1 .text-wrapper_4 .text_16 {
  width: 60rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(85, 101, 240);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 24rpx;
}
.page .box_13 .group_5 .box_14 .label_6 {
  width: 96rpx;
  height: 96rpx;
  margin: 48rpx 16rpx 0 54rpx;
}
.page .box_13 .group_5 .box_15 {
  background-color: rgb(19, 193, 220);
  border-radius: 9px;
  position: relative;
  width: 322rpx;
  height: 190rpx;
}
.page .box_13 .group_5 .box_15 .image-text_4 {
  width: 260rpx;
  height: 114rpx;
  margin: 28rpx 0 0 32rpx;
}
.page .box_13 .group_5 .box_15 .image-text_4 .text-group_5 {
  width: 130rpx;
  height: 74rpx;
}
.page .box_13 .group_5 .box_15 .image-text_4 .text-group_5 .text_17 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_13 .group_5 .box_15 .image-text_4 .text-group_5 .text_18 {
  width: 130rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 36rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 14rpx;
}
.page .box_13 .group_5 .box_15 .image-text_4 .label_7 {
  width: 92rpx;
  height: 92rpx;
  margin-top: 22rpx;
}
.page .box_13 .group_5 .box_15 .text-wrapper_5 {
  background-color: rgb(255, 255, 255);
  border-radius: 50px;
  height: 46rpx;
  width: 108rpx;
  position: absolute;
  left: 32rpx;
  top: 124rpx;
}
.page .box_13 .group_5 .box_15 .text-wrapper_5 .text_19 {
  width: 60rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(19, 193, 220);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 24rpx;
}
.page .box_13 .group_6 {
  background-color: rgb(19, 193, 220);
  border-radius: 9px;
  position: absolute;
  left: 22rpx;
  top: 308rpx;
  width: 322rpx;
  height: 190rpx;
}
.page .box_13 .group_6 .box_16 {
  width: 180rpx;
  height: 132rpx;
  margin: 28rpx 0 0 26rpx;
}
.page .box_13 .group_6 .box_16 .text-group_6 {
  width: 180rpx;
  height: 64rpx;
}
.page .box_13 .group_6 .box_16 .text-group_6 .text_20 {
  width: 180rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_13 .group_6 .box_16 .text-group_6 .text_21 {
  width: 100rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.8);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 20rpx;
}
.page .box_13 .group_6 .box_16 .text-wrapper_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 50px;
  height: 46rpx;
  margin-top: 22rpx;
  width: 108rpx;
}
.page .box_13 .group_6 .box_16 .text-wrapper_6 .text_22 {
  width: 80rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(19, 193, 220);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 14rpx;
}
.page .box_13 .group_6 .label_8 {
  width: 80rpx;
  height: 80rpx;
  margin: 66rpx 24rpx 0 12rpx;
}
.page .box_13 .group_7 {
  background-color: rgb(85, 101, 240);
  border-radius: 9px;
  position: absolute;
  left: 360rpx;
  top: 308rpx;
  width: 322rpx;
  height: 190rpx;
}
.page .box_13 .group_7 .text-group_7 {
  width: 120rpx;
  height: 64rpx;
  margin: 30rpx 0 0 32rpx;
}
.page .box_13 .group_7 .text-group_7 .text_23 {
  width: 120rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_13 .group_7 .text-group_7 .text_24 {
  width: 100rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.8);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 20rpx;
}
.page .box_13 .group_7 .text-wrapper_7 {
  background-color: rgb(255, 255, 255);
  border-radius: 50px;
  height: 46rpx;
  width: 108rpx;
  margin: 22rpx 0 28rpx 32rpx;
}
.page .box_13 .group_7 .text-wrapper_7 .text_25 {
  width: 80rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(85, 101, 240);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 14rpx;
}
.page .box_17 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 428rpx;
  width: 702rpx;
  position: relative;
  margin: 34rpx 0 76rpx 22rpx;
}
.page .box_17 .text-wrapper_8 {
  width: 112rpx;
  height: 20rpx;
  margin: 36rpx 0 0 32rpx;
}
.page .box_17 .text-wrapper_8 .text_26 {
  width: 112rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_17 .group_8 {
  width: 600rpx;
  height: 72rpx;
  margin: 50rpx 0 0 54rpx;
}
.page .box_17 .group_8 .image-wrapper_3 {
  height: 72rpx;
  width: 72rpx;
}
.page .box_17 .group_8 .image-wrapper_3 .label_9 {
  width: 68rpx;
  height: 64rpx;
  margin: 4rpx 0 0 2rpx;
}
.page .box_17 .group_8 .image-wrapper_4 {
  height: 72rpx;
  margin-left: 104rpx;
  width: 72rpx;
}
.page .box_17 .group_8 .image-wrapper_4 .label_10 {
  width: 68rpx;
  height: 68rpx;
  margin: 2rpx 0 0 2rpx;
}
.page .box_17 .group_8 .image-wrapper_5 {
  height: 72rpx;
  margin-left: 104rpx;
  width: 72rpx;
}
.page .box_17 .group_8 .image-wrapper_5 .label_11 {
  width: 68rpx;
  height: 64rpx;
  margin: 4rpx 0 0 2rpx;
}
.page .box_17 .group_8 .group_9 {
  width: 72rpx;
  height: 72rpx;
  margin-left: 104rpx;
}
.page .box_17 .text-wrapper_9 {
  width: 624rpx;
  height: 18rpx;
  margin: 28rpx 0 0 42rpx;
}
.page .box_17 .text-wrapper_9 .text_27 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_17 .text-wrapper_9 .text_28 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-left: 80rpx;
}
.page .box_17 .text-wrapper_9 .text_29 {
  width: 144rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-left: 56rpx;
}
.page .box_17 .text-wrapper_9 .text_30 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-left: 56rpx;
}
.page .box_17 .group_10 {
  width: 600rpx;
  height: 72rpx;
  margin: 54rpx 0 0 54rpx;
}
.page .box_17 .group_10 .image-wrapper_6 {
  height: 72rpx;
  width: 72rpx;
}
.page .box_17 .group_10 .image-wrapper_6 .label_12 {
  width: 68rpx;
  height: 66rpx;
  margin: 4rpx 0 0 2rpx;
}
.page .box_17 .group_10 .image-wrapper_7 {
  height: 72rpx;
  margin-left: 104rpx;
  width: 72rpx;
}
.page .box_17 .group_10 .image-wrapper_7 .label_13 {
  width: 64rpx;
  height: 68rpx;
  margin: 2rpx 0 0 4rpx;
}
.page .box_17 .group_10 .image-wrapper_8 {
  height: 72rpx;
  margin-left: 104rpx;
  width: 72rpx;
}
.page .box_17 .group_10 .image-wrapper_8 .label_14 {
  width: 58rpx;
  height: 68rpx;
  margin: 2rpx 0 0 8rpx;
}
.page .box_17 .group_10 .image-wrapper_9 {
  height: 72rpx;
  margin-left: 104rpx;
  width: 72rpx;
}
.page .box_17 .group_10 .image-wrapper_9 .label_15 {
  width: 68rpx;
  height: 68rpx;
  margin: 2rpx 0 0 2rpx;
}
.page .box_17 .text-wrapper_10 {
  width: 636rpx;
  height: 18rpx;
  margin: 22rpx 0 38rpx 42rpx;
}
.page .box_17 .text-wrapper_10 .text_31 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_17 .text-wrapper_10 .text_32 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-left: 80rpx;
}
.page .box_17 .text-wrapper_10 .text_33 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-left: 80rpx;
}
.page .box_17 .text-wrapper_10 .text_34 {
  width: 120rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-left: 68rpx;
}
.page .box_17 .image_2 {
  position: absolute;
  left: 584rpx;
  top: 110rpx;
  width: 136rpx;
  height: 66rpx;
}
.page .box_18 {
  background-image: -webkit-linear-gradient(336deg, rgb(245, 255, 253) 0, rgb(216, 255, 248) 100%);
  background-image: linear-gradient(114deg, rgb(245, 255, 253) 0, rgb(216, 255, 248) 100%);
  border-radius: 8px;
  position: absolute;
  left: 24rpx;
  top: 292rpx;
  width: 702rpx;
  height: 480rpx;
}
.page .box_18 .group_11 {
  width: 666rpx;
  height: 144rpx;
  margin: 24rpx 0 0 16rpx;
}
.page .box_18 .group_11 .image_3 {
  width: 144rpx;
  height: 144rpx;
}
.page .box_18 .group_11 .section_3 {
  width: 278rpx;
  height: 96rpx;
  margin: 8rpx 0 0 24rpx;
}
.page .box_18 .group_11 .section_3 .text_35 {
  width: 90rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_18 .group_11 .section_3 .section_4 {
  width: 278rpx;
  height: 36rpx;
  margin-top: 30rpx;
}
.page .box_18 .group_11 .section_3 .section_4 .text-wrapper_11 {
  background-color: rgb(11, 206, 148);
  border-radius: 2px;
  height: 36rpx;
  width: 82rpx;
}
.page .box_18 .group_11 .section_3 .section_4 .text-wrapper_11 .text_36 {
  width: 60rpx;
  height: 14rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 12rpx 0 0 10rpx;
}
.page .box_18 .group_11 .section_3 .section_4 .text-wrapper_12 {
  background-color: rgb(24, 200, 99);
  border-radius: 2px;
  height: 36rpx;
  margin-left: 8rpx;
  width: 82rpx;
}
.page .box_18 .group_11 .section_3 .section_4 .text-wrapper_12 .text_37 {
  width: 60rpx;
  height: 14rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 12rpx 0 0 10rpx;
}
.page .box_18 .group_11 .section_3 .section_4 .text-wrapper_13 {
  background-color: rgb(67, 80, 225);
  border-radius: 2px;
  height: 36rpx;
  margin-left: 8rpx;
  width: 98rpx;
}
.page .box_18 .group_11 .section_3 .section_4 .text-wrapper_13 .text_38 {
  width: 80rpx;
  height: 14rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 12rpx 0 0 8rpx;
}
.page .box_18 .group_11 .text-wrapper_14 {
  background-color: rgb(62, 200, 174);
  border-radius: 100px;
  height: 46rpx;
  width: 134rpx;
  margin: 8rpx 0 0 86rpx;
}
.page .box_18 .group_11 .text-wrapper_14 .text_39 {
  width: 96rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 12rpx 0 0 20rpx;
}
.page .box_18 .group_12 {
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  height: 294rpx;
  margin-top: 18rpx;
  width: 702rpx;
}
.page .box_18 .group_12 .group_13 {
  width: 540rpx;
  height: 30rpx;
  margin: 26rpx 0 0 68rpx;
}
.page .box_18 .group_12 .group_13 .text_40 {
  width: 122rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(102, 102, 102, 0.8);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 4rpx;
}
.page .box_18 .group_12 .group_13 .text-wrapper_15 {
  background-color: rgb(62, 200, 174);
  border-radius: 100px;
  height: 30rpx;
  margin-left: 12rpx;
  width: 60rpx;
}
.page .box_18 .group_12 .group_13 .text-wrapper_15 .text_41 {
  width: 40rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 0 0 10rpx;
}
.page .box_18 .group_12 .group_13 .text_42 {
  width: 122rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(102, 102, 102, 0.8);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 4rpx 0 0 154rpx;
}
.page .box_18 .group_12 .group_13 .text-wrapper_16 {
  background-color: rgb(62, 200, 174);
  border-radius: 100px;
  height: 30rpx;
  margin-left: 10rpx;
  width: 60rpx;
}
.page .box_18 .group_12 .group_13 .text-wrapper_16 .text_43 {
  width: 40rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 0 0 10rpx;
}
.page .box_18 .group_12 .text-wrapper_17 {
  width: 522rpx;
  height: 38rpx;
  margin: 18rpx 0 0 68rpx;
}
.page .box_18 .group_12 .text-wrapper_17 .text_44 {
  width: 174rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 44rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_18 .group_12 .text-wrapper_17 .text_45 {
  width: 174rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 44rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_18 .group_12 .group_14 {
  width: 664rpx;
  height: 124rpx;
  margin: 30rpx 0 28rpx 16rpx;
}
.page .box_18 .group_12 .group_14 .group_15 {
  background-color: rgb(62, 200, 174);
  border-radius: 5px;
  width: 322rpx;
  height: 124rpx;
}
.page .box_18 .group_12 .group_14 .group_15 .text-group_8 {
  width: 120rpx;
  height: 62rpx;
  margin: 30rpx 0 0 26rpx;
}
.page .box_18 .group_12 .group_14 .group_15 .text-group_8 .text_46 {
  width: 112rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_18 .group_12 .group_14 .group_15 .text-group_8 .text_47 {
  width: 120rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 20rpx;
}
.page .box_18 .group_12 .group_14 .group_15 .image-wrapper_10 {
  background-color: rgb(255, 255, 255);
  border-radius: 50%;
  height: 82rpx;
  width: 82rpx;
  margin: 20rpx 28rpx 0 66rpx;
}
.page .box_18 .group_12 .group_14 .group_15 .image-wrapper_10 .label_16 {
  width: 48rpx;
  height: 46rpx;
  margin: 16rpx 0 0 18rpx;
}
.page .box_18 .group_12 .group_14 .group_16 {
  background-color: rgb(231, 96, 81);
  border-radius: 5px;
  width: 322rpx;
  height: 124rpx;
}
.page .box_18 .group_12 .group_14 .group_16 .text-group_9 {
  width: 120rpx;
  height: 58rpx;
  margin: 30rpx 0 0 24rpx;
}
.page .box_18 .group_12 .group_14 .group_16 .text-group_9 .text_48 {
  width: 112rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_18 .group_12 .group_14 .group_16 .text-group_9 .text_49 {
  width: 120rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 16rpx;
}
.page .box_18 .group_12 .group_14 .group_16 .label_17 {
  width: 80rpx;
  height: 80rpx;
  margin: 20rpx 14rpx 0 84rpx;
}
