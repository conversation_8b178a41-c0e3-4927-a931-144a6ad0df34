@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 1714rpx;
  overflow: hidden;
}
.page .section_1 {
  width: 750rpx;
  height: 684rpx;
}
.page .section_1 .group_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .section_1 .group_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .section_1 .group_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .section_1 .group_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .section_1 .group_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .section_1 .group_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .section_1 .group_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .section_1 .group_2 .text_2 {
  width: 140rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .section_1 .group_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 348rpx;
}
.page .section_1 .text-wrapper_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 88rpx;
  margin-top: 2rpx;
}
.page .section_1 .text-wrapper_1 .text_3 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 28rpx 0 0 122rpx;
}
.page .section_1 .text-wrapper_1 .text_4 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 28rpx 126rpx 0 262rpx;
}
.page .section_1 .group_3 {
  height: 218rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAXcAAABtCAYAAABa+iG3AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGMSURBVHgB7dRBFQAQAEAxXNWXSyV6/LeF2Nz3vAFAyhoA5MgdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwj60K0DRqrbMFsAAAAASUVORK5CYII=) 100% no-repeat;
  background-size: 100% 100%;
  width: 750rpx;
  position: relative;
}
.page .section_1 .group_3 .text_5 {
  width: 128rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 44rpx 0 0 24rpx;
}
.page .section_1 .group_3 .box_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  position: absolute;
  left: 24rpx;
  top: 102rpx;
  width: 702rpx;
  height: 232rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 {
  position: relative;
  width: 526rpx;
  height: 182rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .image_2 {
  width: 182rpx;
  height: 182rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .text-group_1 {
  width: 322rpx;
  height: 176rpx;
  margin: 4rpx 0 0 22rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .text-group_1 .text_6 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .text-group_1 .text_7 {
  width: 248rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(152, 152, 152);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 20rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .text-group_1 .box_2 {
  width: 322rpx;
  height: 36rpx;
  margin-top: 66rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .text-group_1 .box_2 .text-wrapper_2 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 200rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .text-group_1 .box_2 .text-wrapper_2 .text_8 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 16rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 200rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .text-group_1 .box_2 .text-wrapper_2 .text_9 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .text-group_1 .box_2 .text_10 {
  width: 104rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  text-decoration: line-through;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .text-wrapper_3 {
  background-image: -webkit-linear-gradient(left, rgb(255, 239, 190) 0, rgb(254, 230, 157) 100%);
  background-image: linear-gradient(90deg, rgb(255, 239, 190) 0, rgb(254, 230, 157) 100%);
  border-radius: 10px 10px 0px 10px;
  height: 30rpx;
  width: 120rpx;
  margin: 144rpx 0 0 -236rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .text-wrapper_3 .text_11 {
  width: 100rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(98, 59, 4);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 0 0 10rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_4 {
  border-radius: 4px;
  width: 154rpx;
  height: 30rpx;
  border: 1px solid rgb(231, 96, 81);
  margin: 92rpx 44rpx 0 -82rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_4 .group_5 {
  background-color: rgb(231, 96, 81);
  border-radius: 4px 0px 4px 0px;
  height: 30rpx;
  width: 34rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_4 .group_5 .box_3 {
  background-color: rgb(255, 207, 202);
  width: 16rpx;
  height: 20rpx;
  margin: 4rpx 0 0 10rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_4 .text_12 {
  width: 106rpx;
  height: 16rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 6rpx 0 8rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_6 {
  background-image: -webkit-linear-gradient(top, rgb(59, 41, 0) 0, rgb(86, 54, 0) 100%);
  background-image: linear-gradient(180deg, rgb(59, 41, 0) 0, rgb(86, 54, 0) 100%);
  border-radius: 4px;
  position: absolute;
  left: 204rpx;
  top: 92rpx;
  width: 112rpx;
  height: 30rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_6 .image-text_2 {
  width: 90rpx;
  height: 20rpx;
  margin: 6rpx 0 0 12rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_6 .image-text_2 .section_2 {
  background-color: rgb(255, 234, 184);
  width: 20rpx;
  height: 20rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_6 .image-text_2 .text-group_2 {
  width: 64rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(255, 234, 184);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_6 .image-text_3 {
  position: absolute;
  left: 12rpx;
  top: 6rpx;
  width: 90rpx;
  height: 20rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_6 .image-text_3 .section_2 {
  background-color: rgb(255, 234, 184);
  width: 20rpx;
  height: 20rpx;
}
.page .section_1 .group_3 .box_1 .image-text_1 .group_6 .image-text_3 .text-group_2 {
  width: 64rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(255, 234, 184);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_1 .text-wrapper_4 {
  width: 518rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 144rpx 0 28rpx 24rpx;
}
.page .section_1 .text-wrapper_4 .text_13 {
  width: 518rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_1 .text-wrapper_4 .text_14 {
  width: 518rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 26rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 826rpx;
  width: 702rpx;
  margin: -2rpx 0 0 24rpx;
}
.page .section_3 .list_1 {
  width: 702rpx;
  height: 826rpx;
}
.page .section_3 .list_1 .list-items_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 274rpx;
  width: 702rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 {
  width: 654rpx;
  height: 182rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 {
  position: relative;
  width: 504rpx;
  height: 182rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .image_3 {
  width: 182rpx;
  height: 182rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .text-group_3 {
  width: 300rpx;
  height: 176rpx;
  margin: 4rpx 0 0 22rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .text-group_3 .text_15 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .text-group_3 .text_16 {
  width: 248rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(152, 152, 152);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 20rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .text-group_3 .block_1 {
  width: 300rpx;
  height: 36rpx;
  margin-top: 66rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .text-group_3 .block_1 .text-wrapper_5 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 200rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .text-group_3 .block_1 .text-wrapper_5 .text_17 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 16rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 200rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .text-group_3 .block_1 .text-wrapper_5 .text_18 {
  width: 82rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .text-group_3 .block_1 .text_19 {
  width: 104rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  text-decoration: line-through;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 6rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .text-wrapper_6 {
  background-image: -webkit-linear-gradient(left, rgb(255, 239, 190) 0, rgb(254, 230, 157) 100%);
  background-image: linear-gradient(90deg, rgb(255, 239, 190) 0, rgb(254, 230, 157) 100%);
  border-radius: 10px 10px 0px 10px;
  height: 30rpx;
  width: 100rpx;
  margin: 144rpx 0 0 -214rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .text-wrapper_6 .text_20 {
  width: 80rpx;
  height: 16rpx;
  overflow-wrap: break-word;
  color: rgb(98, 59, 4);
  font-size: 16rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 8rpx 0 0 10rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .section_4 {
  border-radius: 4px;
  width: 154rpx;
  height: 30rpx;
  border: 1px solid rgb(231, 96, 81);
  margin: 92rpx 22rpx 0 -62rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .section_4 .section_5 {
  background-color: rgb(231, 96, 81);
  border-radius: 4px 0px 4px 0px;
  height: 30rpx;
  width: 34rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .section_4 .section_5 .block_2 {
  background-color: rgb(255, 207, 202);
  width: 16rpx;
  height: 20rpx;
  margin: 4rpx 0 0 10rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .section_4 .text_21 {
  width: 106rpx;
  height: 16rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 6rpx 6rpx 0 8rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .section_6 {
  background-image: -webkit-linear-gradient(top, rgb(59, 41, 0) 0, rgb(86, 54, 0) 100%);
  background-image: linear-gradient(180deg, rgb(59, 41, 0) 0, rgb(86, 54, 0) 100%);
  border-radius: 4px;
  position: absolute;
  left: 204rpx;
  top: 92rpx;
  width: 112rpx;
  height: 30rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .section_6 .image-text_5 {
  width: 90rpx;
  height: 20rpx;
  margin: 6rpx 0 0 12rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .section_6 .image-text_5 .group_8 {
  background-color: rgb(255, 234, 184);
  width: 20rpx;
  height: 20rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .image-text_4 .section_6 .image-text_5 .text-group_4 {
  width: 64rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(255, 234, 184);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .list_1 .list-items_1 .group_7 .box_4 {
  border-radius: 50%;
  width: 44rpx;
  height: 44rpx;
  border: 1px solid rgb(153, 153, 153);
  margin-top: 90rpx;
}
.page .section_3 .list_1 .list-items_1 .group_9 {
  width: 214rpx;
  height: 28rpx;
  margin: 12rpx 0 28rpx 228rpx;
}
.page .section_3 .list_1 .list-items_1 .group_9 .text-wrapper_7 {
  width: 214rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .list_1 .list-items_1 .group_9 .text-wrapper_7 .text_22 {
  width: 214rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .list_1 .list-items_1 .group_9 .text-wrapper_7 .text_23 {
  width: 214rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_3 .list_1 .list-items_1 .group_9 .text-wrapper_7 .text_24 {
  width: 214rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_7 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 126rpx;
  margin-top: 80rpx;
}
.page .section_7 .text-wrapper_8 {
  border-radius: 100px;
  height: 88rpx;
  border: 1px solid rgb(153, 153, 153);
  width: 330rpx;
  margin: 20rpx 0 0 30rpx;
}
.page .section_7 .text-wrapper_8 .text_25 {
  width: 196rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 68rpx;
}
.page .section_7 .text-wrapper_9 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 88rpx;
  width: 330rpx;
  margin: 20rpx 30rpx 0 0;
}
.page .section_7 .text-wrapper_9 .text_26 {
  width: 60rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 136rpx;
}
