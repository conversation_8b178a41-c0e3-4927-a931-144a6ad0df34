{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/nearby_technicians/index.vue?278b", "webpack:///./src/pages/nearby_technicians/index.vue?53a0", "webpack:///./src/pages/nearby_technicians/index.vue?7aad", "webpack:///./src/pages/nearby_technicians/index.vue?f681", "uni-app:///src/pages/nearby_technicians/index.vue", "webpack:///./src/pages/nearby_technicians/index.vue?cb1c"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "constants", "tabsIndex", "layoutMode", "getLayoutModeFromCache", "<PERSON><PERSON><PERSON><PERSON>", "isLocating", "selectedTechnician", "isMarkerClicked", "mapAnimating", "mapCenter", "latitude", "longitude", "mapScale", "mapMarkers", "mapLoaded", "isH5", "h5Map", "resizeTimer", "serviceItems", "id", "text", "technicianList", "name", "status", "availableTime", "earliestTime", "rating", "serviceCount", "freeTravel", "comments", "favorites", "shopType", "distance", "lanhuBg13", "avatar", "freeIcon", "mounted", "_this", "console", "log", "checkEnvironment", "initMapMarkers", "$nextTick", "initH5Map", "<PERSON><PERSON><PERSON><PERSON>", "methods", "_this2", "loadLeafletLibrary", "then", "createLeafletMap", "catch", "err", "error", "Promise", "resolve", "reject", "window", "L", "cssLink", "document", "createElement", "rel", "href", "head", "append<PERSON><PERSON><PERSON>", "script", "src", "onload", "onerror", "_this3", "mapContainer", "getElementById", "map", "attributionControl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "attribution", "max<PERSON><PERSON>", "errorTileUrl", "addTo", "on", "<PERSON><PERSON><PERSON>er", "subdomains", "when<PERSON><PERSON><PERSON>", "addLeafletMarkers", "getCenter", "getZoom", "closeSelectedTechnician", "setMapContainerHeight", "_this4", "for<PERSON>ach", "technician", "customIcon", "divIcon", "className", "html", "concat", "iconSize", "iconAnchor", "marker", "icon", "onTechnicianClick", "_this5", "viewportHeight", "innerHeight", "headerElements", "querySelectorAll", "headerHeight", "el", "offsetHeight", "bottomNavHeight", "margins", "availableHeight", "minHeight", "maxHeight", "finalHeight", "Math", "max", "min", "querySelector", "leafletContainer", "style", "height", "setTimeout", "invalidateSize", "handleWindowResize", "_this6", "clearTimeout", "_this7", "uni", "showToast", "title", "cachedLayoutMode", "getStorageSync", "saveLayoutModeToCache", "setStorageSync", "switchTab", "index", "_this8", "_this9", "offsetLat", "random", "offsetLng", "iconPath", "width", "callout", "content", "color", "fontSize", "borderRadius", "bgColor", "padding", "display", "onMarkerTap", "_this0", "markerId", "detail", "find", "item", "onRegionChange", "centerToUserLocation", "_this1", "getLocation", "type", "success", "res", "fail", "zoomIn", "zoomOut", "getMarkerStyle", "handleLocationClick", "_this10", "_asyncToGenerator", "_regenerator", "m", "_callee", "platform", "location", "_t", "w", "_context", "p", "n", "a", "showLoading", "mask", "getSystemInfoSync", "getH5Location", "v", "getUniLocation", "getAddressByLocation", "handleLocationError", "hideLoading", "f", "navigator", "geolocation", "Error", "getCurrentPosition", "position", "coords", "message", "code", "PERMISSION_DENIED", "POSITION_UNAVAILABLE", "TIMEOUT", "enableHighAccuracy", "timeout", "maximumAge", "errMsg", "includes", "_this11", "_callee2", "mockAddresses", "_context2", "floor", "length", "toFixed", "duration", "onMapClick", "refreshMapDisplay", "_this12"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAuD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFpCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCictd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,SAAA;MAAA;MACAC,UAAA,OAAAC,sBAAA;MACA;MACAC,cAAA;MAAA;MACAC,UAAA;MAAA;MACA;MACAC,kBAAA;MAAA;MACAC,eAAA;MAAA;MACA;MACAC,YAAA;MAAA;MACA;MACAC,SAAA;QACAC,QAAA;QAAA;QACAC,SAAA;MACA;MACAC,QAAA;MAAA;MACAC,UAAA;MAAA;MACAC,SAAA;MAAA;MACAC,IAAA;MAAA;MACAC,KAAA;MAAA;MACAC,WAAA;MAAA;MACAC,YAAA,GACA;QAAAC,EAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAC,IAAA;MAAA,EACA;MACA;MACAC,cAAA,GACA;QACAF,EAAA;QACAG,IAAA;QACAC,MAAA;QACAC,aAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,MAAA;QACAC,QAAA;QACA;QACAzB,QAAA;QACAC,SAAA;MACA,GACA;QACAQ,EAAA;QACAG,IAAA;QACAC,MAAA;QACAC,aAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,MAAA;QACAC,QAAA;QACA;QACAzB,QAAA;QACAC,SAAA;MACA,GACA;QACAQ,EAAA;QACAG,IAAA;QACAC,MAAA;QACAC,aAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,MAAA;QACAC,QAAA;QACA;QACAzB,QAAA;QACAC,SAAA;MACA,GACA;QACAQ,EAAA;QACAG,IAAA;QACAC,MAAA;QACAC,aAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,MAAA;QACAC,QAAA;QACA;QACAzB,QAAA;QACAC,SAAA;MACA;IAEA;EACA;EACAyB,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACAC,OAAA,CAAAC,GAAA,sBAAArC,UAAA;;IAEA;IACA,KAAAsC,gBAAA;;IAEA;IACA,KAAAC,cAAA;;IAEA;IACA,SAAA1B,IAAA;MACA;;MAMA,KAAA2B,SAAA;QACAL,KAAA,CAAAM,SAAA;MACA;IACA;EACA;EAEAC,aAAA,WAAAA,cAAA;IACA;EAAA,CAOA;EAEAC,OAAA;IACA;IACAL,gBAAA,WAAAA,iBAAA;MAOA,KAAAzB,IAAA;MACAuB,OAAA,CAAAC,GAAA;IAEA;IAEA;IACAI,SAAA,WAAAA,UAAA;MAAA,IAAAG,MAAA;MACA,UAAA/B,IAAA;MAEAuB,OAAA,CAAAC,GAAA;;MAEA;MACA,KAAAQ,kBAAA,GAAAC,IAAA;QACAV,OAAA,CAAAC,GAAA;QACAO,MAAA,CAAAG,gBAAA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAb,OAAA,CAAAc,KAAA,kBAAAD,GAAA;QACAL,MAAA,CAAAhC,SAAA;MACA;IACA;IAEA;IACAiC,kBAAA,WAAAA,mBAAA;MACA,WAAAM,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA;QACA,IAAAC,MAAA,CAAAC,CAAA;UACAnB,OAAA,CAAAC,GAAA;UACAe,OAAA;UACA;QACA;QAEAhB,OAAA,CAAAC,GAAA;;QAEA;QACA,IAAAmB,OAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,OAAA,CAAAG,GAAA;QACAH,OAAA,CAAAI,IAAA;QACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,OAAA;;QAEA;QACA,IAAAO,MAAA,GAAAN,QAAA,CAAAC,aAAA;QACAK,MAAA,CAAAC,GAAA;QACAD,MAAA,CAAAE,MAAA;UACA7B,OAAA,CAAAC,GAAA;UACAe,OAAA;QACA;QACAW,MAAA,CAAAG,OAAA,aAAAhB,KAAA;UACAd,OAAA,CAAAc,KAAA,kBAAAA,KAAA;UACAG,MAAA,CAAAH,KAAA;QACA;QACAO,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAC,MAAA;MACA;IACA;IAEA;IACAhB,gBAAA,WAAAA,iBAAA;MAAA,IAAAoB,MAAA;MACA;QACA/B,OAAA,CAAAC,GAAA;QACA,IAAA+B,YAAA,GAAAX,QAAA,CAAAY,cAAA;QACA,KAAAD,YAAA;UACAhC,OAAA,CAAAc,KAAA;UACA,KAAAtC,SAAA;UACA;QACA;QAEAwB,OAAA,CAAAC,GAAA;;QAEA;QACA,KAAAvB,KAAA,GAAAyC,CAAA,CAAAe,GAAA;UACAC,kBAAA;QACA,GAAAC,OAAA,CACA,MAAAjE,SAAA,CAAAC,QAAA,OAAAD,SAAA,CAAAE,SAAA,GACA,KAAAC,QACA;;QAEA;QACA;QACA,IAAA+D,SAAA,GAAAlB,CAAA,CAAAkB,SAAA;UACAC,WAAA;UAAA;UACAC,OAAA;UACAC,YAAA;QACA;;QAEA;QACAH,SAAA,CAAAI,KAAA,MAAA/D,KAAA;;QAEA;QACA2D,SAAA,CAAAK,EAAA;UACA1C,OAAA,CAAAC,GAAA;UACA;UACA8B,MAAA,CAAArD,KAAA,CAAAiE,WAAA,CAAAN,SAAA;UACA;UACAlB,CAAA,CAAAkB,SAAA;YACAC,WAAA;YAAA;YACAC,OAAA;YACAK,UAAA;UACA,GAAAH,KAAA,CAAAV,MAAA,CAAArD,KAAA;QACA;QAEAsB,OAAA,CAAAC,GAAA;;QAEA;QACA,KAAAvB,KAAA,CAAAmE,SAAA;UACA7C,OAAA,CAAAC,GAAA;UACA;UACA8B,MAAA,CAAAe,iBAAA;UACAf,MAAA,CAAAvD,SAAA;UACAwB,OAAA,CAAAC,GAAA;QACA;;QAEA;QACA,KAAAvB,KAAA,CAAAgE,EAAA;UACA1C,OAAA,CAAAC,GAAA,kBAAA8B,MAAA,CAAArD,KAAA,CAAAqE,SAAA;QACA;QAEA,KAAArE,KAAA,CAAAgE,EAAA;UACA1C,OAAA,CAAAC,GAAA,mBAAA8B,MAAA,CAAArD,KAAA,CAAAsE,OAAA;QACA;;QAEA;QACA,KAAAtE,KAAA,CAAAgE,EAAA;UACA;UACA,IAAAX,MAAA,CAAA9D,eAAA;YACA;UACA;UACA;UACA,IAAA8D,MAAA,CAAA/D,kBAAA;YACA+D,MAAA,CAAAkB,uBAAA;UACA;QACA;;QAEA;QACA,KAAAC,qBAAA;MACA,SAAApC,KAAA;QACAd,OAAA,CAAAc,KAAA,mBAAAA,KAAA;QACA,KAAAtC,SAAA;MACA;IACA;IAEA;IACAsE,iBAAA,WAAAA,kBAAA;MAAA,IAAAK,MAAA;MACA,UAAAzE,KAAA;QACAsB,OAAA,CAAAC,GAAA;QACA;MACA;MAEAD,OAAA,CAAAC,GAAA;MAEA,KAAAlB,cAAA,CAAAqE,OAAA,WAAAC,UAAA;QACA,IAAAA,UAAA,CAAAjF,QAAA,IAAAiF,UAAA,CAAAhF,SAAA;UACA;UACA,IAAAiF,UAAA,GAAAnC,CAAA,CAAAoC,OAAA;YACAC,SAAA;YACAC,IAAA,2RAAAC,MAAA,CAEAL,UAAA,CAAAzD,MAAA,8bAAA8D,MAAA,CAEAL,UAAA,CAAArE,IAAA,2IAAA0E,MAAA,CACAL,UAAA,CAAA3D,QAAA,uEAGA;YACAiE,QAAA;YAAA;YACAC,UAAA;UACA;;UAEA;UACA,IAAAC,MAAA,GAAA1C,CAAA,CAAA0C,MAAA,EAAAR,UAAA,CAAAjF,QAAA,EAAAiF,UAAA,CAAAhF,SAAA;YACAyF,IAAA,EAAAR;UACA,GAAAb,KAAA,CAAAU,MAAA,CAAAzE,KAAA;;UAEA;UACAmF,MAAA,CAAAnB,EAAA;YACAS,MAAA,CAAAY,iBAAA,CAAAV,UAAA;UACA;UAEArD,OAAA,CAAAC,GAAA,0CAAAyD,MAAA,CAAAL,UAAA,CAAArE,IAAA;QACA;MACA;IACA;IAEA;IACAkE,qBAAA,WAAAA,sBAAA;MAAA,IAAAc,MAAA;MACA,UAAAvF,IAAA;MAEA,KAAA2B,SAAA;QACA;UACA;UACA,IAAA6D,cAAA,GAAA/C,MAAA,CAAAgD,WAAA;;UAEA;UACA,IAAAC,cAAA,GAAA9C,QAAA,CAAA+C,gBAAA;UACA,IAAAC,YAAA;UACAF,cAAA,CAAAf,OAAA,WAAAkB,EAAA;YACAD,YAAA,IAAAC,EAAA,CAAAC,YAAA;UACA;;UAEA;UACA,IAAAC,eAAA;UACA,IAAAC,OAAA;UACA,IAAAC,eAAA,GAAAT,cAAA,GAAAI,YAAA,GAAAG,eAAA,GAAAC,OAAA;;UAEA;UACA,IAAAE,SAAA;UACA,IAAAC,SAAA,GAAAX,cAAA;UACA,IAAAY,WAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAJ,SAAA,EAAAG,IAAA,CAAAE,GAAA,CAAAN,eAAA,EAAAE,SAAA;UAEA5E,OAAA,CAAAC,GAAA;YACAgE,cAAA,EAAAA,cAAA;YACAI,YAAA,EAAAA,YAAA;YACAK,eAAA,EAAAA,eAAA;YACAG,WAAA,EAAAA;UACA;;UAEA;UACA,IAAA7C,YAAA,GAAAX,QAAA,CAAA4D,aAAA,sBAAA5D,QAAA,CAAA4D,aAAA;UACA,IAAAC,gBAAA,GAAA7D,QAAA,CAAAY,cAAA;UAEAjC,OAAA,CAAAC,GAAA;YACA+B,YAAA,EAAAA,YAAA;YACAkD,gBAAA,EAAAA,gBAAA;YACAL,WAAA,EAAAA,WAAA;UACA;UAEA,IAAA7C,YAAA;YACAA,YAAA,CAAAmD,KAAA,CAAAC,MAAA,GAAAP,WAAA;YACA7C,YAAA,CAAAmD,KAAA,CAAAR,SAAA,GAAAE,WAAA;YACA7E,OAAA,CAAAC,GAAA,gBAAA4E,WAAA;UACA;UAEA,IAAAK,gBAAA;YACAA,gBAAA,CAAAC,KAAA,CAAAC,MAAA,GAAAP,WAAA;YACAK,gBAAA,CAAAC,KAAA,CAAAR,SAAA,GAAAE,WAAA;YACA7E,OAAA,CAAAC,GAAA,qBAAA4E,WAAA;UACA;;UAEA;UACA,IAAAb,MAAA,CAAAtF,KAAA;YACA2G,UAAA;cACArB,MAAA,CAAAtF,KAAA,CAAA4G,cAAA;cACAtF,OAAA,CAAAC,GAAA;YACA;UACA;QACA,SAAAa,KAAA;UACAd,OAAA,CAAAc,KAAA,cAAAA,KAAA;QACA;MACA;IACA;IAEA;IACAyE,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,UAAA/G,IAAA;;MAEA;MACAgH,YAAA,MAAA9G,WAAA;MACA,KAAAA,WAAA,GAAA0G,UAAA;QACArF,OAAA,CAAAC,GAAA;QACAuF,MAAA,CAAAtC,qBAAA;MACA;IACA;IAEA;IACAa,iBAAA,WAAAA,kBAAAV,UAAA;MAAA,IAAAqC,MAAA;MACA1F,OAAA,CAAAC,GAAA,UAAAoD,UAAA,CAAArE,IAAA;MACA,KAAAhB,kBAAA,GAAAqF,UAAA;MACA;MACA,KAAApF,eAAA;MACAoH,UAAA;QACAK,MAAA,CAAAzH,eAAA;MACA;MACA0H,GAAA,CAAAC,SAAA;QACAC,KAAA,uBAAAnC,MAAA,CAAAL,UAAA,CAAArE,IAAA;QACA8E,IAAA;MACA;IACA;IAEA;IACAjG,sBAAA,WAAAA,uBAAA;MACA;QACA,IAAAiI,gBAAA,GAAAH,GAAA,CAAAI,cAAA;QACA,IAAAD,gBAAA;UACA9F,OAAA,CAAAC,GAAA,eAAA6F,gBAAA;UACA,OAAAA,gBAAA;QACA;MACA,SAAA7I,CAAA;QACA+C,OAAA,CAAAC,GAAA,YAAAhD,CAAA;MACA;MACA;MACA;IACA;IAEA;IACA+I,qBAAA,WAAAA,sBAAApI,UAAA;MACA;QACA+H,GAAA,CAAAM,cAAA,eAAArI,UAAA;QACAoC,OAAA,CAAAC,GAAA,gBAAArC,UAAA;MACA,SAAAX,CAAA;QACA+C,OAAA,CAAAC,GAAA,YAAAhD,CAAA;MACA;IACA;IAEA;IACAiJ,SAAA,WAAAA,UAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,KAAA;QACA;QACA,KAAAxI,SAAA;QACAqC,OAAA,CAAAC,GAAA;;QAEA;QACA,KAAA/B,YAAA;QAEA,KAAAkC,SAAA;UACA;UACAiF,UAAA;YACArF,OAAA,CAAAC,GAAA;YAEA,IAAAmG,MAAA,CAAA3H,IAAA,IAAA2H,MAAA,CAAA1H,KAAA;cACA;cACAsB,OAAA,CAAAC,GAAA;cACAmG,MAAA,CAAA1H,KAAA,CAAA4G,cAAA;YACA,WAAAc,MAAA,CAAA3H,IAAA,KAAA2H,MAAA,CAAA1H,KAAA;cACA;cACAsB,OAAA,CAAAC,GAAA;cACAmG,MAAA,CAAA/F,SAAA;YACA;cACA;cACAL,OAAA,CAAAC,GAAA;cACAmG,MAAA,CAAA5H,SAAA;cACA4H,MAAA,CAAAhG,SAAA;gBACAgG,MAAA,CAAA5H,SAAA;cACA;YACA;;YAEA;YACA6G,UAAA;cACAe,MAAA,CAAAlI,YAAA;YACA;UACA;QACA;MACA,WAAAiI,KAAA;QACA;QACA,SAAAxI,SAAA;UACA;UACA,KAAAC,UAAA,QAAAA,UAAA;UACA;UACA,KAAAoI,qBAAA,MAAApI,UAAA;UACAoC,OAAA,CAAAC,GAAA,mBAAArC,UAAA;QACA;UACA;UACA,KAAAD,SAAA;UACAqC,OAAA,CAAAC,GAAA;UACA;UACA,SAAAjC,kBAAA;YACA,KAAAiF,uBAAA;UACA;QACA;MACA;IACA;IAEA;IACA9C,cAAA,WAAAA,eAAA;MAAA,IAAAkG,MAAA;MACArG,OAAA,CAAAC,GAAA;;MAEA;MACA,KAAA1B,UAAA,QAAAQ,cAAA,CAAAmD,GAAA,WAAAmB,UAAA;QACA;QACA,IAAAiD,SAAA,IAAAxB,IAAA,CAAAyB,MAAA;QACA,IAAAC,SAAA,IAAA1B,IAAA,CAAAyB,MAAA;QAEA;UACA1H,EAAA,EAAAwE,UAAA,CAAAxE,EAAA;UACAT,QAAA,EAAAiF,UAAA,CAAAjF,QAAA,GAAAkI,SAAA;UACAjI,SAAA,EAAAgF,UAAA,CAAAhF,SAAA,GAAAmI,SAAA;UACAX,KAAA,EAAAxC,UAAA,CAAArE,IAAA;UACAyH,QAAA;UAAA;UACAC,KAAA;UACAtB,MAAA;UACAuB,OAAA;YACAC,OAAA,KAAAlD,MAAA,CAAAL,UAAA,CAAArE,IAAA,OAAA0E,MAAA,CAAAL,UAAA,CAAA3D,QAAA;YACAmH,KAAA;YACAC,QAAA;YACAC,YAAA;YACAC,OAAA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;MACA;MAEAlH,OAAA,CAAAC,GAAA,qBAAA1B,UAAA;;MAEA;MACA8G,UAAA;QACAgB,MAAA,CAAA7H,SAAA;QACAwB,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAkH,WAAA,WAAAA,YAAAlK,CAAA;MAAA,IAAAmK,MAAA;MACA,IAAAC,QAAA,GAAApK,CAAA,CAAAqK,MAAA,CAAAD,QAAA;MACA,IAAAhE,UAAA,QAAAtE,cAAA,CAAAwI,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA3I,EAAA,KAAAwI,QAAA;MAAA;MACA,IAAAhE,UAAA;QACArD,OAAA,CAAAC,GAAA,aAAAoD,UAAA,CAAArE,IAAA;QACA,KAAAhB,kBAAA,GAAAqF,UAAA;QACA;QACA,KAAApF,eAAA;QACAoH,UAAA;UACA+B,MAAA,CAAAnJ,eAAA;QACA;QACA0H,GAAA,CAAAC,SAAA;UACAC,KAAA,qCAAAnC,MAAA,CAAAL,UAAA,CAAArE,IAAA;UACA8E,IAAA;QACA;MACA;IACA;IAEA;IACA2D,cAAA,WAAAA,eAAAxK,CAAA;MACA+C,OAAA,CAAAC,GAAA,YAAAhD,CAAA,CAAAqK,MAAA;IACA;IAEA;IACAI,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA;MACAhC,GAAA,CAAAiC,WAAA;QACAC,IAAA;QACAC,OAAA,WAAAA,QAAAC,GAAA;UACAJ,MAAA,CAAAxJ,SAAA;YACAC,QAAA,EAAA2J,GAAA,CAAA3J,QAAA;YACAC,SAAA,EAAA0J,GAAA,CAAA1J;UACA;UACA2B,OAAA,CAAAC,GAAA,aAAA0H,MAAA,CAAAxJ,SAAA;QACA;QACA6J,IAAA,WAAAA,KAAAnH,GAAA;UACAb,OAAA,CAAAc,KAAA,YAAAD,GAAA;UACA8E,GAAA,CAAAC,SAAA;YACAC,KAAA;YACA/B,IAAA;UACA;QACA;MACA;IACA;IAEAmE,MAAA,WAAAA,OAAA;MACA,SAAA3J,QAAA;QACA,KAAAA,QAAA;QACA0B,OAAA,CAAAC,GAAA,sBAAA3B,QAAA;MACA;IACA;IAEA4J,OAAA,WAAAA,QAAA;MACA,SAAA5J,QAAA;QACA,KAAAA,QAAA;QACA0B,OAAA,CAAAC,GAAA,sBAAA3B,QAAA;MACA;IACA;IAEA;IACA6J,cAAA,WAAAA,eAAA;MACA;QACAjB,OAAA;MACA;IACA;IAEA;IACAkB,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAjK,IAAA,EAAAkK,QAAA,EAAAC,EAAA;QAAA,OAAAL,YAAA,GAAAM,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAA,KACAX,OAAA,CAAAtK,UAAA;gBAAA+K,QAAA,CAAAE,CAAA;gBAAA;cAAA;cAAA,OAAAF,QAAA,CAAAG,CAAA;YAAA;cAIAZ,OAAA,CAAAtK,UAAA;cAAA+K,QAAA,CAAAC,CAAA;cAGA;cACApD,GAAA,CAAAuD,WAAA;gBACArD,KAAA;gBACAsD,IAAA;cACA;;cAEA;cACAT,QAAA,GAAA/C,GAAA,CAAAyD,iBAAA,GAAAV,QAAA;cACAjK,IAAA,GAAAiK,QAAA,oBAAAxH,MAAA;cAAA,KAIAzC,IAAA;gBAAAqK,QAAA,CAAAE,CAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,CAAA;cAAA,OAEAX,OAAA,CAAAgB,aAAA;YAAA;cAAAV,QAAA,GAAAG,QAAA,CAAAQ,CAAA;cAAAR,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,CAAA;cAAA,OAGAX,OAAA,CAAAkB,cAAA;YAAA;cAAAZ,QAAA,GAAAG,QAAA,CAAAQ,CAAA;YAAA;cAAA,KAGAX,QAAA;gBAAAG,QAAA,CAAAE,CAAA;gBAAA;cAAA;cACA;cACAX,OAAA,CAAAlK,SAAA;gBACAC,QAAA,EAAAuK,QAAA,CAAAvK,QAAA;gBACAC,SAAA,EAAAsK,QAAA,CAAAtK;cACA;;cAEA;cAAAyK,QAAA,CAAAE,CAAA;cAAA,OACAX,OAAA,CAAAmB,oBAAA,CAAAb,QAAA,CAAAvK,QAAA,EAAAuK,QAAA,CAAAtK,SAAA;YAAA;cAEA;cACAsH,GAAA,CAAAC,SAAA;gBACAC,KAAA;gBACA/B,IAAA;cACA;;cAEA;cACA,IAAAuE,OAAA,CAAA1K,SAAA;gBACA0K,OAAA,CAAAlI,cAAA;gBACA,IAAAkI,OAAA,CAAA5J,IAAA,IAAA4J,OAAA,CAAA3J,KAAA;kBACA2J,OAAA,CAAA3J,KAAA,CAAA0D,OAAA,EAAAuG,QAAA,CAAAvK,QAAA,EAAAuK,QAAA,CAAAtK,SAAA,GAAAgK,OAAA,CAAA/J,QAAA;gBACA;cACA;YAAA;cAAAwK,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAQ,CAAA;cAIAtJ,OAAA,CAAAc,KAAA,UAAA8H,EAAA;cACAP,OAAA,CAAAoB,mBAAA,CAAAb,EAAA;YAAA;cAAAE,QAAA,CAAAC,CAAA;cAEAV,OAAA,CAAAtK,UAAA;cACA4H,GAAA,CAAA+D,WAAA;cAAA,OAAAZ,QAAA,CAAAa,CAAA;YAAA;cAAA,OAAAb,QAAA,CAAAG,CAAA;UAAA;QAAA,GAAAR,OAAA;MAAA;IAEA;IAEA;IACAY,aAAA,WAAAA,cAAA;MACA,WAAAtI,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA,KAAA2I,SAAA,CAAAC,WAAA;UACA5I,MAAA,KAAA6I,KAAA;UACA;QACA;QAEAF,SAAA,CAAAC,WAAA,CAAAE,kBAAA,CACA,UAAAC,QAAA;UACAhJ,OAAA;YACA5C,QAAA,EAAA4L,QAAA,CAAAC,MAAA,CAAA7L,QAAA;YACAC,SAAA,EAAA2L,QAAA,CAAAC,MAAA,CAAA5L;UACA;QACA,GACA,UAAAyC,KAAA;UACA,IAAAoJ,OAAA;UACA,QAAApJ,KAAA,CAAAqJ,IAAA;YACA,KAAArJ,KAAA,CAAAsJ,iBAAA;cACAF,OAAA;cACA;YACA,KAAApJ,KAAA,CAAAuJ,oBAAA;cACAH,OAAA;cACA;YACA,KAAApJ,KAAA,CAAAwJ,OAAA;cACAJ,OAAA;cACA;UACA;UACAjJ,MAAA,KAAA6I,KAAA,CAAAI,OAAA;QACA,GACA;UACAK,kBAAA;UACAC,OAAA;UACAC,UAAA;QACA,CACA;MACA;IACA;IAEA;IACAlB,cAAA,WAAAA,eAAA;MACA,WAAAxI,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA0E,GAAA,CAAAiC,WAAA;UACAC,IAAA;UAAA;UACAC,OAAA,WAAAA,QAAAC,GAAA;YACA/G,OAAA;cACA5C,QAAA,EAAA2J,GAAA,CAAA3J,QAAA;cACAC,SAAA,EAAA0J,GAAA,CAAA1J;YACA;UACA;UACA2J,IAAA,WAAAA,KAAAlH,KAAA;YACA,IAAAoJ,OAAA;YACA,IAAApJ,KAAA,CAAA4J,MAAA;cACA,IAAA5J,KAAA,CAAA4J,MAAA,CAAAC,QAAA;gBACAT,OAAA;cACA,WAAApJ,KAAA,CAAA4J,MAAA,CAAAC,QAAA;gBACAT,OAAA;cACA,WAAApJ,KAAA,CAAA4J,MAAA,CAAAC,QAAA;gBACAT,OAAA;cACA;YACA;YACAjJ,MAAA,KAAA6I,KAAA,CAAAI,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAV,oBAAA,WAAAA,qBAAApL,QAAA,EAAAC,SAAA;MAAA,IAAAuM,OAAA;MAAA,OAAAtC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAqC,SAAA;QAAA,IAAAC,aAAA,EAAA3E,KAAA;QAAA,OAAAoC,YAAA,GAAAM,CAAA,WAAAkC,SAAA;UAAA,kBAAAA,SAAA,CAAA/B,CAAA;YAAA;cACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEA;gBACA8B,aAAA,IACA,mBACA,oBACA,mBACA,kBACA,EAEA;gBACA3E,KAAA,GAAArB,IAAA,CAAAkG,KAAA,EAAA5M,QAAA,GAAAC,SAAA,WAAAyM,aAAA,CAAAG,MAAA;gBACAL,OAAA,CAAA9M,cAAA,GAAAgN,aAAA,CAAA3E,KAAA;gBAEAnG,OAAA,CAAAC,GAAA,WAAA2K,OAAA,CAAA9M,cAAA;cACA,SAAAgD,KAAA;gBACAd,OAAA,CAAAc,KAAA,YAAAA,KAAA;gBACA;gBACA8J,OAAA,CAAA9M,cAAA,gCAAA4F,MAAA,CAAAtF,QAAA,CAAA8M,OAAA,WAAAxH,MAAA,CAAArF,SAAA,CAAA6M,OAAA;cACA;YAAA;cAAA,OAAAH,SAAA,CAAA9B,CAAA;UAAA;QAAA,GAAA4B,QAAA;MAAA;IACA;IAEA;IACApB,mBAAA,WAAAA,oBAAA3I,KAAA;MACA,IAAAoJ,OAAA;MAEA,IAAApJ,KAAA,CAAAoJ,OAAA;QACAA,OAAA,GAAApJ,KAAA,CAAAoJ,OAAA;MACA;MAEAvE,GAAA,CAAAC,SAAA;QACAC,KAAA,EAAAqE,OAAA;QACApG,IAAA;QACAqH,QAAA;MACA;MAEAnL,OAAA,CAAAc,KAAA,YAAAA,KAAA;IACA;IAEA;IACAmC,uBAAA,WAAAA,wBAAA;MACA,KAAAjF,kBAAA;IACA;IAEA;IACAoN,UAAA,WAAAA,WAAA;MACA;MACA,SAAAnN,eAAA;QACA;MACA;MACA;MACA,SAAAD,kBAAA;QACA,KAAAiF,uBAAA;MACA;IACA;IAEA;IACAoI,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACAtL,OAAA,CAAAC,GAAA;MAEA,SAAAxB,IAAA,SAAAC,KAAA;QACA;QACAsB,OAAA,CAAAC,GAAA;QACA,KAAAiD,qBAAA;QACAmC,UAAA;UACAiG,OAAA,CAAA5M,KAAA,CAAA4G,cAAA;UACAtF,OAAA,CAAAC,GAAA;QACA;MACA,gBAAAxB,IAAA,UAAAC,KAAA;QACA;QACAsB,OAAA,CAAAC,GAAA;QACA,KAAAI,SAAA;MACA;QACA;QACAL,OAAA,CAAAC,GAAA;QACA,KAAAzB,SAAA;QACA,KAAA4B,SAAA;UACAkL,OAAA,CAAA9M,SAAA;UACAwB,OAAA,CAAAC,GAAA;QACA;MACA;IACA;EACA;AACA,E;;;;;;;;;;;;;AClxCA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C", "file": "pages/nearby_technicians/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/nearby_technicians/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=d21ba1ea&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/nearby_technicians/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=d21ba1ea&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <scroll-view class=\"page-scroll\" scroll-y=\"true\" :style=\"{ height: '100vh' }\">\n    <view class=\"page flex-col\">\n    <view class=\"section_1 flex-col justify-between\">\n      <view class=\"box_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/nearby_technicians/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/nearby_technicians/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/nearby_technicians/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"box_2 flex-row justify-between\">\n        <text class=\"text_2\">预约到家</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/nearby_technicians/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n        />\n      </view>\n    </view>\n\n    <view class=\"section_2 flex-col\">\n      <view class=\"group_1 flex-col\">\n        \n        <view class=\"box_3 flex-row\">\n          <!-- 定位 地图/列表切换 -->\n          <image\n            class=\"thumbnail_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/nearby_technicians/FigmaDDSSlicePNG9054b3373ab26e98fe31e2bc2cf29a06.png\"\n            @click=\"handleLocationClick\"\n          />\n          <view class=\"image-text_1 flex-row justify-between\">\n            <text class=\"text-group_1\">{{ currentAddress }}</text>\n            <view class=\"box_4 flex-col\">\n              <image\n                class=\"vector-icon\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/images/vector.png\"\n              />\n            </view>\n          </view>\n          <!-- 定位 地图/列表切换 -->\n\n          <!--tabs切换-->\n          <view class=\"block_1 flex-row\" :class=\"{ 'tab-active': tabsIndex === 0 }\" @click=\"switchTab(0)\">\n            <view class=\"image-text_2 flex-row justify-between\">\n              <image\n                class=\"thumbnail_5\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/nearby_technicians/FigmaDDSSlicePNG615283e6802551177c13b8cbed080173.png\"\n              />\n              <text class=\"text-group_2\">地图</text>\n            </view>\n          </view>\n          <view class=\"block_2 flex-row\" :class=\"{ 'tab-active': tabsIndex === 1 }\" @click=\"switchTab(1)\">\n            <view class=\"image-text_3 flex-row justify-between\">\n              <image\n                class=\"thumbnail_6\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/nearby_technicians/FigmaDDSSlicePNGb180c0de46a11117e51b349c43520f5f.png\"\n              />\n              <text class=\"text-group_3\">列表</text>\n            </view>\n          </view>\n          <!--tabs切换-->\n\n        </view>\n        <view class=\"box_5 flex-row\">\n          <view class=\"image-text_4 flex-row justify-between\">\n            <text class=\"text-group_4\">默认城市</text>\n            <view class=\"group_2 flex-col\">\n              <image\n                class=\"vector-icon\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/images/vector.png\"\n              />\n            </view>\n          </view>\n          <text class=\"text_3\">请输入要查找的项目</text>\n          <image\n            class=\"label_1\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/nearby_technicians/FigmaDDSSlicePNG444a7b67873a94e8a3152bb485a1dba9.png\"\n          />\n        </view>\n\n        <!-- 服务项目水平滚动列表 - 开始 -->\n        <view class=\"box_6\">\n          <view class=\"scroll-content flex-row\">\n            <view\n              v-for=\"item in serviceItems\"\n              :key=\"item.id\"\n              class=\"service-item\"\n            >\n              <text class=\"service-text\">{{ item.text }}</text>\n            </view>\n          </view>\n        </view>\n        <!-- 服务项目水平滚动列表 - 结束 -->\n\n        <view class=\"box_7 flex-row\">\n          <text class=\"text_8\">服务类型</text>\n          <view class=\"group_3 flex-col\">\n            <image\n              class=\"vector-icon\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/images/vector.png\"\n            />\n          </view>\n          <text class=\"text_9\">技师性别</text>\n          <view class=\"group_4 flex-col\">\n            <image\n              class=\"vector-icon\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/images/vector.png\"\n            />\n          </view>\n          <text class=\"text_10\">技师年龄</text>\n          <view class=\"group_5 flex-col\">\n            <image\n              class=\"vector-icon\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/images/vector.png\"\n            />\n          </view>\n          <text class=\"text_11\">服务状态</text>\n          <view class=\"group_6 flex-col\">\n            <image\n              class=\"vector-icon\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/images/vector.png\"\n            />\n          </view>\n        </view>\n      </view>\n\n      <!--地图视图-->\n      <view\n        class=\"map-container\"\n        :class=\"{\n          'h5-fullscreen': isH5,\n          'map-animate': mapAnimating\n        }\"\n        v-show=\"tabsIndex === 0\"\n      >\n        <!-- H5环境下使用Leaflet地图 -->\n        <view class=\"h5-map-wrapper\" v-if=\"isH5\" @click=\"onMapClick\">\n          <view id=\"leafletMapContainer\" class=\"leaflet-map\"></view>\n        </view>\n\n        <!-- 非H5环境使用uni-app map组件 -->\n        <map\n          v-else\n          id=\"technicianMap\"\n          class=\"technician-map\"\n          :latitude=\"mapCenter.latitude\"\n          :longitude=\"mapCenter.longitude\"\n          :scale=\"mapScale\"\n          :markers=\"mapMarkers\"\n          :show-location=\"false\"\n          @markertap=\"onMarkerTap\"\n          @regionchange=\"onRegionChange\"\n          @tap=\"onMapClick\"\n        />\n\n        <!-- 地图加载状态提示 -->\n        <view class=\"map-loading\" v-if=\"!mapLoaded\">\n          <text class=\"loading-text\">地图加载中...</text>\n        </view>\n      </view>\n\n      <!--技师列表 栅格一行两列-->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"technician-grid\" v-if=\"tabsIndex === 1 && layoutMode === 0\" key=\"grid\">\n        <view class=\"technician-card\" v-for=\"technician in technicianList\" :key=\"technician.id\">\n          <!-- 技师头像背景区域 -->\n          <view class=\"card-avatar\">\n            <!-- 技师头像图片 -->\n            <image\n              class=\"technician-avatar\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"technician.avatar\"\n            />\n            <view class=\"time-badge\">\n              <view class=\"time-label-wrapper\">\n                <text class=\"time-label\">最早可约</text>\n              </view>\n              <text class=\"time-value\">{{ technician.earliestTime }}</text>\n            </view>\n          </view>\n\n          <!-- 技师信息卡片 -->\n          <view class=\"card-content\">\n            <!-- 技师姓名和状态 -->\n            <view class=\"technician-header\">\n              <text class=\"technician-name\">{{ technician.name }}</text>\n              <view class=\"status-badge\">\n                <text class=\"status-text\">{{ technician.status }}</text>\n              </view>\n            </view>\n\n            <!-- 评分和服务次数 -->\n            <view class=\"rating-section\">\n              <view class=\"rating-star\"></view>\n              <view class=\"service-info\">\n                <text class=\"rating-score\">{{ technician.rating }}</text>\n                <text class=\"service-count\">已服务{{ technician.serviceCount }}单</text>\n              </view>\n            </view>\n\n            <!-- 出行费用 -->\n            <view class=\"travel-fee\">\n              <image\n                class=\"fee-icon\"\n                referrerpolicy=\"no-referrer\"\n                :src=\"technician.freeIcon\"\n              />\n              <text class=\"fee-text\">{{ technician.freeTravel ? '免出行费用' : '需出行费用' }}</text>\n            </view>\n\n            <!-- 操作按钮 -->\n            <view class=\"action-buttons\">\n              <view class=\"btn-secondary\">\n                <text class=\"btn-text\">更多照片</text>\n              </view>\n              <view class=\"btn-primary\">\n                <text class=\"btn-text\">立即预约</text>\n              </view>\n            </view>\n\n            <!-- 底部图标信息 -->\n            <view class=\"bottom-info\">\n              <view class=\"info-item\">\n                <uni-icons type=\"chat\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.comments }}</text>\n              </view>\n              <view class=\"info-item\">\n                <uni-icons type=\"star\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.favorites }}</text>\n              </view>\n              <view class=\"info-item\">\n                <uni-icons type=\"shop\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.shopType }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!--技师列表 栅格一行两列-->\n\n      <!--技师列表 栅格一行一列-->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"technician-list-container flex-col\" v-if=\"tabsIndex === 1 && layoutMode === 1\" key=\"list\">\n        <view\n          class=\"technician-list-item flex-col\"\n          v-for=\"(item, index) in technicianList\"\n          :key=\"index\"\n        >\n          <view class=\"technician-info-top flex-row\">\n            <image\n              class=\"technician-avatar-img\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.avatar\"\n            />\n            <view class=\"single-row-image flex-col justify-between\">\n              <view class=\"technician-name-row flex-row justify-between\">\n                <text class=\"technician-name-text\" >{{ item.name }}</text>\n                <view class=\"technician-photos-btn flex-col\">\n                  <text class=\"technician-photos-text\" > 更多照片 </text>\n                </view>\n              </view>\n              <view class=\"technician-rating-row flex-row justify-between\">\n                <view class=\"technician-rating-area flex-row justify-between\">\n                  <view class=\"technician-star-icon flex-col\"></view>\n                  <text class=\"technician-rating-text\" >{{ item.rating }}</text>\n                </view>\n                <text class=\"technician-service-text\" >已服务{{item.serviceCount}}单</text>\n              </view>\n            </view>\n            <view class=\"single-row-time flex-col justify-between\">\n              <view class=\"technician-time-wrapper flex-col\">\n                <text class=\"technician-time-text\" >最早可约：{{item.earliestTime}}</text>\n              </view>\n              <view class=\"technician-distance-area flex-row justify-between\">\n                <view class=\"single-row-distance flex-col\">\n                  <uni-icons type=\"location\" size=\"16\" color=\"#0BCE94\"></uni-icons>\n                </view>\n                <text class=\"technician-distance-text\" >{{item.distance}}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"technician-info-bottom flex-row\">\n            <view\n              class=\"technician-status-badge flex-col\"\n              :style=\"{ background: item.lanhuBg13 }\"\n            >\n              <text class=\"technician-status-text\" >{{ item.status }}</text>\n            </view>\n            <view class=\"bottom-info\">\n              <!--评论-->\n              <view class=\"info-item\">\n                <uni-icons type=\"chat\" size=\"20\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ item.comments }}</text>\n              </view>\n\n              <!--收藏-->\n              <view class=\"info-item\">\n                <uni-icons type=\"star\" size=\"20\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ item.favorites }}</text>\n              </view>\n\n              <!--商家-->\n              <view class=\"info-item\">\n                <uni-icons type=\"shop\" size=\"20\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ item.shopType }}</text>\n              </view>\n            </view>\n            <view class=\"technician-book-btn flex-col\">\n              <text class=\"technician-book-text\" >立即预约</text>\n            </view>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!--技师列表 栅格一行一列-->\n\n      <!-- 地图选中技师卡片 -->\n      <transition name=\"slide-up\">\n        <view class=\"selected-technician-card\" v-if=\"selectedTechnician && tabsIndex === 0\">\n          <view class=\"technician-list-item flex-col\">\n            <view class=\"technician-info-top flex-row\">\n              <image\n                class=\"technician-avatar-img\"\n                referrerpolicy=\"no-referrer\"\n                :src=\"selectedTechnician.avatar\"\n              />\n              <view class=\"single-row-image flex-col justify-between\">\n                <view class=\"technician-name-row flex-row justify-between\">\n                  <text class=\"technician-name-text\">{{ selectedTechnician.name }}</text>\n                  <view class=\"technician-photos-btn flex-col\">\n                    <text class=\"technician-photos-text\">更多照片</text>\n                  </view>\n                </view>\n                <view class=\"technician-rating-row flex-row justify-between\">\n                  <view class=\"technician-rating-area flex-row justify-between\">\n                    <view class=\"technician-star-icon flex-col\"></view>\n                    <text class=\"technician-rating-text\">{{ selectedTechnician.rating }}</text>\n                  </view>\n                  <text class=\"technician-service-text\">已服务{{ selectedTechnician.serviceCount }}单</text>\n                </view>\n              </view>\n              <view class=\"single-row-time flex-col justify-between\">\n                <view class=\"technician-time-wrapper flex-col\">\n                  <text class=\"technician-time-text\">最早可约：{{ selectedTechnician.earliestTime }}</text>\n                </view>\n                <view class=\"technician-distance-area flex-row justify-between\">\n                  <view class=\"single-row-distance flex-col\">\n                    <uni-icons type=\"location\" size=\"16\" color=\"#0BCE94\"></uni-icons>\n                  </view>\n                  <text class=\"technician-distance-text\">{{ selectedTechnician.distance }}</text>\n                </view>\n              </view>\n            </view>\n            <view class=\"technician-info-bottom flex-row\">\n              <view\n                class=\"technician-status-badge flex-col\"\n                :style=\"{ background: selectedTechnician.lanhuBg13 }\"\n              >\n                <text class=\"technician-status-text\">{{ selectedTechnician.status }}</text>\n              </view>\n              <view class=\"bottom-info\">\n                <!--评论-->\n                <view class=\"info-item\">\n                  <uni-icons type=\"chat\" size=\"20\" color=\"#969696\"></uni-icons>\n                  <text class=\"info-text\">{{ selectedTechnician.comments }}</text>\n                </view>\n\n                <!--收藏-->\n                <view class=\"info-item\">\n                  <uni-icons type=\"star\" size=\"20\" color=\"#969696\"></uni-icons>\n                  <text class=\"info-text\">{{ selectedTechnician.favorites }}</text>\n                </view>\n\n                <!--商家-->\n                <view class=\"info-item\">\n                  <uni-icons type=\"shop\" size=\"20\" color=\"#969696\"></uni-icons>\n                  <text class=\"info-text\">{{ selectedTechnician.shopType }}</text>\n                </view>\n              </view>\n              <view class=\"technician-book-btn flex-col\">\n                <text class=\"technician-book-text\">立即预约</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </transition>\n      <!-- 地图选中技师卡片 -->\n\n      <view class=\"group_27 flex-row justify-around\">\n        <view class=\"image-text_15 flex-col justify-between\">\n          <image\n            class=\"label_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/nearby_technicians/FigmaDDSSlicePNG3a64844a13b7e0453d08677530393353.png\"\n          />\n          <text class=\"text-group_15\">首页</text>\n        </view>\n        <view class=\"image-text_16 flex-col justify-between\">\n          <view class=\"block_6 flex-col\">\n            <view class=\"group_28 flex-col\"></view>\n          </view>\n          <text class=\"text-group_16\">技师</text>\n        </view>\n        <view class=\"image-text_17 flex-col justify-between\">\n          <image\n            class=\"label_3\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/nearby_technicians/b1dc8ac20d2e485da0a997f619c96d5c_mergeImage.png\"\n          />\n          <text class=\"text-group_17\">订单</text>\n        </view>\n        <view class=\"image-text_18 flex-col justify-between\">\n          <image\n            class=\"label_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/nearby_technicians/3b4e0933d3db4adbb15d341775f3edc1_mergeImage.png\"\n          />\n          <text class=\"text-group_18\">我的</text>\n        </view>\n      </view>\n\n    </view>\n  </view>\n  </scroll-view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      constants: {},\n      tabsIndex: 0, // 默认显示地图视图\n      layoutMode: this.getLayoutModeFromCache(),\n      // 定位相关数据\n      currentAddress: '重庆市渝中区名族路188号...', // 当前地址\n      isLocating: false, // 定位状态\n      // 选中技师相关数据\n      selectedTechnician: null, // 当前选中的技师\n      isMarkerClicked: false, // 标记是否刚点击了技师标记\n      // 动画相关数据\n      mapAnimating: false, // 地图动画状态\n      // 地图相关数据\n      mapCenter: {\n        latitude: 29.5647, // 重庆市中心坐标\n        longitude: 106.5507\n      },\n      mapScale: 15, // 地图缩放级别\n      mapMarkers: [], // 地图标记点数据\n      mapLoaded: false, // 地图加载状态\n      isH5: false, // H5环境标识\n      h5Map: null, // H5地图实例\n      resizeTimer: null, // 窗口大小变化防抖定时器\n      serviceItems: [\n        { id: 1, text: '全部技师' },\n        { id: 2, text: '免费出行' },\n        { id: 3, text: '狐狸到家' },\n        { id: 4, text: '经典按摩' },\n        { id: 5, text: '深度按摩' },\n        { id: 6, text: '足疗服务' },\n        { id: 7, text: '推拿理疗' },\n        { id: 8, text: '养生保健' }\n      ],\n      // 技师列表数据\n      technicianList: [\n        {\n          id: 1,\n          name: '林欣蕾',\n          status: '可预约',\n          availableTime: '11:00',\n          earliestTime: '11:00',\n          rating: 5,\n          serviceCount: 489,\n          freeTravel: true,\n          comments: 0,\n          favorites: 0,\n          shopType: '商家',\n          distance: '2.8km',\n          lanhuBg13: 'rgba(11,206,148,1.000000)',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png',\n          // 地图坐标（重庆市渝中区附近）\n          latitude: 29.5647,\n          longitude: 106.5507\n        },\n        {\n          id: 2,\n          name: '林欣蕾',\n          status: '不可预约',\n          availableTime: '12:00',\n          earliestTime: '12:00',\n          rating: 4.8,\n          serviceCount: 356,\n          freeTravel: false,\n          comments: 5,\n          favorites: 12,\n          shopType: '商家',\n          distance: '2.8km',\n          lanhuBg13: 'rgba(153, 153, 153, 1)',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png',\n          // 地图坐标（重庆市江北区附近）\n          latitude: 29.5847,\n          longitude: 106.5307\n        },\n        {\n          id: 3,\n          name: '林欣蕾',\n          status: '可预约',\n          availableTime: '10:30',\n          earliestTime: '10:30',\n          rating: 4.9,\n          serviceCount: 267,\n          freeTravel: true,\n          comments: 8,\n          favorites: 15,\n          shopType: '商家',\n          distance: '2.8km',\n          lanhuBg13: 'rgba(11,206,148,1.000000)',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png',\n          // 地图坐标（重庆市南岸区附近）\n          latitude: 29.5447,\n          longitude: 106.5707\n        },\n        {\n          id: 4,\n          name: '林欣蕾',\n          status: '可预约',\n          availableTime: '14:00',\n          earliestTime: '14:00',\n          rating: 5.0,\n          serviceCount: 523,\n          freeTravel: true,\n          comments: 12,\n          favorites: 28,\n          shopType: '商家',\n          distance: '2.8km',\n          lanhuBg13: 'rgba(11,206,148,1.000000)',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png',\n          // 地图坐标（重庆市沙坪坝区附近）\n          latitude: 29.5747,\n          longitude: 106.4707\n        }\n      ]\n    };\n  },\n  mounted() {\n    // 页面加载时确保布局模式正确初始化\n    console.log('页面加载，当前布局模式:', this.layoutMode === 0 ? '一行两列' : '一行一列');\n\n    // 检测运行环境\n    this.checkEnvironment();\n\n    // 初始化地图标记点\n    this.initMapMarkers();\n\n    // 如果是H5环境，初始化腾讯地图\n    if (this.isH5) {\n      // 添加窗口大小变化监听器\n      // #ifdef H5\n      window.addEventListener('resize', this.handleWindowResize);\n      window.addEventListener('orientationchange', this.handleWindowResize);\n      // #endif\n\n      this.$nextTick(() => {\n        this.initH5Map();\n      });\n    }\n  },\n\n  beforeDestroy() {\n    // 清理事件监听器\n    // #ifdef H5\n    if (this.isH5) {\n      window.removeEventListener('resize', this.handleWindowResize);\n      window.removeEventListener('orientationchange', this.handleWindowResize);\n    }\n    // #endif\n  },\n\n  methods: {\n    // 检测运行环境\n    checkEnvironment() {\n      // #ifdef H5\n      this.isH5 = true;\n      console.log('当前运行在H5环境');\n      // #endif\n\n      // #ifndef H5\n      this.isH5 = false;\n      console.log('当前运行在非H5环境');\n      // #endif\n    },\n\n    // 初始化H5地图\n    initH5Map() {\n      if (!this.isH5) return;\n\n      console.log('开始初始化H5 Leaflet地图...');\n\n      // 动态加载Leaflet库\n      this.loadLeafletLibrary().then(() => {\n        console.log('Leaflet库加载成功，开始创建地图');\n        this.createLeafletMap();\n      }).catch(err => {\n        console.error('加载Leaflet库失败:', err);\n        this.mapLoaded = true;\n      });\n    },\n\n    // 动态加载Leaflet库\n    loadLeafletLibrary() {\n      return new Promise((resolve, reject) => {\n        // 检查是否已经加载\n        if (window.L) {\n          console.log('Leaflet库已存在');\n          resolve();\n          return;\n        }\n\n        console.log('开始加载Leaflet CSS和JS...');\n\n        // 加载CSS\n        const cssLink = document.createElement('link');\n        cssLink.rel = 'stylesheet';\n        cssLink.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';\n        document.head.appendChild(cssLink);\n\n        // 加载JS\n        const script = document.createElement('script');\n        script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';\n        script.onload = () => {\n          console.log('Leaflet库加载完成');\n          resolve();\n        };\n        script.onerror = (error) => {\n          console.error('Leaflet库加载失败:', error);\n          reject(error);\n        };\n        document.head.appendChild(script);\n      });\n    },\n\n    // 创建Leaflet地图\n    createLeafletMap() {\n      try {\n        console.log('开始创建Leaflet地图实例...');\n        const mapContainer = document.getElementById('leafletMapContainer');\n        if (!mapContainer) {\n          console.error('地图容器未找到');\n          this.mapLoaded = true;\n          return;\n        }\n\n        console.log('地图容器找到，创建Leaflet地图实例');\n\n        // 创建地图实例\n        this.h5Map = L.map('leafletMapContainer', {\n          attributionControl: false // 禁用版权信息控件\n        }).setView(\n          [this.mapCenter.latitude, this.mapCenter.longitude],\n          this.mapScale\n        );\n\n        // 添加地图瓦片层（免费，无需API密钥）\n        // 优先使用OpenStreetMap，如果加载失败则使用备用服务\n        const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\n          attribution: '', // 移除版权信息\n          maxZoom: 19,\n          errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='\n        });\n\n        // 添加瓦片层到地图\n        tileLayer.addTo(this.h5Map);\n\n        // 监听瓦片加载错误，如果主服务不可用则切换到备用服务\n        tileLayer.on('tileerror', () => {\n          console.log('主地图服务加载失败，尝试备用服务...');\n          // 移除当前瓦片层\n          this.h5Map.removeLayer(tileLayer);\n          // 添加备用瓦片层\n          L.tileLayer('https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png', {\n            attribution: '', // 移除版权信息\n            maxZoom: 19,\n            subdomains: 'abcd'\n          }).addTo(this.h5Map);\n        });\n\n        console.log('地图实例创建成功');\n\n        // 监听地图加载完成事件\n        this.h5Map.whenReady(() => {\n          console.log('地图瓦片加载完成，开始添加技师标记');\n          // 添加技师标记\n          this.addLeafletMarkers();\n          this.mapLoaded = true;\n          console.log('H5 Leaflet地图完全初始化完成');\n        });\n\n        // 监听地图视图变化\n        this.h5Map.on('moveend', () => {\n          console.log('地图移动完成，当前中心点:', this.h5Map.getCenter());\n        });\n\n        this.h5Map.on('zoomend', () => {\n          console.log('地图缩放完成，当前缩放级别:', this.h5Map.getZoom());\n        });\n\n        // 监听地图点击事件（用于关闭技师卡片）\n        this.h5Map.on('click', () => {\n          // 如果刚点击了标记，则不关闭卡片\n          if (this.isMarkerClicked) {\n            return;\n          }\n          // 点击地图空白区域时关闭技师卡片\n          if (this.selectedTechnician) {\n            this.closeSelectedTechnician();\n          }\n        });\n\n        // 设置地图容器高度以铺满剩余空间\n        this.setMapContainerHeight();\n      } catch (error) {\n        console.error('创建Leaflet地图失败:', error);\n        this.mapLoaded = true;\n      }\n    },\n\n    // 添加Leaflet地图标记\n    addLeafletMarkers() {\n      if (!this.h5Map) {\n        console.log('地图实例不存在，无法添加标记');\n        return;\n      }\n\n      console.log('开始添加技师标记...');\n\n      this.technicianList.forEach((technician) => {\n        if (technician.latitude && technician.longitude) {\n          // 创建自定义图标\n          const customIcon = L.divIcon({\n            className: 'custom-marker',\n            html: `\n              <div class=\"marker-container\" style=\"display: flex; align-items: center; background: rgba(255, 255, 255, 0.95); border-radius: 6px; padding: 2px 4px; box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15); cursor: pointer; max-width: 100px;\">\n                <img src=\"${technician.avatar}\" class=\"marker-avatar\" style=\"width: 24px !important; height: 24px !important; border-radius: 50% !important; margin-right: 4px; object-fit: cover !important; border: 1px solid #fff !important;\" />\n                <div class=\"marker-info\" style=\"display: flex; flex-direction: column;\">\n                  <div class=\"marker-name\" style=\"font-size: 11px; font-weight: 500; color: #333; line-height: 1.2; margin-bottom: 1px;\">${technician.name}</div>\n                  <div class=\"marker-distance\" style=\"font-size: 9px; color: #0BCE94; font-weight: 600; line-height: 1;\">${technician.distance}</div>\n                </div>\n              </div>\n            `,\n            iconSize: [100, 40], // 从[120, 60]调小到[100, 40]\n            iconAnchor: [50, 20] // 相应调整锚点位置\n          });\n\n          // 添加标记到地图\n          const marker = L.marker([technician.latitude, technician.longitude], {\n            icon: customIcon\n          }).addTo(this.h5Map);\n\n          // 添加点击事件\n          marker.on('click', () => {\n            this.onTechnicianClick(technician);\n          });\n\n          console.log(`添加技师标记: ${technician.name}`);\n        }\n      });\n    },\n\n    // 设置地图容器高度以铺满剩余空间\n    setMapContainerHeight() {\n      if (!this.isH5) return;\n\n      this.$nextTick(() => {\n        try {\n          // 获取视口高度\n          const viewportHeight = window.innerHeight;\n\n          // 获取页面头部区域的高度\n          const headerElements = document.querySelectorAll('.section_1, .section_2 .group_1');\n          let headerHeight = 0;\n          headerElements.forEach(el => {\n            headerHeight += el.offsetHeight;\n          });\n\n          // 计算地图应该占用的高度（视口高度 - 头部高度 - 底部导航栏高度 - 边距）\n          const bottomNavHeight = 60; // 底部导航栏高度\n          const margins = 40; // 上下边距\n          const availableHeight = viewportHeight - headerHeight - bottomNavHeight - margins;\n\n          // 设置最小和最大高度限制\n          const minHeight = 300;\n          const maxHeight = viewportHeight * 0.7;\n          const finalHeight = Math.max(minHeight, Math.min(availableHeight, maxHeight));\n\n          console.log('计算地图高度:', {\n            viewportHeight,\n            headerHeight,\n            availableHeight,\n            finalHeight\n          });\n\n          // 应用高度到地图容器\n          const mapContainer = document.querySelector('.map-container') || document.querySelector('.map-container.h5-fullscreen');\n          const leafletContainer = document.getElementById('leafletMapContainer');\n\n          console.log('设置地图容器高度:', {\n            mapContainer: mapContainer ? '找到' : '未找到',\n            leafletContainer: leafletContainer ? '找到' : '未找到',\n            finalHeight: finalHeight + 'px'\n          });\n\n          if (mapContainer) {\n            mapContainer.style.height = finalHeight + 'px';\n            mapContainer.style.minHeight = finalHeight + 'px';\n            console.log('地图容器高度已设置为:', finalHeight + 'px');\n          }\n\n          if (leafletContainer) {\n            leafletContainer.style.height = finalHeight + 'px';\n            leafletContainer.style.minHeight = finalHeight + 'px';\n            console.log('Leaflet容器高度已设置为:', finalHeight + 'px');\n          }\n\n          // 触发地图重新计算大小\n          if (this.h5Map) {\n            setTimeout(() => {\n              this.h5Map.invalidateSize();\n              console.log('地图尺寸已重新计算');\n            }, 100);\n          }\n        } catch (error) {\n          console.error('设置地图高度失败:', error);\n        }\n      });\n    },\n\n    // 处理窗口大小变化\n    handleWindowResize() {\n      if (!this.isH5) return;\n\n      // 防抖处理，避免频繁调用\n      clearTimeout(this.resizeTimer);\n      this.resizeTimer = setTimeout(() => {\n        console.log('窗口大小变化，重新计算地图高度');\n        this.setMapContainerHeight();\n      }, 300);\n    },\n\n    // 技师点击事件（H5地图标记点击）\n    onTechnicianClick(technician) {\n      console.log('点击技师:', technician.name);\n      this.selectedTechnician = technician;\n      // 设置标记，防止地图点击事件立即关闭卡片\n      this.isMarkerClicked = true;\n      setTimeout(() => {\n        this.isMarkerClicked = false;\n      }, 100);\n      uni.showToast({\n        title: `选择了${technician.name}`,\n        icon: 'none'\n      });\n    },\n\n    // 从缓存中读取布局模式\n    getLayoutModeFromCache() {\n      try {\n        const cachedLayoutMode = uni.getStorageSync('layoutMode');\n        if (cachedLayoutMode !== '') {\n          console.log('从缓存读取布局模式:', cachedLayoutMode === 0 ? '一行两列' : '一行一列');\n          return cachedLayoutMode;\n        }\n      } catch (e) {\n        console.log('读取缓存失败:', e);\n      }\n      // 默认返回一行一列布局\n      return 1;\n    },\n\n    // 保存布局模式到缓存\n    saveLayoutModeToCache(layoutMode) {\n      try {\n        uni.setStorageSync('layoutMode', layoutMode);\n        console.log('布局模式已保存到缓存:', layoutMode === 0 ? '一行两列' : '一行一列');\n      } catch (e) {\n        console.log('保存缓存失败:', e);\n      }\n    },\n\n    // 切换tabs\n    switchTab(index) {\n      if (index === 0) {\n        // 点击地图tab\n        this.tabsIndex = 0;\n        console.log('切换到tab: 地图');\n\n        // 切换到地图时，触发动画并刷新地图\n        this.mapAnimating = true;\n\n        this.$nextTick(() => {\n          // 等待DOM显示状态切换完成\n          setTimeout(() => {\n            console.log('切换到地图视图，刷新地图显示');\n\n            if (this.isH5 && this.h5Map) {\n              // H5环境：地图实例已存在，只需要重新计算尺寸\n              console.log('H5地图实例存在，重新计算尺寸');\n              this.h5Map.invalidateSize();\n            } else if (this.isH5 && !this.h5Map) {\n              // H5环境：地图实例不存在，需要初始化\n              console.log('H5地图实例不存在，初始化地图');\n              this.initH5Map();\n            } else {\n              // 小程序环境：触发地图组件刷新\n              console.log('小程序环境，触发地图刷新');\n              this.mapLoaded = false;\n              this.$nextTick(() => {\n                this.mapLoaded = true;\n              });\n            }\n\n            // 动画完成后重置状态\n            setTimeout(() => {\n              this.mapAnimating = false;\n            }, 500);\n          }, 100); // 给足够时间让v-show完成显示切换\n        });\n      } else if (index === 1) {\n        // 点击列表tab\n        if (this.tabsIndex === 1) {\n          // 如果当前已经是列表tab，则切换布局模式\n          this.layoutMode = this.layoutMode === 0 ? 1 : 0;\n          // 保存新的布局模式到缓存\n          this.saveLayoutModeToCache(this.layoutMode);\n          console.log('切换列表布局模式:', this.layoutMode === 0 ? '一行两列' : '一行一列');\n        } else {\n          // 如果当前不是列表tab，则切换到列表tab\n          this.tabsIndex = 1;\n          console.log('切换到tab: 列表');\n          // 切换到列表时，关闭选中的技师卡片\n          if (this.selectedTechnician) {\n            this.closeSelectedTechnician();\n          }\n        }\n      }\n    },\n\n    // 初始化地图标记点\n    initMapMarkers() {\n      console.log('开始初始化地图标记点...');\n\n      // 基于技师列表生成地图标记点\n      this.mapMarkers = this.technicianList.map((technician) => {\n        // 为每个技师分配不同的坐标，避免重叠\n        const offsetLat = (Math.random() - 0.5) * 0.01; // 随机偏移\n        const offsetLng = (Math.random() - 0.5) * 0.01;\n\n        return {\n          id: technician.id,\n          latitude: technician.latitude + offsetLat,\n          longitude: technician.longitude + offsetLng,\n          title: technician.name,\n          iconPath: '', // H5环境下使用默认标记\n          width: 30,\n          height: 30,\n          callout: {\n            content: `${technician.name} ${technician.distance}`,\n            color: '#ffffff',\n            fontSize: 14,\n            borderRadius: 8,\n            bgColor: '#0BCE94',\n            padding: 10,\n            display: 'ALWAYS'\n          }\n        };\n      });\n\n      console.log('地图标记点初始化完成:', this.mapMarkers);\n\n      // 延迟设置地图加载完成状态\n      setTimeout(() => {\n        this.mapLoaded = true;\n        console.log('地图加载完成');\n      }, 1000);\n    },\n\n    // 地图标记点点击事件（uni-app原生地图）\n    onMarkerTap(e) {\n      const markerId = e.detail.markerId;\n      const technician = this.technicianList.find(item => item.id === markerId);\n      if (technician) {\n        console.log('点击了技师标记:', technician.name);\n        this.selectedTechnician = technician;\n        // 设置标记，防止地图点击事件立即关闭卡片\n        this.isMarkerClicked = true;\n        setTimeout(() => {\n          this.isMarkerClicked = false;\n        }, 100);\n        uni.showToast({\n          title: `选择了技师: ${technician.name}`,\n          icon: 'none'\n        });\n      }\n    },\n\n    // 地图区域变化事件\n    onRegionChange(e) {\n      console.log('地图区域变化:', e.detail);\n    },\n\n    // 地图控制方法\n    centerToUserLocation() {\n      // 获取用户当前位置并居中显示\n      uni.getLocation({\n        type: 'gcj02',\n        success: (res) => {\n          this.mapCenter = {\n            latitude: res.latitude,\n            longitude: res.longitude\n          };\n          console.log('定位到用户位置:', this.mapCenter);\n        },\n        fail: (err) => {\n          console.error('获取位置失败:', err);\n          uni.showToast({\n            title: '定位失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n\n    zoomIn() {\n      if (this.mapScale < 18) {\n        this.mapScale += 1;\n        console.log('地图放大，当前缩放级别:', this.mapScale);\n      }\n    },\n\n    zoomOut() {\n      if (this.mapScale > 5) {\n        this.mapScale -= 1;\n        console.log('地图缩小，当前缩放级别:', this.mapScale);\n      }\n    },\n\n    // 获取自定义标记样式（预留，当前使用原生标记）\n    getMarkerStyle() {\n      return {\n        display: 'none' // 暂时隐藏自定义标记，使用原生标记\n      };\n    },\n\n    // 处理定位图片点击事件\n    async handleLocationClick() {\n      if (this.isLocating) {\n        return; // 防止重复点击\n      }\n\n      this.isLocating = true;\n\n      try {\n        // 显示加载提示\n        uni.showLoading({\n          title: '正在定位...',\n          mask: true\n        });\n\n        // 检测运行环境\n        const platform = uni.getSystemInfoSync().platform;\n        const isH5 = platform === 'h5' || typeof window !== 'undefined';\n\n        let location;\n\n        if (isH5) {\n          // H5环境使用浏览器定位API\n          location = await this.getH5Location();\n        } else {\n          // 小程序环境使用uni.getLocation\n          location = await this.getUniLocation();\n        }\n\n        if (location) {\n          // 更新地图中心点\n          this.mapCenter = {\n            latitude: location.latitude,\n            longitude: location.longitude\n          };\n\n          // 根据经纬度获取地址信息（这里使用模拟地址，实际项目中可以调用逆地理编码API）\n          await this.getAddressByLocation(location.latitude, location.longitude);\n\n          // 显示定位成功提示\n          uni.showToast({\n            title: '定位成功',\n            icon: 'success'\n          });\n\n          // 如果是地图视图，重新初始化标记点\n          if (this.tabsIndex === 0) {\n            this.initMapMarkers();\n            if (this.isH5 && this.h5Map) {\n              this.h5Map.setView([location.latitude, location.longitude], this.mapScale);\n            }\n          }\n        }\n\n      } catch (error) {\n        console.error('定位失败:', error);\n        this.handleLocationError(error);\n      } finally {\n        this.isLocating = false;\n        uni.hideLoading();\n      }\n    },\n\n    // H5环境定位\n    getH5Location() {\n      return new Promise((resolve, reject) => {\n        if (!navigator.geolocation) {\n          reject(new Error('浏览器不支持定位功能'));\n          return;\n        }\n\n        navigator.geolocation.getCurrentPosition(\n          (position) => {\n            resolve({\n              latitude: position.coords.latitude,\n              longitude: position.coords.longitude\n            });\n          },\n          (error) => {\n            let message = '定位失败';\n            switch (error.code) {\n              case error.PERMISSION_DENIED:\n                message = '用户拒绝了定位请求';\n                break;\n              case error.POSITION_UNAVAILABLE:\n                message = '位置信息不可用';\n                break;\n              case error.TIMEOUT:\n                message = '定位请求超时';\n                break;\n            }\n            reject(new Error(message));\n          },\n          {\n            enableHighAccuracy: true,\n            timeout: 10000,\n            maximumAge: 60000\n          }\n        );\n      });\n    },\n\n    // 小程序环境定位\n    getUniLocation() {\n      return new Promise((resolve, reject) => {\n        uni.getLocation({\n          type: 'gcj02', // 使用国测局坐标系\n          success: (res) => {\n            resolve({\n              latitude: res.latitude,\n              longitude: res.longitude\n            });\n          },\n          fail: (error) => {\n            let message = '定位失败';\n            if (error.errMsg) {\n              if (error.errMsg.includes('auth deny')) {\n                message = '用户拒绝了定位权限';\n              } else if (error.errMsg.includes('timeout')) {\n                message = '定位超时，请重试';\n              } else if (error.errMsg.includes('system deny')) {\n                message = '系统拒绝定位，请检查设置';\n              }\n            }\n            reject(new Error(message));\n          }\n        });\n      });\n    },\n\n    // 根据经纬度获取地址信息\n    async getAddressByLocation(latitude, longitude) {\n      try {\n        // 这里使用模拟地址，实际项目中可以调用腾讯地图或高德地图的逆地理编码API\n        // 示例API调用（需要配置API密钥）：\n        // const response = await uni.request({\n        //   url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=YOUR_API_KEY`,\n        //   method: 'GET'\n        // });\n\n        // 模拟根据坐标生成地址\n        const mockAddresses = [\n          '重庆市渝中区解放碑步行街88号',\n          '重庆市江北区观音桥步行街168号',\n          '重庆市南岸区南坪步行街288号',\n          '重庆市沙坪坝区三峡广场128号'\n        ];\n\n        // 根据坐标选择一个模拟地址\n        const index = Math.floor((latitude + longitude) * 100) % mockAddresses.length;\n        this.currentAddress = mockAddresses[index];\n\n        console.log('获取到地址:', this.currentAddress);\n      } catch (error) {\n        console.error('获取地址失败:', error);\n        // 如果获取地址失败，使用默认地址\n        this.currentAddress = `定位成功 (${latitude.toFixed(4)}, ${longitude.toFixed(4)})`;\n      }\n    },\n\n    // 处理定位错误\n    handleLocationError(error) {\n      let message = '定位失败';\n\n      if (error.message) {\n        message = error.message;\n      }\n\n      uni.showToast({\n        title: message,\n        icon: 'none',\n        duration: 3000\n      });\n\n      console.error('定位错误详情:', error);\n    },\n\n    // 关闭选中技师卡片\n    closeSelectedTechnician() {\n      this.selectedTechnician = null;\n    },\n\n    // 地图点击事件（用于关闭技师卡片）\n    onMapClick() {\n      // 如果刚点击了标记，则不关闭卡片\n      if (this.isMarkerClicked) {\n        return;\n      }\n      // 点击地图空白区域时关闭技师卡片\n      if (this.selectedTechnician) {\n        this.closeSelectedTechnician();\n      }\n    },\n\n    // 刷新地图显示（仅在特殊情况下使用，如窗口大小变化）\n    refreshMapDisplay() {\n      console.log('刷新地图显示...');\n\n      if (this.isH5 && this.h5Map) {\n        // H5环境：重新计算地图尺寸\n        console.log('重新计算H5地图尺寸');\n        this.setMapContainerHeight();\n        setTimeout(() => {\n          this.h5Map.invalidateSize();\n          console.log('H5地图尺寸已重新计算');\n        }, 100);\n      } else if (this.isH5 && !this.h5Map) {\n        // H5环境：地图实例不存在，重新初始化\n        console.log('H5地图实例不存在，重新初始化');\n        this.initH5Map();\n      } else {\n        // 小程序环境：触发地图组件重新渲染\n        console.log('刷新小程序地图');\n        this.mapLoaded = false;\n        this.$nextTick(() => {\n          this.mapLoaded = true;\n          console.log('小程序地图已刷新');\n        });\n      }\n    }\n  }\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}