@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(245, 245, 245);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .box_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 172rpx;
}
.page .box_1 .block_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .block_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .block_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .block_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .block_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .block_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .block_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .box_1 .block_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .box_1 .block_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .box_2 {
  width: 750rpx;
  height: 1454rpx;
  margin-bottom: 2rpx;
}
.page .box_2 .box_3 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 222rpx;
}
.page .box_2 .box_3 .box_4 {
  width: 132rpx;
  height: 150rpx;
  margin: 36rpx 0 0 60rpx;
}
.page .box_2 .box_3 .box_4 .image-wrapper_1 {
  background-image: -webkit-linear-gradient(left, rgb(253, 167, 20) 0, rgb(253, 139, 4) 99.407417%);
  background-image: linear-gradient(90deg, rgb(253, 167, 20) 0, rgb(253, 139, 4) 99.407417%);
  border-radius: 24px;
  height: 98rpx;
  width: 132rpx;
}
.page .box_2 .box_3 .box_4 .image-wrapper_1 .label_1 {
  width: 64rpx;
  height: 52rpx;
  margin: 24rpx 0 0 34rpx;
}
.page .box_2 .box_3 .box_4 .text_3 {
  width: 96rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 32rpx 0 0 18rpx;
}
.page .box_2 .box_3 .image-text_1 {
  width: 132rpx;
  height: 150rpx;
  margin: 36rpx 0 0 118rpx;
}
.page .box_2 .box_3 .image-text_1 .image-wrapper_2 {
  height: 98rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
  width: 132rpx;
}
.page .box_2 .box_3 .image-text_1 .image-wrapper_2 .label_2 {
  width: 54rpx;
  height: 48rpx;
  margin: 24rpx 0 0 38rpx;
}
.page .box_2 .box_3 .image-text_1 .text-group_1 {
  width: 96rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 32rpx 0 0 18rpx;
}
.page .box_2 .box_3 .box_5 {
  width: 132rpx;
  height: 152rpx;
  margin: 36rpx 58rpx 0 118rpx;
}
.page .box_2 .box_3 .box_5 .image-wrapper_3 {
  background-image: -webkit-linear-gradient(left, rgb(27, 209, 194) 0, rgb(0, 189, 173) 100%);
  background-image: linear-gradient(90deg, rgb(27, 209, 194) 0, rgb(0, 189, 173) 100%);
  border-radius: 24px;
  height: 98rpx;
  width: 132rpx;
}
.page .box_2 .box_3 .box_5 .image-wrapper_3 .label_3 {
  width: 62rpx;
  height: 52rpx;
  margin: 22rpx 0 0 34rpx;
}
.page .box_2 .box_3 .box_5 .text_4 {
  width: 120rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 34rpx 0 0 6rpx;
}
.page .box_2 .box_6 {
  width: 724rpx;
  height: 40rpx;
  margin: 40rpx 0 0 24rpx;
}
.page .box_2 .box_6 .text_5 {
  width: 128rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.page .box_2 .box_6 .text_6 {
  width: 52rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-left: 494rpx;
}
.page .box_2 .box_6 .box_7 {
  background-color: rgb(34, 34, 34);
  width: 22rpx;
  height: 18rpx;
  margin: 22rpx 0 0 28rpx;
}
.page .box_2 .image-wrapper_4 {
  width: 702rpx;
  height: 336rpx;
  margin: 28rpx 0 0 24rpx;
}
.page .box_2 .image-wrapper_4 .image_2 {
  width: 336rpx;
  height: 336rpx;
}
.page .box_2 .image-wrapper_4 .image_3 {
  width: 336rpx;
  height: 336rpx;
}
.page .box_2 .box_8 {
  width: 702rpx;
  height: 122rpx;
  margin: 0 0 666rpx 24rpx;
}
.page .box_2 .box_8 .group_1 {
  background-color: rgb(255, 255, 255);
  height: 122rpx;
  width: 336rpx;
}
.page .box_2 .box_8 .group_1 .text-wrapper_1 {
  width: 120rpx;
  height: 34rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_2 .box_8 .group_1 .text-wrapper_1 .text_7 {
  width: 120rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.page .box_2 .box_8 .group_1 .block_3 {
  width: 288rpx;
  height: 34rpx;
  margin: 12rpx 0 22rpx 24rpx;
}
.page .box_2 .box_8 .group_1 .block_3 .image-text_2 {
  width: 64rpx;
  height: 34rpx;
}
.page .box_2 .box_8 .group_1 .block_3 .image-text_2 .thumbnail_5 {
  width: 28rpx;
  height: 28rpx;
  margin-top: 4rpx;
}
.page .box_2 .box_8 .group_1 .block_3 .image-text_2 .text-group_2 {
  width: 24rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.page .box_2 .box_8 .group_1 .block_3 .image-text_3 {
  width: 100rpx;
  height: 28rpx;
  margin-top: 4rpx;
}
.page .box_2 .box_8 .group_1 .block_3 .image-text_3 .thumbnail_6 {
  width: 18rpx;
  height: 20rpx;
  margin-top: 4rpx;
}
.page .box_2 .box_8 .group_1 .block_3 .image-text_3 .text-group_3 {
  width: 78rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
}
.page .box_2 .box_8 .group_2 {
  background-color: rgb(255, 255, 255);
  height: 122rpx;
  width: 336rpx;
}
.page .box_2 .box_8 .group_2 .text-wrapper_2 {
  width: 120rpx;
  height: 34rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .box_2 .box_8 .group_2 .text-wrapper_2 .text_8 {
  width: 120rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.page .box_2 .box_8 .group_2 .section_1 {
  width: 288rpx;
  height: 34rpx;
  margin: 12rpx 0 22rpx 24rpx;
}
.page .box_2 .box_8 .group_2 .section_1 .image-text_4 {
  width: 64rpx;
  height: 34rpx;
}
.page .box_2 .box_8 .group_2 .section_1 .image-text_4 .thumbnail_7 {
  width: 28rpx;
  height: 28rpx;
  margin-top: 4rpx;
}
.page .box_2 .box_8 .group_2 .section_1 .image-text_4 .text-group_4 {
  width: 24rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.page .box_2 .box_8 .group_2 .section_1 .image-text_5 {
  width: 100rpx;
  height: 28rpx;
  margin-top: 4rpx;
}
.page .box_2 .box_8 .group_2 .section_1 .image-text_5 .thumbnail_8 {
  width: 18rpx;
  height: 20rpx;
  margin-top: 4rpx;
}
.page .box_2 .box_8 .group_2 .section_1 .image-text_5 .text-group_5 {
  width: 78rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
}
