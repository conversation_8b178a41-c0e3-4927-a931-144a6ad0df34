@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 1970rpx;
  overflow: hidden;
}
.page .block_1 {
  width: 750rpx;
  height: 826rpx;
}
.page .block_1 .box_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .block_1 .box_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .block_1 .box_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .block_1 .box_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .block_1 .box_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .block_1 .box_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .block_1 .box_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .block_1 .box_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .block_1 .box_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .block_1 .box_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 296rpx;
  width: 702rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .block_1 .box_3 .section_1 {
  width: 136rpx;
  height: 30rpx;
  margin: 36rpx 0 0 20rpx;
}
.page .block_1 .box_3 .section_1 .text-wrapper_1 {
  width: 136rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .box_3 .section_1 .text-wrapper_1 .text_3 {
  width: 136rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(220, 78, 76);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .box_3 .section_1 .text-wrapper_1 .text_4 {
  width: 136rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .box_3 .section_2 {
  width: 664rpx;
  height: 66rpx;
  margin: 36rpx 0 0 20rpx;
}
.page .block_1 .box_3 .section_2 .text-wrapper_2 {
  background-color: rgb(246, 246, 246);
  border-radius: 100px;
  height: 66rpx;
  width: 208rpx;
}
.page .block_1 .box_3 .section_2 .text-wrapper_2 .text_5 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 0 0 44rpx;
}
.page .block_1 .box_3 .section_2 .text-wrapper_3 {
  background-color: rgb(246, 246, 246);
  border-radius: 100px;
  height: 66rpx;
  margin-left: 20rpx;
  width: 208rpx;
}
.page .block_1 .box_3 .section_2 .text-wrapper_3 .text_6 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 0 0 44rpx;
}
.page .block_1 .box_3 .section_2 .text-wrapper_4 {
  background-color: rgb(246, 246, 246);
  border-radius: 100px;
  height: 66rpx;
  margin-left: 20rpx;
  width: 208rpx;
}
.page .block_1 .box_3 .section_2 .text-wrapper_4 .text_7 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 0 0 44rpx;
}
.page .block_1 .box_3 .section_3 {
  width: 664rpx;
  height: 66rpx;
  margin: 18rpx 0 44rpx 20rpx;
}
.page .block_1 .box_3 .section_3 .text-wrapper_5 {
  background-color: rgb(246, 246, 246);
  border-radius: 100px;
  height: 66rpx;
  width: 208rpx;
}
.page .block_1 .box_3 .section_3 .text-wrapper_5 .text_8 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 0 0 44rpx;
}
.page .block_1 .box_3 .section_3 .text-wrapper_6 {
  background-color: rgb(246, 246, 246);
  border-radius: 100px;
  height: 66rpx;
  margin-left: 20rpx;
  width: 208rpx;
}
.page .block_1 .box_3 .section_3 .text-wrapper_6 .text_9 {
  width: 124rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 0 0 42rpx;
}
.page .block_1 .box_3 .section_3 .text-wrapper_7 {
  background-color: rgb(246, 246, 246);
  border-radius: 100px;
  height: 66rpx;
  margin-left: 20rpx;
  width: 208rpx;
}
.page .block_1 .box_3 .section_3 .text-wrapper_7 .text_10 {
  width: 60rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 0 0 74rpx;
}
.page .block_1 .box_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 702rpx;
  height: 296rpx;
  margin: 20rpx 0 22rpx 24rpx;
}
.page .block_1 .box_4 .text-wrapper_8 {
  width: 136rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 36rpx 0 0 22rpx;
}
.page .block_1 .box_4 .text-wrapper_8 .text_11 {
  width: 136rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(220, 78, 76);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .box_4 .text-wrapper_8 .text_12 {
  width: 136rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .box_4 .text_13 {
  width: 600rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 22rpx;
}
.page .block_1 .box_4 .text-wrapper_9 {
  background-color: rgb(246, 246, 246);
  border-radius: 10px;
  height: 110rpx;
  width: 656rpx;
  margin: 34rpx 0 32rpx 22rpx;
}
.page .block_1 .box_4 .text-wrapper_9 .text_14 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 40rpx 0 0 44rpx;
}
.page .block_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 702rpx;
  height: 916rpx;
  margin: -2rpx 0 0 24rpx;
}
.page .block_2 .text-wrapper_10 {
  width: 136rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 36rpx 0 0 22rpx;
}
.page .block_2 .text-wrapper_10 .text_15 {
  width: 136rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(220, 78, 76);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_2 .text-wrapper_10 .text_16 {
  width: 136rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_2 .text-wrapper_11 {
  background-color: rgb(246, 246, 246);
  border-radius: 10px;
  width: 656rpx;
  height: 264rpx;
  margin: 28rpx 0 0 22rpx;
}
.page .block_2 .text-wrapper_11 .paragraph_1 {
  width: 570rpx;
  height: 80rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 292rpx;
  margin: 26rpx 0 0 28rpx;
}
.page .block_2 .text-wrapper_11 .text_17 {
  width: 90rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 292rpx;
  margin: 98rpx 0 20rpx 530rpx;
}
.page .block_2 .group_1 {
  background-color: rgb(246, 246, 246);
  border-radius: 10px;
  width: 216rpx;
  height: 216rpx;
  margin: 30rpx 0 0 22rpx;
}
.page .block_2 .group_1 .image-text_1 {
  width: 104rpx;
  height: 126rpx;
  margin: 56rpx 0 0 56rpx;
}
.page .block_2 .group_1 .image-text_1 .label_1 {
  width: 50rpx;
  height: 42rpx;
  margin-left: 28rpx;
}
.page .block_2 .group_1 .image-text_1 .text-group_1 {
  width: 104rpx;
  height: 58rpx;
  margin-top: 26rpx;
}
.page .block_2 .group_1 .image-text_1 .text-group_1 .text_18 {
  width: 104rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 48rpx;
}
.page .block_2 .group_1 .image-text_1 .text-group_1 .text_19 {
  width: 40rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 48rpx;
  margin: 18rpx 0 0 32rpx;
}
.page .block_2 .group_2 {
  background-color: rgb(246, 246, 246);
  border-radius: 10px;
  width: 216rpx;
  height: 216rpx;
  margin: 26rpx 0 0 22rpx;
}
.page .block_2 .group_2 .image-text_2 {
  width: 104rpx;
  height: 126rpx;
  margin: 56rpx 0 0 56rpx;
}
.page .block_2 .group_2 .image-text_2 .label_2 {
  width: 50rpx;
  height: 42rpx;
  margin-left: 28rpx;
}
.page .block_2 .group_2 .image-text_2 .text-group_2 {
  width: 104rpx;
  height: 58rpx;
  margin-top: 26rpx;
}
.page .block_2 .group_2 .image-text_2 .text-group_2 .text_20 {
  width: 104rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 48rpx;
}
.page .block_2 .group_2 .image-text_2 .text-group_2 .text_21 {
  width: 40rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 48rpx;
  margin: 18rpx 0 0 32rpx;
}
.page .block_2 .text_22 {
  width: 592rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 48rpx;
  margin: 22rpx 0 30rpx 22rpx;
}
.page .text-wrapper_12 {
  width: 750rpx;
  height: 98rpx;
}
.page .text-wrapper_12 .text_23 {
  width: 528rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 48rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .block_3 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 134rpx;
  margin-top: -2rpx;
}
.page .block_3 .text-wrapper_13 {
  border-radius: 200px;
  height: 88rpx;
  border: 1px solid rgb(204, 204, 204);
  width: 330rpx;
  margin: 26rpx 0 0 24rpx;
}
.page .block_3 .text-wrapper_13 .text_24 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 106rpx;
}
.page .block_3 .text-wrapper_14 {
  background-color: rgb(11, 206, 148);
  border-radius: 200px;
  height: 88rpx;
  width: 330rpx;
  margin: 24rpx 24rpx 0 0;
}
.page .block_3 .text-wrapper_14 .text_25 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 106rpx;
}
