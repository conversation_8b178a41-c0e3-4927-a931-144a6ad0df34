@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 2052rpx;
  overflow: hidden;
}
.page .block_1 {
  width: 750rpx;
  height: 2052rpx;
}
.page .block_1 .group_1 {
  position: relative;
  width: 750rpx;
  height: 1928rpx;
}
.page .block_1 .group_1 .section_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .block_1 .group_1 .section_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .block_1 .group_1 .section_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .block_1 .group_1 .section_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .block_1 .group_1 .section_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .block_1 .group_1 .section_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .block_1 .group_1 .section_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .block_1 .group_1 .section_2 .text_2 {
  width: 64rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .block_1 .group_1 .section_2 .text_3 {
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 8rpx 0 0 262rpx;
}
.page .block_1 .group_1 .section_2 .box_1 {
  position: relative;
  width: 226rpx;
  height: 86rpx;
  margin: 0 12rpx 0 26rpx;
}
.page .block_1 .group_1 .section_2 .box_1 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 0 0 52rpx;
}
.page .block_1 .group_1 .section_2 .box_1 .text_4 {
  position: absolute;
  left: 0;
  top: 0;
  width: 84rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .section_3 {
  width: 750rpx;
  height: 116rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAXcAAAA6CAYAAABLcRn4AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADgSURBVHgB7dRBFQAQAEAxXNWXSyV6/LeF2Nz3vAFAyhoA5MgdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBH0pOQLgvJ6YsAAAAABJRU5ErkJggg==) 100% no-repeat;
  background-size: 100% 100%;
  margin-top: 2rpx;
}
.page .block_1 .group_1 .section_3 .image-text_1 {
  width: 172rpx;
  height: 52rpx;
  margin: 32rpx 0 0 290rpx;
}
.page .block_1 .group_1 .section_3 .image-text_1 .label_1 {
  width: 48rpx;
  height: 48rpx;
  margin-top: 2rpx;
}
.page .block_1 .group_1 .section_3 .image-text_1 .text-group_1 {
  width: 108rpx;
  height: 52rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 52rpx;
}
.page .block_1 .group_1 .section_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 308rpx;
  width: 702rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .block_1 .group_1 .section_4 .text-wrapper_1 {
  width: 656rpx;
  height: 26rpx;
  margin: 36rpx 0 0 24rpx;
}
.page .block_1 .group_1 .section_4 .text-wrapper_1 .text_5 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .section_4 .text-wrapper_1 .text_6 {
  width: 72rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .block_1 .group_1 .section_4 .block_2 {
  width: 654rpx;
  height: 128rpx;
  margin: 72rpx 0 46rpx 22rpx;
}
.page .block_1 .group_1 .section_4 .block_2 .image-text_2 {
  width: 436rpx;
  height: 128rpx;
}
.page .block_1 .group_1 .section_4 .block_2 .image-text_2 .image_2 {
  width: 126rpx;
  height: 126rpx;
}
.page .block_1 .group_1 .section_4 .block_2 .image-text_2 .text-group_2 {
  width: 298rpx;
  height: 128rpx;
}
.page .block_1 .group_1 .section_4 .block_2 .image-text_2 .text-group_2 .text_7 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 34rpx;
}
.page .block_1 .group_1 .section_4 .block_2 .image-text_2 .text-group_2 .text_8 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: -28rpx 0 0 2rpx;
}
.page .block_1 .group_1 .section_4 .block_2 .image-text_2 .text-group_2 .text_9 {
  width: 296rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 2rpx;
}
.page .block_1 .group_1 .section_4 .block_2 .image-text_2 .text-group_2 .text_10 {
  width: 120rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 22rpx;
}
.page .block_1 .group_1 .section_4 .block_2 .text_11 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .block_1 .group_1 .section_5 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  width: 702rpx;
  height: 88rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .block_1 .group_1 .section_5 .image-text_3 {
  width: 130rpx;
  height: 40rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .block_1 .group_1 .section_5 .image-text_3 .thumbnail_5 {
  width: 40rpx;
  height: 40rpx;
}
.page .block_1 .group_1 .section_5 .image-text_3 .text-group_3 {
  width: 78rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 8rpx;
}
.page .block_1 .group_1 .section_5 .label_2 {
  width: 44rpx;
  height: 44rpx;
  margin: 22rpx 28rpx 0 476rpx;
}
.page .block_1 .group_1 .section_6 {
  background-color: rgb(255, 255, 255);
  height: 60rpx;
  margin-left: 24rpx;
  width: 702rpx;
}
.page .block_1 .group_1 .section_6 .text-wrapper_2 {
  width: 668rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 0 0 24rpx;
}
.page .block_1 .group_1 .section_6 .text-wrapper_2 .text_12 {
  width: 668rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .section_6 .text-wrapper_2 .text_13 {
  width: 668rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .section_6 .text-wrapper_2 .text_14 {
  width: 668rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .section_6 .text-wrapper_2 .text_15 {
  width: 668rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .section_6 .text-wrapper_2 .text_16 {
  width: 668rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .section_7 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 60rpx;
  margin-left: 24rpx;
}
.page .block_1 .group_1 .section_7 .image-text_4 {
  width: 654rpx;
  height: 44rpx;
  margin: 8rpx 0 0 24rpx;
}
.page .block_1 .group_1 .section_7 .image-text_4 .text-group_4 {
  width: 582rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 10rpx;
}
.page .block_1 .group_1 .section_7 .image-text_4 .text-group_4 .text_17 {
  width: 582rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .section_7 .image-text_4 .text-group_4 .text_18 {
  width: 582rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_1 .section_7 .image-text_4 .label_3 {
  width: 44rpx;
  height: 44rpx;
}
.page .block_1 .group_1 .text-wrapper_3 {
  background-color: rgb(255, 255, 255);
  height: 60rpx;
  margin-left: 24rpx;
  width: 702rpx;
}
.page .block_1 .group_1 .text-wrapper_3 .text_19 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 0 0 24rpx;
}
.page .block_1 .group_1 .section_8 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 0px 10px 10px;
  width: 702rpx;
  height: 104rpx;
  margin-left: 24rpx;
}
.page .block_1 .group_1 .section_8 .image-text_5 {
  width: 654rpx;
  height: 76rpx;
  margin: 4rpx 0 0 24rpx;
}
.page .block_1 .group_1 .section_8 .image-text_5 .text-group_5 {
  width: 582rpx;
  height: 76rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 298rpx;
}
.page .block_1 .group_1 .section_8 .image-text_5 .text-wrapper_4 {
  border-radius: 2px;
  height: 32rpx;
  border: 0.6399999857px solid rgb(11, 206, 148);
  width: 62rpx;
  margin: 40rpx 0 0 -336rpx;
}
.page .block_1 .group_1 .section_8 .image-text_5 .text-wrapper_4 .text_20 {
  width: 32rpx;
  height: 16rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 14rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 8rpx 0 0 16rpx;
}
.page .block_1 .group_1 .section_8 .image-text_5 .label_4 {
  width: 44rpx;
  height: 44rpx;
  margin-left: 302rpx;
}
.page .block_1 .group_1 .text-wrapper_5 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 702rpx;
  height: 88rpx;
  margin: 20rpx 0 0 22rpx;
}
.page .block_1 .group_1 .text-wrapper_5 .text_21 {
  width: 56rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 26rpx;
}
.page .block_1 .group_1 .text-wrapper_5 .text_22 {
  width: 110rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 32rpx 0 478rpx;
}
.page .block_1 .group_1 .text-wrapper_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  height: 80rpx;
  width: 702rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .block_1 .group_1 .text-wrapper_6 .text_23 {
  width: 120rpx;
  height: 52rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 52rpx;
  margin: 14rpx 0 0 20rpx;
}
.page .block_1 .group_1 .section_9 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 80rpx;
  margin-left: 24rpx;
}
.page .block_1 .group_1 .section_9 .text_24 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 32rpx 0 0 24rpx;
}
.page .block_1 .group_1 .section_9 .text_25 {
  width: 260rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 28rpx 0 0 206rpx;
}
.page .block_1 .group_1 .section_9 .text-wrapper_7 {
  border-radius: 3px;
  height: 38rpx;
  border: 0.75px solid rgb(11, 206, 148);
  width: 72rpx;
  margin: 22rpx 22rpx 0 14rpx;
}
.page .block_1 .group_1 .section_9 .text-wrapper_7 .text_26 {
  width: 36rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 18rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 0 18rpx;
}
.page .block_1 .group_1 .text-wrapper_8 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 68rpx;
  margin-left: 24rpx;
}
.page .block_1 .group_1 .text-wrapper_8 .text_27 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .block_1 .group_1 .text-wrapper_8 .text_28 {
  width: 292rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 22rpx 0 260rpx;
}
.page .block_1 .group_1 .text-wrapper_9 {
  background-color: rgb(255, 255, 255);
  height: 68rpx;
  width: 702rpx;
  margin: 172rpx 0 0 24rpx;
}
.page .block_1 .group_1 .text-wrapper_9 .text_29 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .block_1 .group_1 .image-wrapper_1 {
  background-color: rgb(255, 255, 255);
  height: 260rpx;
  width: 702rpx;
  margin: 0 0 62rpx 24rpx;
}
.page .block_1 .group_1 .image-wrapper_1 .image_3 {
  width: 194rpx;
  height: 194rpx;
  margin: 10rpx 0 0 24rpx;
}
.page .block_1 .group_1 .text-wrapper_10 {
  background-color: rgb(255, 255, 255);
  position: absolute;
  left: 24rpx;
  top: 1364rpx;
  width: 702rpx;
  height: 174rpx;
}
.page .block_1 .group_1 .text-wrapper_10 .text_30 {
  width: 112rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .block_1 .group_1 .text-wrapper_10 .text_31 {
  width: 646rpx;
  height: 82rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 276rpx;
  margin: 28rpx 0 16rpx 26rpx;
}
.page .block_1 .group_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 126rpx;
  margin: 1926rpx 0 0 -750rpx;
}
.page .block_1 .group_2 .text-wrapper_11 {
  border-radius: 100px;
  height: 88rpx;
  border: 1px solid rgb(153, 153, 153);
  width: 330rpx;
  margin: 20rpx 0 0 30rpx;
}
.page .block_1 .group_2 .text-wrapper_11 .text_32 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 106rpx;
}
.page .block_1 .group_2 .text-wrapper_12 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 88rpx;
  width: 330rpx;
  margin: 20rpx 30rpx 0 0;
}
.page .block_1 .group_2 .text-wrapper_12 .text_33 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 106rpx;
}
