@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 1682rpx;
  overflow: hidden;
}
.page .section_1 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 172rpx;
}
.page .section_1 .box_1 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 64rpx;
}
.page .section_1 .box_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .section_1 .box_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .section_1 .box_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .section_1 .box_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .section_1 .box_2 {
  background-color: rgb(11, 206, 148);
  width: 750rpx;
  height: 108rpx;
}
.page .section_1 .box_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .section_1 .box_2 .text_2 {
  width: 64rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .section_1 .box_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 424rpx;
}
.page .section_2 {
  width: 750rpx;
  height: 1370rpx;
}
.page .section_2 .group_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 260rpx;
}
.page .section_2 .group_1 .block_1 {
  width: 700rpx;
  height: 58rpx;
  margin: 34rpx 0 0 26rpx;
}
.page .section_2 .group_1 .block_1 .image-text_1 {
  width: 172rpx;
  height: 44rpx;
  margin-top: 6rpx;
}
.page .section_2 .group_1 .block_1 .image-text_1 .text-group_1 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .section_2 .group_1 .block_1 .image-text_1 .thumbnail_5 {
  width: 28rpx;
  height: 28rpx;
  margin-top: 8rpx;
}
.page .section_2 .group_1 .block_1 .text-wrapper_1 {
  background-color: rgb(11, 206, 148);
  border-radius: 5px;
  height: 58rpx;
  margin-left: 210rpx;
  width: 144rpx;
}
.page .section_2 .group_1 .block_1 .text-wrapper_1 .text_3 {
  width: 112rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 8rpx 0 0 16rpx;
}
.page .section_2 .group_1 .block_1 .text-wrapper_2 {
  background-color: rgb(222, 222, 222);
  border-radius: 5px;
  height: 58rpx;
  margin-left: 30rpx;
  width: 144rpx;
}
.page .section_2 .group_1 .block_1 .text-wrapper_2 .text_4 {
  width: 112rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 8rpx 0 0 16rpx;
}
.page .section_2 .group_1 .block_2 {
  width: 698rpx;
  height: 78rpx;
  margin: 58rpx 0 32rpx 24rpx;
}
.page .section_2 .group_1 .block_2 .section_3 {
  background-color: rgb(245, 246, 248);
  border-radius: 50px;
  width: 588rpx;
  height: 78rpx;
}
.page .section_2 .group_1 .block_2 .section_3 .image-text_2 {
  width: 334rpx;
  height: 40rpx;
  margin: 18rpx 0 0 24rpx;
}
.page .section_2 .group_1 .block_2 .section_3 .image-text_2 .thumbnail_6 {
  width: 40rpx;
  height: 40rpx;
}
.page .section_2 .group_1 .block_2 .section_3 .image-text_2 .text-group_2 {
  width: 280rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 8rpx;
}
.page .section_2 .group_1 .block_2 .image-text_3 {
  width: 80rpx;
  height: 26rpx;
  margin-top: 26rpx;
}
.page .section_2 .group_1 .block_2 .image-text_3 .text-group_3 {
  width: 52rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .group_1 .block_2 .image-text_3 .group_2 {
  background-color: rgb(43, 43, 43);
  width: 20rpx;
  height: 12rpx;
  margin-top: 6rpx;
}
.page .section_2 .list_1 {
  width: 702rpx;
  height: 964rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
  margin: 16rpx 0 130rpx 24rpx;
}
.page .section_2 .list_1 .list-items_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 226rpx;
  margin-bottom: 20rpx;
  width: 702rpx;
}
.page .section_2 .list_1 .list-items_1 .box_3 {
  width: 654rpx;
  height: 124rpx;
  margin: 32rpx 0 0 24rpx;
}
.page .section_2 .list_1 .list-items_1 .box_3 .block_3 {
  border-radius: 50%;
  width: 34rpx;
  height: 34rpx;
  border: 1px solid rgb(199, 199, 199);
  margin-top: 48rpx;
}
.page .section_2 .list_1 .list-items_1 .box_3 .image_2 {
  width: 124rpx;
  height: 124rpx;
  margin-left: 26rpx;
}
.page .section_2 .list_1 .list-items_1 .box_3 .block_4 {
  width: 446rpx;
  height: 108rpx;
  margin-left: 24rpx;
}
.page .section_2 .list_1 .list-items_1 .box_3 .block_4 .group_3 {
  width: 446rpx;
  height: 32rpx;
}
.page .section_2 .list_1 .list-items_1 .box_3 .block_4 .group_3 .text_5 {
  width: 92rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .list_1 .list-items_1 .box_3 .block_4 .group_3 .text-wrapper_3 {
  background-color: rgba(62, 200, 174, 0.2);
  border-radius: 3px;
  height: 26rpx;
  margin-top: 2rpx;
  width: 226rpx;
}
.page .section_2 .list_1 .list-items_1 .box_3 .block_4 .group_3 .text-wrapper_3 .text_6 {
  width: 212rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 4rpx 0 0 8rpx;
}
.page .section_2 .list_1 .list-items_1 .box_3 .block_4 .text_7 {
  width: 332rpx;
  height: 54rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 152rpx;
  margin: 22rpx 0 0 2rpx;
}
.page .section_2 .list_1 .list-items_1 .box_4 {
  width: 442rpx;
  height: 32rpx;
  margin: 10rpx 0 28rpx 234rpx;
}
.page .section_2 .list_1 .list-items_1 .box_4 .text_8 {
  width: 110rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgb(231, 96, 81);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .section_2 .list_1 .list-items_1 .box_4 .image-text_4 {
  width: 110rpx;
  height: 26rpx;
  margin-top: 2rpx;
}
.page .section_2 .list_1 .list-items_1 .box_4 .image-text_4 .box_5 {
  background-color: rgb(11, 206, 148);
  width: 22rpx;
  height: 26rpx;
}
.page .section_2 .list_1 .list-items_1 .box_4 .image-text_4 .text-group_4 {
  width: 84rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 2rpx;
}
.page .section_4 {
  background-color: rgb(255, 255, 255);
  height: 142rpx;
  margin-top: -2rpx;
  width: 750rpx;
}
.page .section_4 .text-wrapper_4 {
  background-color: rgb(11, 206, 148);
  border-radius: 50px;
  height: 86rpx;
  width: 690rpx;
  margin: 28rpx 0 0 30rpx;
}
.page .section_4 .text-wrapper_4 .text_9 {
  width: 64rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 286rpx;
}
