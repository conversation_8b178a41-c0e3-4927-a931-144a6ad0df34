{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_shouyixiangqing/index.vue?77ae", "webpack:///./src/pages/lanhu_shouyixiangqing/index.vue?c44b", "webpack:///./src/pages/lanhu_shouyixiangqing/index.vue?b274", "webpack:///./src/pages/lanhu_shouyixiangqing/index.vue?f66f", "uni-app:///src/pages/lanhu_shouyixiangqing/index.vue", "webpack:///./src/pages/lanhu_shouyixiangqing/index.vue?d40b", "webpack:///./src/pages/lanhu_shouyixiangqing/index.vue?d173"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loopData0", "lanhutext0", "lanhutext1", "loopData1", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAA0D,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFvCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCmHtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QAAAC,UAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,UAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,UAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,UAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,UAAA;QAAAC,UAAA;MAAA,EACA;MACAC,SAAA,GACA;QAAAF,UAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,UAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,UAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,UAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,UAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,UAAA;QAAAC,UAAA;MAAA,EACA;MACAE,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;ACzIA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_shouyixiangqing/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_shouyixiangqing/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=862b6e20&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_shouyixiangqing/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=862b6e20&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"group_1 flex-col justify-between\">\n      <view class=\"box_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyixiangqing/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyixiangqing/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyixiangqing/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"box_2 flex-row\">\n        <image\n          class=\"thumbnail_4\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyixiangqing/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n        />\n        <text class=\"text_2\">收益详情</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyixiangqing/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n        />\n      </view>\n    </view>\n    <view class=\"group_2 flex-col\">\n      <view class=\"section_1 flex-col\"></view>\n      <view class=\"section_2 flex-col\">\n        <view class=\"group_3 flex-row justify-between\">\n          <view class=\"box_3 flex-col\"></view>\n          <text class=\"text_3\">订单号&nbsp;&nbsp;202544574764678756316465</text>\n        </view>\n        <text class=\"text_4\">项目明细</text>\n      </view>\n      <view class=\"text-wrapper_1 flex-row justify-between\">\n        <text class=\"text_5\">测试服务&nbsp;&nbsp;x1</text>\n        <text class=\"text_6\">￥10</text>\n      </view>\n      <view class=\"text-wrapper_2 flex-row justify-between\">\n        <text class=\"text_7\">服务时间</text>\n        <text class=\"text_8\">2024-12-23&nbsp;17:22-16:00</text>\n      </view>\n      <view class=\"list_1 flex-col\">\n        <view\n          class=\"text-wrapper_3 flex-row justify-between\"\n          v-for=\"(item, index) in loopData0\"\n          :key=\"index\"\n        >\n          <text class=\"text_9\" v-html=\"item.lanhutext0\"></text>\n          <text class=\"text_10\" v-html=\"item.lanhutext1\"></text>\n        </view>\n      </view>\n      <view class=\"section_3 flex-row justify-between\">\n        <view class=\"image-text_1 flex-row justify-between\">\n          <text class=\"text-group_1\">应得收益</text>\n          <image\n            class=\"thumbnail_5\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyixiangqing/FigmaDDSSlicePNG5095d4c5e87cb8a5e7d614cb3375a189.png\"\n          />\n        </view>\n        <view class=\"text-wrapper_4\">\n          <text class=\"text_11\">(10+1.00-1.00-0-0)*10%=</text>\n          <text class=\"text_12\">￥1</text>\n        </view>\n      </view>\n      <view class=\"text-wrapper_5 flex-col\">\n        <text class=\"text_13\">扣费明细</text>\n      </view>\n      <view class=\"list_2 flex-col\">\n        <view\n          class=\"text-wrapper_6 flex-row justify-between\"\n          v-for=\"(item, index) in loopData1\"\n          :key=\"index\"\n        >\n          <text class=\"text_14\" v-html=\"item.lanhutext0\"></text>\n          <text class=\"text_15\" v-html=\"item.lanhutext1\"></text>\n        </view>\n      </view>\n      <view class=\"section_4 flex-row justify-between\">\n        <view class=\"image-text_2 flex-row justify-between\">\n          <text class=\"text-group_2\">合计扣费</text>\n          <image\n            class=\"thumbnail_6\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyixiangqing/FigmaDDSSlicePNG5095d4c5e87cb8a5e7d614cb3375a189.png\"\n          />\n        </view>\n        <text class=\"text_16\">￥0</text>\n      </view>\n      <view class=\"text-wrapper_7 flex-col\">\n        <text class=\"text_17\">车费明细</text>\n      </view>\n      <view class=\"text-wrapper_8 flex-row justify-between\">\n        <text class=\"text_18\">车费收益</text>\n        <text class=\"text_19\">￥63.00</text>\n      </view>\n    </view>\n    <view class=\"text-wrapper_9 flex-row justify-between\">\n      <text class=\"text_20\">车费明细</text>\n      <text class=\"text_21\">1-0=65.96=66.96</text>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      loopData0: [\n        { lanhutext0: '项目总金额', lanhutext1: '￥10' },\n        { lanhutext0: '物料费', lanhutext1: '￥1.00' },\n        { lanhutext0: '卡券优惠', lanhutext1: '-￥0' },\n        { lanhutext0: '退款金额', lanhutext1: '-￥10' },\n        { lanhutext0: '分成比例', lanhutext1: '10%' }\n      ],\n      loopData1: [\n        { lanhutext0: '经纪人扣款', lanhutext1: '-￥0' },\n        { lanhutext0: '渠道扣款', lanhutext1: '-￥0' },\n        { lanhutext0: '业务扣款', lanhutext1: '-￥0' },\n        { lanhutext0: '储值扣款', lanhutext1: '-￥0' },\n        { lanhutext0: '广告费', lanhutext1: '-￥0' },\n        { lanhutext0: '提现手续费', lanhutext1: '-￥0' }\n      ],\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332586830\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}