@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(255, 255, 255);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
}
.page .box_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 172rpx;
}
.page .box_1 .section_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .box_1 .section_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_1 .section_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .box_1 .section_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .box_1 .section_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .box_1 .section_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .box_1 .section_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .box_1 .section_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .box_1 .section_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .box_2 {
  height: 568rpx;
  background: url(/static/lanhu_ditu/FigmaDDSSlicePNGa353739cd73f449728c306c482d1dfa5.png) 100% no-repeat;
  background-size: 100% 100%;
  width: 750rpx;
  position: relative;
}
.page .box_2 .section_3 {
  background-image: -webkit-linear-gradient(bottom, rgba(217, 217, 217, 0) 0, rgb(115, 115, 115) 100%);
  background-image: linear-gradient(360deg, rgba(217, 217, 217, 0) 0, rgb(115, 115, 115) 100%);
  width: 750rpx;
  height: 130rpx;
}
.page .box_2 .section_3 .group_1 {
  background-color: rgb(255, 255, 255);
  width: 32rpx;
  height: 32rpx;
  margin: 32rpx 0 0 24rpx;
}
.page .box_2 .section_3 .label_1 {
  width: 92rpx;
  height: 62rpx;
  margin: 34rpx 24rpx 0 0;
}
.page .box_2 .label_2 {
  position: absolute;
  left: 634rpx;
  top: 112rpx;
  width: 92rpx;
  height: 62rpx;
}
.page .box_3 {
  width: 750rpx;
  height: 886rpx;
  margin-bottom: 2rpx;
}
.page .box_3 .text-wrapper_1 {
  height: 78rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAV8AAAAnCAYAAACos0uZAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHfSURBVHgB7drBTeNAFIDhmYECgpSQK+mATsJ2ACVQASkhHbBUsKETOiBXElAicQTifUbaVRSBhATxXL5PcmxPfP71NHZOH1itVr2Xl5eznPM4jtOmaU4SAF91F8c8+jnr9/s3Hz2QdxceHh7ODg4OriO4vQTAt0SA53Ga7Ea4bN88Pj5OSyl/hBfgZ7Q7B3H8jsF2sr3+f/JdLpftH1cJgH2ZDgaDy/biPb6LxeI8RuPrBMBebTabX8PhcPYe39huuPdSDaAT68PDw1Fpp17hBehM7+3tbRzv18o4AdCZGHjPiqkXoFvR3dO8XC6bBECnSgKgc+ILUIH4AlQgvgAViC9ABeILUIH4AlQgvgAViC9ABeILUIH4AlQgvgAViC9ABeILUIH4AlQgvgAViC9ABeILUIH4AlQgvgAVlJzzPAHQmaZp7trJ9y4B0Jl26C2bzeY2AdCZiO8sr1ar3uvr633c9xIAexVbDvPj4+NROTo6WkeFLxIAe1dKmbyf259+vz+L0zQBsE+T6O1Ne5G3V5+enq5iD3iSAPhp08FgcPnvJu/+u1gszuN0FVsRJwmA71rHUHsxHA5n24v5s6fbCEeAx3F5EsdpAuBL2k/J2m95268anp+fb0ej0Xr3mb/Lyo0RBAtVPgAAAABJRU5ErkJggg==) 100% no-repeat;
  background-size: 100% 100%;
  width: 702rpx;
  margin: 28rpx 0 0 24rpx;
}
.page .box_3 .text-wrapper_1 .text_3 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 26rpx 0 0 32rpx;
}
.page .box_3 .section_4 {
  width: 750rpx;
  height: 672rpx;
  margin: 4rpx 0 104rpx 0;
}
.page .box_3 .section_4 .section_5 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 112rpx;
  border: 1px solid rgb(238, 238, 238);
}
.page .box_3 .section_4 .section_5 .text-group_1 {
  width: 304rpx;
  height: 62rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .box_3 .section_4 .section_5 .text-group_1 .text_4 {
  width: 140rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .box_3 .section_4 .section_5 .text-group_1 .text_5 {
  width: 304rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 10rpx;
}
.page .box_3 .section_4 .section_5 .group_2 {
  background-color: rgb(11, 206, 148);
  width: 36rpx;
  height: 32rpx;
  margin: 40rpx 30rpx 0 356rpx;
}
.page .box_3 .section_4 .text-group_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 112rpx;
  border: 1px solid rgb(238, 238, 238);
}
.page .box_3 .section_4 .text-group_2 .text_6 {
  width: 140rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .box_3 .section_4 .text-group_2 .text_7 {
  width: 304rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 26rpx 24rpx;
}
.page .box_3 .section_4 .text-group_3 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 112rpx;
  border: 1px solid rgb(238, 238, 238);
}
.page .box_3 .section_4 .text-group_3 .text_8 {
  width: 140rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .box_3 .section_4 .text-group_3 .text_9 {
  width: 304rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 26rpx 24rpx;
}
.page .box_3 .section_4 .text-group_4 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 112rpx;
  border: 1px solid rgb(238, 238, 238);
}
.page .box_3 .section_4 .text-group_4 .text_10 {
  width: 140rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .box_3 .section_4 .text-group_4 .text_11 {
  width: 304rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 26rpx 24rpx;
}
.page .box_3 .section_4 .text-group_5 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 112rpx;
  border: 1px solid rgb(238, 238, 238);
}
.page .box_3 .section_4 .text-group_5 .text_12 {
  width: 140rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .box_3 .section_4 .text-group_5 .text_13 {
  width: 304rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 26rpx 24rpx;
}
.page .box_3 .section_4 .text-group_6 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 112rpx;
  border: 1px solid rgb(238, 238, 238);
}
.page .box_3 .section_4 .text-group_6 .text_14 {
  width: 140rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 24rpx 0 0 24rpx;
}
.page .box_3 .section_4 .text-group_6 .text_15 {
  width: 304rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 10rpx 0 26rpx 24rpx;
}
