@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(255, 255, 255);
  position: relative;
  width: 750rpx;
  height: 1628rpx;
  overflow: hidden;
}
.page .block_1 {
  background-color: rgb(237, 237, 237);
  width: 750rpx;
  height: 172rpx;
}
.page .block_1 .box_1 {
  background-color: rgb(237, 237, 237);
  width: 750rpx;
  height: 64rpx;
}
.page .block_1 .box_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .block_1 .box_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .block_1 .box_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .block_1 .box_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .block_1 .box_2 {
  background-color: rgb(237, 237, 237);
  width: 750rpx;
  height: 108rpx;
}
.page .block_1 .box_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .block_1 .box_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .block_1 .box_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .block_2 {
  width: 750rpx;
  height: 1458rpx;
  margin-bottom: 2rpx;
}
.page .block_2 .section_1 {
  height: 240rpx;
  width: 240rpx;
  margin: 78rpx 0 0 264rpx;
}
.page .block_2 .section_1 .group_1 {
  width: 148rpx;
  height: 20rpx;
  margin: 26rpx 0 0 52rpx;
}
.page .block_2 .section_1 .group_1 .group_2 {
  background-color: rgb(251, 158, 48);
  width: 14rpx;
  height: 14rpx;
  margin-top: 6rpx;
}
.page .block_2 .section_1 .group_1 .group_3 {
  border-radius: 50%;
  width: 12rpx;
  height: 12rpx;
  border: 1px solid rgb(39, 171, 128);
}
.page .block_2 .section_1 .group_4 {
  width: 182rpx;
  height: 162rpx;
  margin: 10rpx 0 22rpx 10rpx;
}
.page .block_2 .section_1 .group_4 .thumbnail_5 {
  width: 14rpx;
  height: 14rpx;
  margin-top: 24rpx;
}
.page .block_2 .section_1 .group_4 .group_5 {
  border-radius: 50%;
  width: 10rpx;
  height: 10rpx;
  border: 1px solid rgb(39, 171, 128);
  margin: 102rpx 0 0 6rpx;
}
.page .block_2 .section_1 .group_4 .group_6 {
  background-image: -webkit-linear-gradient(135deg, rgb(39, 171, 128) 0, rgb(90, 227, 183) 100%);
  background-image: linear-gradient(315deg, rgb(39, 171, 128) 0, rgb(90, 227, 183) 100%);
  border-radius: 5px;
  height: 162rpx;
  margin-left: 14rpx;
  width: 138rpx;
  position: relative;
}
.page .block_2 .section_1 .group_4 .group_6 .box_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 5px;
  height: 140rpx;
  width: 118rpx;
  margin: 12rpx 0 0 10rpx;
}
.page .block_2 .section_1 .group_4 .group_6 .box_3 .group_7 {
  height: 104rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAA1CAYAAAA3Q3kVAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGGSURBVHgB7djPUcJQEAbwb0PGkfEfFODEVIBWACXQgdCBdGAJWAFSgZaAFSgVJHjwHEEGYSDrJiMSuAiXsDPs75KXN+/wZl8y2XwEETCXgFEd6lDo01kvHQU8rjtAR8YlqMRtj05bNODxq9xdQ7EYfCPF1FrJFReFC9koP0C38JKKL66cfzvgYURwb6EMAW8xptoLaYwx5iBR0osWMHlicNJBKWhQKIwxb/l0/rw2+87jDgMN6BLFmPk+laPlhCObrEEfOeWjanbCISk1FFpg1s/eOwtMm3INoQrdybGHazPLwQezBwWmiD6zz6YxxpgDk36ZAh7VHDhV5CTGor/Zxv1H+tGvhgPqIGeS0EnPedredv0+Y8fIo5Pytov3GTvu1HjsM3Z83GXx78s0lOfUrch/Uy7VJXBP4s4ujDHGmE1/AUTAkyso41MxXI4p2WABLIke16BOkovRvUfFrtbYMSuNILXGjlklF8cVtbHjCkdzfA/S2FE2u9P/S35Yjj1ubkaQqv0AsX6FgGC1iNwAAAAASUVORK5CYII=) 100% no-repeat;
  background-size: 100% 100%;
  width: 84rpx;
  margin: 18rpx 0 0 14rpx;
}
.page .block_2 .section_1 .group_4 .group_6 .box_3 .group_7 .block_3 {
  background-color: rgb(39, 171, 128);
  border-radius: 50%;
  height: 82rpx;
  border: 2px solid rgb(255, 255, 255);
  width: 80rpx;
  position: relative;
  margin: 34rpx 0 0 64rpx;
}
.page .block_2 .section_1 .group_4 .group_6 .box_3 .group_7 .block_3 .box_4 {
  background-color: rgb(255, 255, 255);
  width: 52rpx;
  height: 40rpx;
  margin: 24rpx 0 0 14rpx;
}
.page .block_2 .section_1 .group_4 .group_6 .box_3 .group_7 .block_3 .thumbnail_6 {
  position: absolute;
  left: 74rpx;
  top: 58rpx;
  width: 16rpx;
  height: 16rpx;
}
.page .block_2 .section_1 .group_4 .group_6 .box_3 .group_7 .block_3 .box_5 {
  background-color: rgb(134, 108, 255);
  border-radius: 50%;
  position: absolute;
  left: 78rpx;
  top: -4rpx;
  width: 8rpx;
  height: 8rpx;
}
.page .block_2 .section_1 .group_4 .group_6 .image_2 {
  position: absolute;
  left: 34rpx;
  top: -18rpx;
  width: 66rpx;
  height: 24rpx;
}
.page .block_2 .text-group_1 {
  width: 624rpx;
  height: 86rpx;
  margin: 42rpx 0 0 72rpx;
}
.page .block_2 .text-group_1 .text_3 {
  width: 120rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-left: 252rpx;
}
.page .block_2 .text-group_1 .text_4 {
  width: 624rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 260rpx;
  margin-top: 22rpx;
}
.page .block_2 .section_2 {
  background-color: rgb(255, 255, 255);
  height: 126rpx;
  width: 750rpx;
  margin: 112rpx 0 774rpx 0;
}
.page .block_2 .section_2 .text-wrapper_1 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 88rpx;
  width: 690rpx;
  margin: 20rpx 0 0 30rpx;
}
.page .block_2 .section_2 .text-wrapper_1 .text_5 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 256rpx;
}
