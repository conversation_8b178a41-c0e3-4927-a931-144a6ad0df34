@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(247, 248, 250);
  position: relative;
  width: 750rpx;
  height: 2290rpx;
  overflow: hidden;
}
.page .box_1 {
  background-color: rgb(255, 255, 255);
  height: 176rpx;
  width: 750rpx;
}
.page .box_1 .box_2 {
  width: 696rpx;
  height: 40rpx;
  margin: 24rpx 0 0 28rpx;
}
.page .box_1 .box_2 .text_1 {
  width: 108rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 40rpx;
}
.page .box_1 .box_2 .block_1 {
  background-color: rgb(0, 0, 0);
  width: 34rpx;
  height: 22rpx;
  margin: 10rpx 0 0 454rpx;
}
.page .box_1 .box_2 .block_2 {
  background-color: rgb(0, 0, 0);
  width: 32rpx;
  height: 22rpx;
  margin: 10rpx 0 0 10rpx;
}
.page .box_1 .box_2 .image_1 {
  width: 50rpx;
  height: 24rpx;
  margin: 10rpx 0 0 8rpx;
}
.page .box_1 .box_3 {
  width: 416rpx;
  height: 36rpx;
  margin: 50rpx 0 26rpx 44rpx;
}
.page .box_1 .box_3 .thumbnail_1 {
  width: 18rpx;
  height: 32rpx;
  margin-top: 4rpx;
}
.page .box_1 .box_3 .text_2 {
  width: 170rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 34rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 36rpx;
}
.page .box_4 {
  position: relative;
  width: 750rpx;
  height: 2116rpx;
  margin-bottom: 2rpx;
}
.page .box_4 .group_1 {
  background-color: rgb(255, 255, 255);
  height: 108rpx;
  width: 750rpx;
}
.page .box_4 .group_1 .section_1 {
  background-color: rgb(247, 248, 250);
  border-radius: 17px;
  width: 686rpx;
  height: 68rpx;
  margin: 20rpx 0 0 32rpx;
}
.page .box_4 .group_1 .section_1 .image-text_1 {
  width: 638rpx;
  height: 44rpx;
  margin: 12rpx 0 0 24rpx;
}
.page .box_4 .group_1 .section_1 .image-text_1 .thumbnail_2 {
  width: 32rpx;
  height: 32rpx;
  margin-top: 6rpx;
}
.page .box_4 .group_1 .section_1 .image-text_1 .text-group_1 {
  width: 598rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(200, 201, 204);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .text_3 {
  width: 112rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .box_4 .group_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  width: 700rpx;
  height: 100rpx;
  margin: 14rpx 0 0 24rpx;
}
.page .box_4 .group_2 .image-text_2 {
  width: 138rpx;
  height: 44rpx;
  margin: 28rpx 0 0 24rpx;
}
.page .box_4 .group_2 .image-text_2 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin-top: 2rpx;
}
.page .box_4 .group_2 .image-text_2 .text-group_2 {
  width: 84rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .group_2 .image-text_3 {
  width: 142rpx;
  height: 44rpx;
  margin: 28rpx 24rpx 0 0;
}
.page .box_4 .group_2 .image-text_3 .thumbnail_4 {
  width: 28rpx;
  height: 28rpx;
  margin-top: 8rpx;
}
.page .box_4 .group_2 .image-text_3 .text-group_3 {
  width: 96rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .text_4 {
  width: 686rpx;
  height: 64rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 64rpx;
  margin: 26rpx 0 0 32rpx;
}
.page .box_4 .group_3 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_3 .text_5 {
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_3 .image_2 {
  width: 686rpx;
  height: 2rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_4 {
  background-color: rgb(255, 255, 255);
  height: 110rpx;
  width: 750rpx;
}
.page .box_4 .group_4 .text-wrapper_1 {
  width: 686rpx;
  height: 44rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_4 .text-wrapper_1 .text_6 {
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .group_4 .box_5 {
  width: 702rpx;
  height: 28rpx;
  margin: 8rpx 0 2rpx 32rpx;
}
.page .box_4 .group_4 .box_5 .image_3 {
  width: 686rpx;
  height: 2rpx;
  margin-top: 24rpx;
}
.page .box_4 .group_4 .box_5 .text_7 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(87, 192, 81);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin-left: -8rpx;
}
.page .box_4 .group_5 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_5 .text_8 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 2rpx 0 0 710rpx;
}
.page .box_4 .group_5 .section_2 {
  position: relative;
  width: 702rpx;
  height: 56rpx;
  margin-left: 32rpx;
}
.page .box_4 .group_5 .section_2 .text-wrapper_2 {
  width: 24rpx;
  height: 56rpx;
  margin-left: 678rpx;
}
.page .box_4 .group_5 .section_2 .text-wrapper_2 .text_9 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_5 .section_2 .text-wrapper_2 .text_10 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_5 .section_2 .text_11 {
  position: absolute;
  left: 0;
  top: 2rpx;
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .group_5 .section_3 {
  width: 702rpx;
  height: 28rpx;
  margin: 0 0 4rpx 32rpx;
}
.page .box_4 .group_5 .section_3 .image_4 {
  width: 686rpx;
  height: 2rpx;
  margin-top: 22rpx;
}
.page .box_4 .group_5 .section_3 .text_12 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin-left: -8rpx;
}
.page .box_4 .group_6 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_6 .text_13 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 4rpx 0 0 710rpx;
}
.page .box_4 .group_6 .box_6 {
  position: relative;
  width: 702rpx;
  height: 56rpx;
  margin-left: 32rpx;
}
.page .box_4 .group_6 .box_6 .text-wrapper_3 {
  width: 24rpx;
  height: 56rpx;
  margin-left: 678rpx;
}
.page .box_4 .group_6 .box_6 .text-wrapper_3 .text_14 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_6 .box_6 .text-wrapper_3 .text_15 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_6 .box_6 .text_16 {
  position: absolute;
  left: 0;
  top: 0;
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .group_6 .image_5 {
  width: 686rpx;
  height: 2rpx;
  margin: 20rpx 0 0 32rpx;
}
.page .box_4 .group_7 {
  background-color: rgb(255, 255, 255);
  position: relative;
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_7 .group_8 {
  position: relative;
  width: 702rpx;
  height: 58rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_7 .group_8 .text-wrapper_4 {
  width: 24rpx;
  height: 56rpx;
  margin: 2rpx 0 0 678rpx;
}
.page .box_4 .group_7 .group_8 .text-wrapper_4 .text_17 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_7 .group_8 .text-wrapper_4 .text_18 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_7 .group_8 .text_19 {
  position: absolute;
  left: 0;
  top: 0;
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .group_7 .image_6 {
  width: 686rpx;
  height: 2rpx;
  margin: 18rpx 0 0 32rpx;
}
.page .box_4 .group_7 .text_20 {
  position: absolute;
  left: 710rpx;
  top: 6rpx;
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_9 {
  background-color: rgb(255, 255, 255);
  position: relative;
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_9 .group_10 {
  position: relative;
  width: 702rpx;
  height: 60rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_9 .group_10 .text-wrapper_5 {
  width: 24rpx;
  height: 56rpx;
  margin: 4rpx 0 0 678rpx;
}
.page .box_4 .group_9 .group_10 .text-wrapper_5 .text_21 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_9 .group_10 .text-wrapper_5 .text_22 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_9 .group_10 .text_23 {
  position: absolute;
  left: 0;
  top: 0;
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .group_9 .image_7 {
  width: 686rpx;
  height: 2rpx;
  margin: 16rpx 0 0 32rpx;
}
.page .box_4 .group_9 .text_24 {
  position: absolute;
  left: 710rpx;
  top: 8rpx;
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_11 {
  background-color: rgb(255, 255, 255);
  position: relative;
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_11 .box_7 {
  position: relative;
  width: 702rpx;
  height: 62rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_11 .box_7 .text-wrapper_6 {
  width: 24rpx;
  height: 56rpx;
  margin: 6rpx 0 0 678rpx;
}
.page .box_4 .group_11 .box_7 .text-wrapper_6 .text_25 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_11 .box_7 .text-wrapper_6 .text_26 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_11 .box_7 .text_27 {
  position: absolute;
  left: 0;
  top: 0;
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .group_11 .image_8 {
  width: 686rpx;
  height: 2rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_4 .group_11 .text_28 {
  position: absolute;
  left: 710rpx;
  top: 10rpx;
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_12 {
  background-color: rgb(255, 255, 255);
  height: 110rpx;
  width: 750rpx;
}
.page .box_4 .group_12 .box_8 {
  position: relative;
  width: 702rpx;
  height: 84rpx;
  margin: 12rpx 0 0 32rpx;
}
.page .box_4 .group_12 .box_8 .text-wrapper_7 {
  width: 24rpx;
  height: 84rpx;
  margin-left: 678rpx;
}
.page .box_4 .group_12 .box_8 .text-wrapper_7 .text_29 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_12 .box_8 .text-wrapper_7 .text_30 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_12 .box_8 .text-wrapper_7 .text_31 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .group_12 .box_8 .text_32 {
  position: absolute;
  left: 0;
  top: 20rpx;
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_4 .group_12 .image-wrapper_1 {
  width: 686rpx;
  height: 2rpx;
  margin: 12rpx 0 0 32rpx;
}
.page .box_4 .group_12 .image-wrapper_1 .image_9 {
  width: 686rpx;
  height: 2rpx;
}
.page .box_4 .text_33 {
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: -14rpx 0 0 710rpx;
}
.page .box_4 .text_34 {
  width: 686rpx;
  height: 64rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 64rpx;
  margin: 2rpx 0 0 32rpx;
}
.page .box_4 .group_13 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_13 .text_35 {
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_13 .image_10 {
  width: 686rpx;
  height: 2rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .image_11 {
  width: 750rpx;
  height: 68rpx;
  margin-top: 90rpx;
}
.page .box_4 .group_14 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 110rpx;
  margin-top: 62rpx;
}
.page .box_4 .group_14 .text_36 {
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_14 .image_12 {
  width: 686rpx;
  height: 2rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_15 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_15 .text_37 {
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_15 .image_13 {
  width: 686rpx;
  height: 2rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_16 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_16 .text_38 {
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_16 .image_14 {
  width: 686rpx;
  height: 2rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_17 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 110rpx;
  margin-bottom: 8rpx;
}
.page .box_4 .group_17 .text_39 {
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_17 .image_15 {
  width: 686rpx;
  height: 2rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_18 {
  background-color: rgb(255, 255, 255);
  position: absolute;
  left: 0;
  top: 1448rpx;
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_18 .text_40 {
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_18 .image_16 {
  width: 686rpx;
  height: 2rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_19 {
  background-color: rgb(255, 255, 255);
  position: absolute;
  left: 0;
  top: 1558rpx;
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_19 .text_41 {
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_19 .image_17 {
  width: 686rpx;
  height: 2rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_20 {
  background-color: rgb(255, 255, 255);
  position: absolute;
  left: 0;
  top: 2108rpx;
  width: 750rpx;
  height: 110rpx;
}
.page .box_4 .group_20 .text_42 {
  width: 686rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(51, 51, 51);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .group_20 .image_18 {
  width: 686rpx;
  height: 2rpx;
  margin: 32rpx 0 0 32rpx;
}
.page .box_4 .text_43 {
  position: absolute;
  left: 710rpx;
  top: 796rpx;
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .text_44 {
  position: absolute;
  left: 710rpx;
  top: 908rpx;
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .text_45 {
  position: absolute;
  left: 710rpx;
  top: 1020rpx;
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .text_46 {
  position: absolute;
  left: 710rpx;
  top: 1132rpx;
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_4 .text_47 {
  position: absolute;
  left: 710rpx;
  top: 1272rpx;
  width: 24rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(150, 151, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
/* 城市选择相关样式 */
.box_4 {
  height: calc(100vh - 200rpx); /* 设置滚动区域高度 */
}
/* 搜索框样式 */
.search-container {
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 20rpx;
  position: relative;
}
.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  -webkit-flex-shrink: 0;
          flex-shrink: 0;
}
.search-input {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
          flex: 1;
  font-size: 28rpx;
  color: #333;
  background-color: transparent;
  border: none;
  outline: none;
}
.search-input::-webkit-input-placeholder {
  color: #999;
}
.search-input::placeholder {
  color: #999;
}
.clear-btn {
  width: 40rpx;
  height: 40rpx;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  background-color: #ccc;
  border-radius: 50%;
  margin-left: 16rpx;
}
.clear-btn:active {
  background-color: #999;
}
.clear-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1;
}
.city-groups {
  position: relative;
  padding: 20rpx;
  padding-right: 80rpx; /* 为右侧字母索引留出空间 */
}
/* 右侧字母索引 */
.alphabet-index {
  position: fixed;
  right: 20rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  z-index: 100;
}
.alphabet-item {
  font-size: 24rpx;
  color: #666;
  padding: 8rpx 12rpx;
  margin: 2rpx 0;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 6rpx;
  text-align: center;
  min-width: 40rpx;
}
.alphabet-item:active {
  background-color: #007aff;
  color: #fff;
}
/* 城市分组 */
.city-group {
  margin-bottom: 40rpx;
}
.group-letter {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}
.group-city-item {
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.group-city-item:active {
  background-color: #f5f5f5;
}
.group-city-item:last-child {
  border-bottom: none;
}
.city-text {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 15rpx;
  padding-left: 10rpx;
}
.city-divider {
  width: 100%;
  height: 2rpx;
}
.loading-container {
  padding: 60rpx 20rpx;
  text-align: center;
}
.loading-text {
  font-size: 28rpx;
  color: #666;
}
.no-data-container {
  padding: 100rpx 20rpx;
  text-align: center;
}
.no-data-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}
.retry-btn {
  padding: 20rpx 40rpx;
  background-color: #007aff;
  border-radius: 10rpx;
  display: inline-block;
}
.retry-btn:active {
  background-color: #0056cc;
}
.retry-text {
  color: #fff;
  font-size: 28rpx;
}
/* 定位按钮禁用状态 */
.image-text_3.disabled {
  opacity: 0.6;
  pointer-events: none;
}
.image-text_3.disabled .text-group_3 {
  color: #999;
}
