@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(246, 246, 246);
  position: relative;
  width: 750rpx;
  height: 2614rpx;
  overflow: hidden;
}
.page .block_1 {
  width: 750rpx;
  height: 1874rpx;
}
.page .block_1 .group_1 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 64rpx;
}
.page .block_1 .group_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .block_1 .group_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 502rpx;
}
.page .block_1 .group_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin: 14rpx 0 0 6rpx;
}
.page .block_1 .group_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin: 14rpx 30rpx 0 6rpx;
}
.page .block_1 .group_2 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 108rpx;
}
.page .block_1 .group_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin: 38rpx 0 0 36rpx;
}
.page .block_1 .group_2 .text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 32rpx 0 0 22rpx;
}
.page .block_1 .group_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin: 22rpx 12rpx 0 360rpx;
}
.page .block_1 .group_3 {
  position: relative;
  width: 750rpx;
  height: 304rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAXcAAACYCAYAAAAfpakdAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIeSURBVHgB7dRBFQAQAEAxXNWXSyV6/LeF2Nz3vAFAyhoA5MgdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4QJHeAILkDBMkdIEjuAEFyBwiSO0CQ3AGC5A4Q9AGmZgOc/w8s3QAAAABJRU5ErkJggg==) 100% no-repeat;
  background-size: 100% 100%;
}
.page .block_1 .group_3 .box_1 {
  width: 172rpx;
  height: 52rpx;
  margin: 52rpx 0 0 248rpx;
}
.page .block_1 .group_3 .box_1 .label_1 {
  width: 48rpx;
  height: 48rpx;
  margin-top: 2rpx;
}
.page .block_1 .group_3 .box_1 .text_3 {
  width: 108rpx;
  height: 52rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 52rpx;
}
.page .block_1 .group_3 .text_4 {
  width: 668rpx;
  height: 52rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 52rpx;
  margin: 10rpx 0 138rpx 42rpx;
}
.page .block_1 .group_3 .box_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 190rpx;
  width: 702rpx;
  position: absolute;
  left: 24rpx;
  top: 210rpx;
}
.page .block_1 .group_3 .box_2 .list_1 {
  width: 610rpx;
  height: 106rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
  margin: 42rpx 0 0 46rpx;
}
.page .block_1 .group_3 .box_2 .list_1 .image-text_1 {
  width: 88rpx;
  height: 106rpx;
  margin-right: 42rpx;
}
.page .block_1 .group_3 .box_2 .list_1 .image-text_1 .label_2 {
  width: 72rpx;
  height: 72rpx;
  margin-left: 8rpx;
}
.page .block_1 .group_3 .box_2 .list_1 .image-text_1 .text-group_1 {
  width: 88rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 22rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 12rpx;
}
.page .block_1 .group_4 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 288rpx;
  width: 702rpx;
  margin: 116rpx 0 0 24rpx;
}
.page .block_1 .group_4 .text-wrapper_1 {
  width: 104rpx;
  height: 26rpx;
  margin: 36rpx 0 0 30rpx;
}
.page .block_1 .group_4 .text-wrapper_1 .text_5 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_4 .section_1 {
  width: 630rpx;
  height: 160rpx;
  margin: 38rpx 0 28rpx 30rpx;
}
.page .block_1 .group_4 .section_1 .image-text_2 {
  width: 388rpx;
  height: 160rpx;
}
.page .block_1 .group_4 .section_1 .image-text_2 .image_2 {
  width: 160rpx;
  height: 160rpx;
}
.page .block_1 .group_4 .section_1 .image-text_2 .text-group_2 {
  width: 212rpx;
  height: 140rpx;
  margin-top: 6rpx;
}
.page .block_1 .group_4 .section_1 .image-text_2 .text-group_2 .text_6 {
  width: 180rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_4 .section_1 .image-text_2 .text-group_2 .text_7 {
  width: 208rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 30rpx 0 0 4rpx;
}
.page .block_1 .group_4 .section_1 .image-text_2 .text-group_2 .text-wrapper_2 {
  width: 124rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 26rpx;
}
.page .block_1 .group_4 .section_1 .image-text_2 .text-group_2 .text-wrapper_2 .text_8 {
  width: 124rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_4 .section_1 .image-text_2 .text-group_2 .text-wrapper_2 .text_9 {
  width: 124rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_4 .section_1 .text_10 {
  width: 26rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 8rpx;
}
.page .block_1 .text-wrapper_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  width: 702rpx;
  height: 88rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_3 .text_11 {
  width: 78rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 32rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_3 .text_12 {
  width: 26rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 32rpx 24rpx 0 0;
}
.page .block_1 .text-wrapper_4 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 60rpx;
  margin-left: 24rpx;
}
.page .block_1 .text-wrapper_4 .text_13 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_4 .text_14 {
  width: 164rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 24rpx 0 0;
}
.page .block_1 .text-wrapper_5 {
  background-color: rgb(255, 255, 255);
  height: 60rpx;
  margin-left: 24rpx;
  width: 702rpx;
}
.page .block_1 .text-wrapper_5 .text_15 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 18rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 0px 10px 10px;
  height: 100rpx;
  margin-left: 24rpx;
  width: 702rpx;
}
.page .block_1 .text-wrapper_6 .paragraph_1 {
  width: 654rpx;
  height: 76rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 298rpx;
  margin: 2rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_7 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  width: 702rpx;
  height: 80rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_7 .text_16 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 32rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_7 .text_17 {
  width: 262rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 32rpx 22rpx 0 290rpx;
}
.page .block_1 .text-wrapper_8 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 68rpx;
  margin-left: 24rpx;
}
.page .block_1 .text-wrapper_8 .text_18 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_8 .text_19 {
  width: 292rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 22rpx 0 260rpx;
}
.page .block_1 .text-wrapper_9 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 68rpx;
  margin-left: 24rpx;
}
.page .block_1 .text-wrapper_9 .text_20 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_9 .text_21 {
  width: 84rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .block_1 .text-wrapper_10 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 68rpx;
  margin-left: 24rpx;
}
.page .block_1 .text-wrapper_10 .text_22 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_10 .text_23 {
  width: 236rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .block_1 .group_5 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 68rpx;
  margin-left: 24rpx;
}
.page .block_1 .group_5 .text_24 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .block_1 .group_5 .text-wrapper_11 {
  width: 112rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .block_1 .group_5 .text-wrapper_11 .text_25 {
  width: 112rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_5 .text-wrapper_11 .text_26 {
  width: 112rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .group_5 .text-wrapper_11 .text_27 {
  width: 112rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  text-decoration: line-through;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_1 .text-wrapper_12 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 68rpx;
  margin-left: 24rpx;
}
.page .block_1 .text-wrapper_12 .text_28 {
  width: 78rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_12 .text_29 {
  width: 54rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  text-decoration: line-through;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .block_1 .text-wrapper_13 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 68rpx;
  margin-left: 24rpx;
}
.page .block_1 .text-wrapper_13 .text_30 {
  width: 52rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_13 .text_31 {
  width: 54rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  text-decoration: line-through;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .block_1 .text-wrapper_14 {
  background-color: rgb(255, 255, 255);
  width: 702rpx;
  height: 68rpx;
  margin-left: 24rpx;
}
.page .block_1 .text-wrapper_14 .text_32 {
  width: 156rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_14 .text_33 {
  width: 74rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .block_1 .text-wrapper_15 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 0px 10px 10px;
  width: 702rpx;
  height: 68rpx;
  margin: 0 0 22rpx 24rpx;
}
.page .block_1 .text-wrapper_15 .text_34 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 0 0 24rpx;
}
.page .block_1 .text-wrapper_15 .text_35 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 22rpx 24rpx 0 0;
}
.page .block_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 516rpx;
  width: 702rpx;
  margin: -2rpx 0 0 24rpx;
}
.page .block_2 .block_3 {
  width: 636rpx;
  height: 50rpx;
  margin: 26rpx 0 0 24rpx;
}
.page .block_2 .block_3 .text_36 {
  width: 416rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 12rpx;
}
.page .block_2 .block_3 .text-wrapper_16 {
  border-radius: 4px;
  height: 50rpx;
  border: 1px solid rgb(11, 206, 148);
  width: 96rpx;
}
.page .block_2 .block_3 .text-wrapper_16 .text_37 {
  width: 48rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 14rpx 0 0 24rpx;
}
.page .block_2 .block_4 {
  width: 304rpx;
  height: 360rpx;
  margin: 32rpx 0 48rpx 24rpx;
}
.page .block_2 .block_4 .image-text_3 {
  width: 148rpx;
  height: 360rpx;
}
.page .block_2 .block_4 .image-text_3 .image_3 {
  width: 30rpx;
  height: 360rpx;
}
.page .block_2 .block_4 .image-text_3 .text-group_3 {
  width: 104rpx;
  height: 356rpx;
  margin-top: 2rpx;
}
.page .block_2 .block_4 .image-text_3 .text-group_3 .text_38 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_2 .block_4 .image-text_3 .text-group_3 .text_39 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 40rpx;
}
.page .block_2 .block_4 .image-text_3 .text-group_3 .text_40 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 40rpx;
}
.page .block_2 .block_4 .image-text_3 .text-group_3 .text_41 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 40rpx;
}
.page .block_2 .block_4 .image-text_3 .text-group_3 .text_42 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 40rpx;
}
.page .block_2 .block_4 .image-text_3 .text-group_3 .text_43 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 40rpx;
}
.page .block_2 .block_4 .text-wrapper_17 {
  width: 120rpx;
  height: 354rpx;
  margin-top: 2rpx;
}
.page .block_2 .block_4 .text-wrapper_17 .text_44 {
  width: 120rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
}
.page .block_2 .block_4 .text-wrapper_17 .text_45 {
  width: 120rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 42rpx;
}
.page .block_2 .block_4 .text-wrapper_17 .text_46 {
  width: 120rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 42rpx;
}
.page .block_2 .block_4 .text-wrapper_17 .text_47 {
  width: 120rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 42rpx;
}
.page .block_2 .block_4 .text-wrapper_17 .text_48 {
  width: 120rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 42rpx;
}
.page .block_2 .block_4 .text-wrapper_17 .text_49 {
  width: 120rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin-top: 42rpx;
}
.page .block_5 {
  background-color: rgb(255, 255, 255);
  width: 750rpx;
  height: 126rpx;
  margin-top: 100rpx;
}
.page .block_5 .text-wrapper_18 {
  background-color: rgb(228, 228, 228);
  border-radius: 100px;
  height: 64rpx;
  width: 150rpx;
  margin: 32rpx 0 0 406rpx;
}
.page .block_5 .text-wrapper_18 .text_50 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 24rpx;
}
.page .block_5 .text-wrapper_19 {
  background-color: rgb(11, 206, 148);
  border-radius: 100px;
  height: 64rpx;
  width: 150rpx;
  margin: 32rpx 24rpx 0 20rpx;
}
.page .block_5 .text-wrapper_19 .text_51 {
  width: 104rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 200rpx;
  margin: 20rpx 0 0 24rpx;
}
