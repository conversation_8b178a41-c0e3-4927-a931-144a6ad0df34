<view class="page flex-col"><view class="box_1 flex-col"><view class="box_3 flex-row justify-between"><image class="thumbnail_1" referrerpolicy="no-referrer" src="/static/cityselector/FigmaDDSSlicePNGa3fa9e448b139bfdff3a8dabbb6265ce.png"></image><text class="text_2">国家和地区</text></view></view><scroll-view class="box_4 flex-col" scroll-y="true" refresher-enabled="true" refresher-triggered="{{refreshing}}" scroll-into-view="{{scrollIntoView}}" data-event-opts="{{[['refresherrefresh',[['onRefresh',['$event']]]],['scrolltolower',[['loadMore',['$event']]]]]}}" bindrefresherrefresh="__e" bindscrolltolower="__e"><view class="group_1 flex-col"><view class="section_1 flex-row"><view class="search-container flex-row" style="padding-top:8px;"><image class="search-icon" referrerpolicy="no-referrer" src="/static/cityselector/FigmaDDSSlicePNGaf3e79b7cd05c51210e7b9ad1893305c.png"></image><input class="search-input" type="text" placeholder="请输入城市名称" confirm-type="search" data-event-opts="{{[['input',[['__set_model',['','searchKeyword','$event',[]]],['onSearchInput',['$event']]]],['confirm',[['onSearchConfirm',['$event']]]]]}}" value="{{searchKeyword}}" bindinput="__e" bindconfirm="__e"/><block wx:if="{{searchKeyword}}"><view data-event-opts="{{[['tap',[['clearSearch',['$event']]]]]}}" class="clear-btn" style="margin-left:99px;" bindtap="__e"><text class="clear-text">×</text></view></block></view></view></view><text class="text_3">当前城市</text><view class="group_2 flex-row justify-between"><view data-event-opts="{{[['tap',[['getCurrentLocation',['$event']]]]]}}" class="image-text_2 flex-row justify-between" bindtap="__e"><image class="thumbnail_3" referrerpolicy="no-referrer" src="/static/cityselector/FigmaDDSSlicePNG8ce251d7cdb03d0b79452073ad295013.png"></image><text class="text-group_2">{{''+(isLocating?'正在定位...':selectedCity?selectedCity.name:'请选择城市')+''}}</text></view><view data-event-opts="{{[['tap',[['handleLocationAction',['$event']]]]]}}" class="{{['image-text_3','flex-row','justify-between',(isLocating)?'disabled':'']}}" bindtap="__e"><image class="thumbnail_4" referrerpolicy="no-referrer" src="/static/cityselector/FigmaDDSSlicePNG7fdcf6ebdb51682c5d9d318fb66370c9.png"></image><text class="text-group_3">{{''+(isLocating?'定位中...':selectedCity?'清除选择':'重新定位')+''}}</text></view></view><block wx:if="{{groupedCities.length>0}}"><view class="city-groups"><view class="alphabet-index"><block wx:for="{{alphabetList}}" wx:for-item="letter" wx:for-index="__i0__" wx:key="*this"><text data-event-opts="{{[['tap',[['scrollToLetter',['$0'],[[['alphabetList','',__i0__]]]]]]]}}" class="alphabet-item" bindtap="__e">{{''+letter+''}}</text></block></view><view class="city-content"><block wx:for="{{groupedCities}}" wx:for-item="group" wx:for-index="__i1__" wx:key="letter"><view class="city-group" id="{{'letter-'+group.letter}}"><text class="group-letter">{{group.letter}}</text><block wx:for="{{group.cities}}" wx:for-item="city" wx:for-index="index"><view data-event-opts="{{[['tap',[['selectCity',['$0'],[[['groupedCities','letter',group.letter],['cities','code||index',city.code||index]]]]]]]}}" class="group-city-item flex-col justify-end" bindtap="__e"><text class="city-text">{{city.name}}</text><image class="city-divider" referrerpolicy="no-referrer" src="/static/cityselector/FigmaDDSSlicePNG917826c1fd1fcf3c0a049cf6d5026e0b.png"></image></view></block></view></block></view></view></block><block wx:if="{{loading}}"><view class="loading-container"><text class="loading-text">正在加载城市数据...</text></view></block><block wx:if="{{!loading&&cities.length===0}}"><view class="no-data-container"><text class="no-data-text">暂无城市数据</text></view></block></scroll-view></view>