{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/userhome/index.vue?3e4c", "webpack:///./src/pages/userhome/index.vue?412e", "webpack:///./src/pages/userhome/index.vue?b676", "webpack:///./src/pages/userhome/index.vue?0f9d", "uni-app:///src/pages/userhome/index.vue", "webpack:///./src/pages/userhome/index.vue?0182", "webpack:///./src/pages/userhome/index.vue?5cb5"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "carouselD<PERSON>", "src", "url", "loopData0", "lanhuimage0", "lanhutext0", "lanhutext1", "lanhutext2", "lanhutext3", "lanhutext4", "lanhutext5", "lanhutext6", "lanhutext7", "lanhutext8", "loopData1", "technicianList", "id", "name", "status", "earliestTime", "rating", "serviceCount", "freeTravel", "comments", "favorites", "shopType", "distance", "lanhuBg13", "lanhuimage1", "lanhuimage2", "avatar", "freeIcon", "tabsIndex", "layoutMode", "shopList", "shopImage", "shopName", "businessHours", "starIcon", "ratingInfo", "constants", "onLoad", "loadLayoutMode", "methods", "handleCarouselClick", "uni", "navigateTo", "toggleTabs", "index", "toggleLayout", "saveLayoutMode", "setStorageSync", "error", "console", "savedLayoutMode", "getStorageSync", "undefined"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAA6C,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAF1BG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCC6ftd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA,GACA;QACAC,GAAA;QACAC,GAAA;MACA,GACA;QACAD,GAAA;QACAC,GAAA;MACA,GACA;QACAD,GAAA;QACAC,GAAA;MACA,EACA;MACAC,SAAA,GACA;QACAC,WAAA,EACA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;MACA,GACA;QACAT,WAAA,EACA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;MACA,EACA;MACAC,SAAA,GACA;QACAV,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,EACA;MACA;MACAU,cAAA,GACA;QACAC,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAf,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;MACA,EACA;MACA;MACAC,SAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA,GACA;QACAC,SAAA;QACAC,QAAA;QACAhB,MAAA;QACAiB,aAAA;QACAC,QAAA;QACAC,UAAA;QACArB,MAAA;QACAG,YAAA;QACAK,QAAA;MACA,GACA;QACAS,SAAA;QACAC,QAAA;QACAhB,MAAA;QACAiB,aAAA;QACAC,QAAA;QACAC,UAAA;QACArB,MAAA;QACAG,YAAA;QACAK,QAAA;MACA,GACA;QACAS,SAAA;QACAC,QAAA;QACAhB,MAAA;QACAiB,aAAA;QACAC,QAAA;QACAC,UAAA;QACArB,MAAA;QACAG,YAAA;QACAK,QAAA;MACA,GACA;QACAS,SAAA;QACAC,QAAA;QACAhB,MAAA;QACAiB,aAAA;QACAC,QAAA;QACAC,UAAA;QACArB,MAAA;QACAG,YAAA;QACAK,QAAA;MACA,EACA;MACAc,SAAA;IACA;EACA;EACAC,MAAA,WAAAA,OAAA;IACA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAC,mBAAA,WAAAA,oBAAA1C,GAAA;MACA,IAAAA,GAAA;QACA2C,GAAA,CAAAC,UAAA;UACA5C,GAAA,EAAAA;QACA;MACA;IACA;IACA;IACA6C,UAAA,WAAAA,WAAAC,KAAA;MACA,KAAAhB,SAAA,GAAAgB,KAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAhB,UAAA,QAAAA,UAAA;MACA;MACA,KAAAiB,cAAA;IACA;IACA;IACAA,cAAA,WAAAA,eAAA;MACA;QACAL,GAAA,CAAAM,cAAA,8BAAAlB,UAAA;MACA,SAAAmB,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IACA;IACAV,cAAA,WAAAA,eAAA;MACA;QACA,IAAAY,eAAA,GAAAT,GAAA,CAAAU,cAAA;QACA,IAAAD,eAAA,WAAAA,eAAA,aAAAA,eAAA,KAAAE,SAAA;UACA,KAAAvB,UAAA,GAAAqB,eAAA;QACA;MACA,SAAAF,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA;QACA,KAAAnB,UAAA;MACA;IACA;EACA;AACA,E;;;;;;;;;;;;;AC7tBA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/userhome/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/userhome/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1fc8cbe6&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/userhome/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1fc8cbe6&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"block_1 flex-col justify-between\">\n      <view class=\"box_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/userhome/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/userhome/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/userhome/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"box_2 flex-row\">\n        <text class=\"text_2\">莎莎到家</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/userhome/FigmaDDSSlicePNG80b6aef7d115939c85032f2f6ff3a237.png\"\n        />\n      </view>\n    </view>\n\n    <!-- 城市定位 项目搜索 -->\n    <view class=\"block_2 flex-col\">\n      <view class=\"group_1 flex-row\">\n        <view class=\"image-text_1 flex-row justify-between\">\n          <image\n            class=\"text-group_1\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/images/uilogo.png\"\n          />\n        </view>\n        <text class=\"text_3\">平台担保，无额外收费急速退款</text>\n        <view class=\"section_1 flex-row\">\n          <view class=\"image-text_2 flex-row justify-between\">\n            <text class=\"text-group_2\">默认城市</text>\n            <image\n              class=\"block_3\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/images/vector.png\"\n            />\n          </view>\n          <text class=\"text_4\">请输入要查找的项目</text>\n          <image\n            class=\"label_1\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/FigmaDDSSlicePNG4b0c31646ada352b81d926d484d6514a.png\"\n          />\n        </view>\n      </view>\n      <!-- 城市定位 项目搜索 -->\n      \n      <!-- 轮播图  -->     \n      <swiper\n        class=\"carousel-container\"\n        indicator-dots=\"true\"\n        autoplay=\"true\"\n        interval=\"3000\"\n        duration=\"500\"\n        circular=\"true\"\n      >\n        <swiper-item\n          v-for=\"(item, index) in carouselData\"\n          :key=\"index\"\n          @click=\"handleCarouselClick(item.url)\"\n        >\n          <image\n            class=\"carousel-image\"\n            :src=\"item.src\"\n            mode=\"aspectFit\"\n          />\n        </swiper-item>\n      </swiper>\n      <!-- 轮播图  --> \n\n      <view class=\"block_7 flex-col\">\n      <view class=\"group_11 flex-row\">\n        <view class=\"image-text_9 flex-row justify-between\">\n          <image\n            class=\"thumbnail_6\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png\"\n          />\n          <text class=\"text-group_10\">实名认证</text>\n        </view>\n        <view class=\"image-text_10 flex-row justify-between\">\n          <image\n            class=\"thumbnail_7\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/FigmaDDSSlicePNGa311c0aa4464ab4a12f421c8228cb8a0.png\"\n          />\n          <text class=\"text-group_11\">爽约包赔</text>\n        </view>\n        <view class=\"image-text_11 flex-row justify-between\">\n          <image\n            class=\"thumbnail_8\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/FigmaDDSSlicePNG644082427060ccd4f6f07c248397a799.png\"\n          />\n          <text class=\"text-group_12\">超时秒退</text>\n        </view>\n        <view class=\"image-text_12 flex-row justify-between\">\n          <image\n            class=\"thumbnail_9\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/FigmaDDSSlicePNGe12dabfd56c3e4a4ac8b73a89a417089.png\"\n          />\n          <text class=\"text-group_13\">资质认证</text>\n        </view>\n      </view>\n      <view class=\"grid_1 flex-row\">\n        <view\n          class=\"image-text_13 flex-col justify-between\"\n          v-for=\"(item, index) in loopData1\"\n          :key=\"index\"\n        >\n          <image\n            class=\"label_6\"\n            referrerpolicy=\"no-referrer\"\n            :src=\"item.lanhuimage0\"\n          />\n          <text class=\"text-group_14\">{{ item.lanhutext0 }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 项目列表 -->\n      <!-- tabs -->\n      <view class=\"group_7 flex-row\">\n        <view class=\"tab-item\">\n          <text class=\"tab-text\" :class=\"{ 'tab-active': tabsIndex === 0 }\" @click=\"toggleTabs(0)\">预约上门</text>\n          <view class=\"tab-indicator\" v-if=\"tabsIndex === 0\"></view>\n        </view>\n        <view class=\"tab-item\">\n          <text class=\"tab-text\" :class=\"{ 'tab-active': tabsIndex === 1 }\" @click=\"toggleTabs(1)\">推荐技师</text>\n          <view class=\"tab-indicator\" v-if=\"tabsIndex === 1\"></view>\n        </view>\n        <view class=\"tab-item\">\n          <text class=\"tab-text\" :class=\"{ 'tab-active': tabsIndex === 2 }\" @click=\"toggleTabs(2)\">到店服务</text>\n          <view class=\"tab-indicator\" v-if=\"tabsIndex === 2\"></view>\n        </view>\n        <view class=\"image-text_3 flex-row justify-between\" @click=\"toggleLayout\">\n          <text class=\"text-group_4\">切换</text>\n          <image\n            class=\"thumbnail_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/FigmaDDSSlicePNG2027b34b07bbd8306566a9cb77906b41.png\"\n          />\n        </view>\n      </view>\n      <!-- tabs -->\n\n      <!--技师列表 栅格一行两列-->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"technician-grid\" v-if=\"tabsIndex === 1 && layoutMode === 0\" key=\"grid\">\n        <view class=\"technician-card\" v-for=\"technician in technicianList\" :key=\"technician.id\">\n          <!-- 技师头像背景区域 -->\n          <view class=\"card-avatar\">\n            <!-- 技师头像图片 -->\n            <image\n              class=\"technician-avatar\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"technician.avatar\"\n            />\n            <view class=\"time-badge\">\n              <view class=\"time-label-wrapper\">\n                <text class=\"time-label\">最早可约</text>\n              </view>\n              <text class=\"time-value\">{{ technician.earliestTime }}</text>\n            </view>\n          </view>\n\n          <!-- 技师信息卡片 -->\n          <view class=\"card-content\">\n            <!-- 技师姓名和状态 -->\n            <view class=\"technician-header\">\n              <text class=\"technician-name\">{{ technician.name }}</text>\n              <view class=\"status-badge\">\n                <text class=\"status-text\">{{ technician.status }}</text>\n              </view>\n            </view>\n\n            <!-- 评分和服务次数 -->\n            <view class=\"rating-section\">\n              <view class=\"rating-star\"></view>\n              <view class=\"service-info\">\n                <text class=\"rating-score\">{{ technician.rating }}</text>\n                <text class=\"service-count\">已服务{{ technician.serviceCount }}单</text>\n              </view>\n            </view>\n\n            <!-- 出行费用 -->\n            <view class=\"travel-fee\">\n              <image\n                class=\"fee-icon\"\n                referrerpolicy=\"no-referrer\"\n                :src=\"technician.freeIcon\"\n              />\n              <text class=\"fee-text\">{{ technician.freeTravel ? '免出行费用' : '需出行费用' }}</text>\n            </view>\n\n            <!-- 操作按钮 -->\n            <view class=\"action-buttons\">\n              <view class=\"btn-secondary\">\n                <text class=\"btn-text\">更多照片</text>\n              </view>\n              <view class=\"btn-primary\">\n                <text class=\"btn-text\">立即预约</text>\n              </view>\n            </view>\n\n            <!-- 底部图标信息 -->\n            <view class=\"bottom-info\">\n              <view class=\"info-item\">\n                <uni-icons type=\"chat\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.comments }}</text>\n              </view>\n              <view class=\"info-item\">\n                <uni-icons type=\"star\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.favorites }}</text>\n              </view>\n              <view class=\"info-item\">\n                <uni-icons type=\"shop\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.shopType }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!--技师列表 栅格一行两列-->\n\n      <!--技师列表 栅格一行一列-->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"technician-list-container flex-col\" v-if=\"tabsIndex === 1 && layoutMode === 1\" key=\"list\">\n        <view\n          class=\"technician-list-item flex-col\"\n          v-for=\"(item, index) in technicianList\"\n          :key=\"index\"\n        >\n          <view class=\"technician-info-top flex-row\">\n            <image\n              class=\"technician-avatar-img\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.avatar\"\n            />\n            <view class=\"single-row-image flex-col justify-between\">\n              <view class=\"technician-name-row flex-row justify-between\">\n                <text class=\"technician-name-text\" >{{ item.name }}</text>\n                <view class=\"technician-photos-btn flex-col\">\n                  <text class=\"technician-photos-text\" > 更多照片 </text>\n                </view>\n              </view>\n              <view class=\"technician-rating-row flex-row justify-between\">\n                <view class=\"technician-rating-area flex-row justify-between\">\n                  <view class=\"technician-star-icon flex-col\"></view>\n                  <text class=\"technician-rating-text\" >{{ item.rating }}</text>\n                </view>\n                <text class=\"technician-service-text\" >已服务{{item.serviceCount}}单</text>\n              </view>\n            </view>\n            <view class=\"single-row-time flex-col justify-between\">\n              <view class=\"technician-time-wrapper flex-col\">\n                <text class=\"technician-time-text\" >最早可约：{{item.earliestTime}}</text>\n              </view>\n              <view class=\"technician-distance-area flex-row justify-between\">\n                <view class=\"single-row-distance flex-col\">\n                  <uni-icons type=\"location\" size=\"16\" color=\"#0BCE94\"></uni-icons>\n                </view>\n                <text class=\"technician-distance-text\" >{{item.distance}}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"technician-info-bottom flex-row\">\n            <view\n              class=\"technician-status-badge flex-col\"\n              :style=\"{ background: item.lanhuBg13 }\"\n            >\n              <text class=\"technician-status-text\" >{{ item.status }}</text>\n            </view>\n            <view class=\"bottom-info\">\n              <!--评论-->\n              <view class=\"info-item\">\n                <uni-icons type=\"chat\" size=\"20\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ item.comments }}</text>\n              </view>\n\n              <!--收藏-->\n              <view class=\"info-item\">\n                <uni-icons type=\"star\" size=\"20\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ item.favorites }}</text>\n              </view>\n\n              <!--商家-->\n              <view class=\"info-item\">\n                <uni-icons type=\"shop\" size=\"20\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ item.shopType }}</text>\n              </view>\n            </view>\n            <view class=\"technician-book-btn flex-col\">\n              <text class=\"technician-book-text\" >立即预约</text>\n            </view>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!--技师列表 栅格一行一列-->\n\n      <!--到店服务栅格布局一行二列-->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"shop-grid\" v-if=\"tabsIndex === 2 && layoutMode === 0\" key=\"shop-grid\">\n        <view\n          class=\"shop-item flex-col\"\n          v-for=\"(shop, index) in shopList\"\n          :key=\"index\"\n        >\n          <image\n            class=\"shop-image\"\n            referrerpolicy=\"no-referrer\"\n            :src=\"shop.shopImage\"\n          />\n          <text class=\"shop-name\">{{ shop.shopName }}</text>\n          <view class=\"rating-section flex-row\">\n            <view class=\"star-bg flex-col\"></view>\n            <text class=\"rating-text\">{{ shop.rating }}</text>\n            <image\n              class=\"star-icon\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"shop.starIcon\"\n            />\n            <text class=\"hours-text\">{{ shop.businessHours }}</text>\n          </view>\n          <view class=\"distance-section flex-row justify-between\">\n            <image\n              class=\"location-icon\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG5a4163fb0783a530dfe7050cf95e5f4c.png\"\n            />\n            <text class=\"distance-text\">{{ shop.distance }}</text>\n          </view>\n          <view class=\"status-section flex-row justify-between\">\n            <view class=\"status-wrapper flex-col\">\n              <text class=\"status-text\">{{ shop.status }}</text>\n            </view>\n            <view class=\"service-wrapper flex-col\">\n              <text class=\"service-text\">{{ shop.serviceCount }}</text>\n            </view>\n          </view>\n          <view class=\"rating-info-wrapper flex-col\">\n            <text class=\"rating-info-text\">\n              {{ shop.ratingInfo }}\n            </text>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!--到店服务栅格布局一行二列-->\n\n      <!--到店服务一行一列-->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"shop-service-list flex-col\" v-if=\"tabsIndex === 2 && layoutMode === 1\" key=\"shop-list\">\n        <view\n          class=\"shop-service-item flex-row\"\n          v-for=\"(item, index) in shopList\"\n          :key=\"index\"\n        >\n          <view class=\"shop-service-main flex-row justify-between\">\n            <image\n              class=\"shop-service-image\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.shopImage\"\n            />\n            <view class=\"shop-service-info flex-col\">\n              <view class=\"shop-service-header flex-col justify-between\">\n                <text class=\"shop-service-name\">{{ item.shopName }}</text>\n                <view class=\"shop-service-rating-time flex-row justify-between\">\n                  <text class=\"shop-service-rating\">{{ item.rating }}</text>\n                  <text class=\"shop-service-hours\">{{ item.businessHours }}</text>\n                </view>\n              </view>\n              <view class=\"shop-service-divider flex-col\"></view>\n              <image\n                class=\"shop-service-star\"\n                referrerpolicy=\"no-referrer\"\n                :src=\"item.starIcon\"\n              />\n              <view class=\"shop-service-rating-info flex-col\">\n                <text class=\"shop-service-rating-text\">{{ item.ratingInfo }}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"shop-service-status flex-col\">\n            <text class=\"shop-service-status-text\">{{ item.status }}</text>\n          </view>\n          <view class=\"shop-service-details flex-col justify-between\">\n            <view class=\"shop-service-count flex-col\">\n              <text class=\"shop-service-count-text\">{{ item.serviceCount }}</text>\n            </view>\n            <view class=\"shop-service-distance flex-row justify-between\">\n              <view class=\"single-row-distance flex-col\">\n                  <uni-icons type=\"location\" size=\"16\" color=\"#0BCE94\"></uni-icons>\n              </view>\n              <text class=\"shop-service-distance-text\">{{ item.distance }}</text>\n            </view>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!--到店服务一行一列-->\n\n      <!-- 预约上门列表 -->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"list_1 flex-col\" v-if=\"tabsIndex === 0\" key=\"appointment-list\">\n        <view\n          class=\"list-items_1 flex-row justify-between\"\n          v-for=\"(item, index) in loopData0\"\n          :key=\"index\"\n        >\n          <view class=\"image-text_4 flex-row\">\n            <image\n              class=\"image_2\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.lanhuimage0\"\n            />\n            <view class=\"text-group_5 flex-col\">\n              <text class=\"text_15\">{{ item.lanhutext0 }}</text>\n              <text class=\"text_16\">{{ item.lanhutext1 }}</text>\n              <view class=\"section_2 flex-row justify-between\">\n                <view class=\"text-wrapper_3\">\n                  <text class=\"text_17\">{{ item.lanhutext2 }}</text>\n                  <text class=\"text_18\">{{ item.lanhutext3 }}</text>\n                </view>\n                <text class=\"text_19\">{{ item.lanhutext4 }}</text>\n              </view>\n            </view>\n            <view class=\"text-wrapper_4 flex-col\">\n              <text class=\"text_20\">{{ item.lanhutext5 }}</text>\n            </view>\n            <view class=\"box_4 flex-row\">\n              <view class=\"block_5 flex-col\">\n                <view class=\"section_3 flex-col\"></view>\n              </view>\n              <text class=\"text_21\">{{ item.lanhutext6 }}</text>\n            </view>\n            <view class=\"box_5 flex-row\">\n              <view class=\"image-text_5 flex-row justify-between\">\n                <view class=\"group_9 flex-col\"></view>\n                <text class=\"text-group_6\">{{ item.lanhutext7 }}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"text-wrapper_5 flex-col\">\n            <text class=\"text_22\">{{ item.lanhutext8 }}</text>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!-- 预约上门列表 -->\n    <!-- 项目列表 -->\n      \n      <view class=\"group_10 flex-row justify-around\">\n        <view class=\"block_6 flex-col justify-between\">\n          <image\n            class=\"thumbnail_5\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/FigmaDDSSlicePNG52ab17b699dcd97c93a69999aa9aa3be.png\"\n          />\n          <text class=\"text_23\">首页</text>\n        </view>\n        <view class=\"image-text_6 flex-col justify-between\">\n          <image\n            class=\"label_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/214f8f6d1a004f8bb0356c926f2f2e64_mergeImage.png\"\n          />\n          <text class=\"text-group_7\">技师</text>\n        </view>\n        <view class=\"image-text_7 flex-col justify-between\">\n          <image\n            class=\"label_3\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/686ee292c4f8476481b0ea86ecb376b9_mergeImage.png\"\n          />\n          <text class=\"text-group_8\">订单</text>\n        </view>\n        <view class=\"image-text_8 flex-col justify-between\">\n          <image\n            class=\"label_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/ab9da0d72e1642419c7bf7c81abfd950_mergeImage.png\"\n          />\n          <text class=\"text-group_9\">我的</text>\n        </view>\n      </view>\n      \n    </view>\n    \n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      carouselData: [\n        {\n          src: '/static/images/Carousel1.png',\n          url: '/pages/lanhu_shouyexiangmuliebiao_1/index'\n        },\n        {\n          src: '/static/images/Carousel1.png',\n          url: '/pages/lanhu_shouyexiangmuliebiao_1/index'\n        },\n        {\n          src: '/static/images/Carousel1.png',\n          url: '/pages/lanhu_shouyexiangmuliebiao_1/index'\n        }\n      ],\n      loopData0: [\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG31da488d12d11503c7344795ff172a3f.png',\n          lanhutext0: '狐狸舒适推拿',\n          lanhutext1: '肌肉舒张&nbsp;缓解腰肌劳损',\n          lanhutext2: '￥',\n          lanhutext3: '298',\n          lanhutext4: '￥482.00',\n          lanhutext5: '会员价',\n          lanhutext6: '5201已预约',\n          lanhutext7: '60分钟',\n          lanhutext8: '选择技师'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG31da488d12d11503c7344795ff172a3f.png',\n          lanhutext0: '狐狸舒适推拿',\n          lanhutext1: '肌肉舒张&nbsp;缓解腰肌劳损',\n          lanhutext2: '￥',\n          lanhutext3: '298',\n          lanhutext4: '￥482.00',\n          lanhutext5: '会员价',\n          lanhutext6: '5201已预约',\n          lanhutext7: '60分钟',\n          lanhutext8: '选择技师'\n        }\n      ],\n      loopData1: [\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG60c9e11ea671e9071f10ec4fa81522a1.png',\n          lanhutext0: '养生健康'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG9f11741a21b235bfe1bb6589f4525928.png',\n          lanhutext0: '伴游伴玩'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGe3af19da0f97379a81ec29a2a9ceaabc.png',\n          lanhutext0: '生活美容'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG5b04c16665581dc8eb7ff0beaf51b8fe.png',\n          lanhutext0: '家政维修'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGdbe146b7f68cb9d247a8920c57145588.png',\n          lanhutext0: '采耳专区'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGf455b9a4b2a2527551a2300706663739.png',\n          lanhutext0: '台球助教'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG4614c422961226f3f9e2bd57c296db6d.png',\n          lanhutext0: '女士专区'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGe1cbf6c812a20e3d29e3d746a1e07bdc.png',\n          lanhutext0: '小儿推拿'\n        }\n      ],\n      // 技师列表数据\n      technicianList: [\n        {\n          id: 1,\n          name: '王艳艳',\n          status: '可预约',\n          earliestTime: '11:00',\n          rating: 5,\n          serviceCount: 489,\n          freeTravel: true,\n          comments: 0,\n          favorites: 0,\n          shopType: '商家',\n          distance: '0.26km',\n          lanhuBg13: 'rgba(11,206,148,1.000000)',\n          lanhuimage1:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGd1ee15fc2ac6826faff86d6122434868.png',\n          lanhuimage2:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG18ba3b55b0fc8031440ec66e6e6398b9.png',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png'\n        },\n        {\n          id: 2,\n          name: '李美美',\n          status: '不可预约',\n          earliestTime: '12:00',\n          rating: 4.8,\n          serviceCount: 356,\n          freeTravel: false,\n          comments: 5,\n          favorites: 12,\n          shopType: '商家',\n          distance: '0.26km',\n          lanhuBg13: 'rgba(153, 153, 153, 1)',\n          lanhuimage1:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGd1ee15fc2ac6826faff86d6122434868.png',\n          lanhuimage2:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG18ba3b55b0fc8031440ec66e6e6398b9.png',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png'\n        }\n      ],\n      // 控制技师列表显示\n      tabsIndex: 0,\n      // 控制技师列表布局模式：0=一行两列，1=一行一列\n      layoutMode: 0,\n      //店铺列表\n      shopList: [\n        {\n          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',\n          shopName: '爱上你spa按摩...',\n          rating: '5',\n          businessHours: '00:00-12:15',\n          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',\n          ratingInfo: '好评率 11%  接单率 11%',\n          status: '营业中',\n          serviceCount: '1+次服务',\n          distance: '0.26KM'\n        },\n        {\n          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',\n          shopName: '按摩足浴店新...',\n          rating: '5',\n          businessHours: '00:00-12:15',\n          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',\n          ratingInfo: '好评率 11%  接单率 11%',\n          status: '营业中',\n          serviceCount: '1+次服务',\n          distance: '0.26KM'\n        },\n        {\n          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',\n          shopName: '康复理疗中心...',\n          rating: '4',\n          businessHours: '09:00-22:00',\n          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',\n          ratingInfo: '好评率 95%  接单率 88%',\n          status: '营业中',\n          serviceCount: '50+次服务',\n          distance: '1.2KM'\n        },\n        {\n          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',\n          shopName: '康复理疗中心...',\n          rating: '4',\n          businessHours: '09:00-22:00',\n          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',\n          ratingInfo: '好评率 95%  接单率 88%',\n          status: '营业中',\n          serviceCount: '50+次服务',\n          distance: '1.2KM'\n        }\n      ],\n      constants: {}\n    };\n  },\n  onLoad() {\n    // 页面加载时读取缓存的布局模式\n    this.loadLayoutMode();\n  },\n  methods: {\n    handleCarouselClick(url) {\n      if (url) {\n        uni.navigateTo({\n          url: url\n        });\n      }\n    },\n    // 切换技师列表显示状态\n    toggleTabs(index) {\n      this.tabsIndex = index;\n    },\n    // 切换技师列表布局模式\n    toggleLayout() {\n      this.layoutMode = this.layoutMode === 0 ? 1 : 0;\n      // 保存布局模式到缓存\n      this.saveLayoutMode();\n    },\n    // 保存布局模式到本地缓存\n    saveLayoutMode() {\n      try {\n        uni.setStorageSync('technicianLayoutMode', this.layoutMode);\n      } catch (error) {\n        console.error('保存布局模式失败:', error);\n      }\n    },\n    // 从本地缓存读取布局模式\n    loadLayoutMode() {\n      try {\n        const savedLayoutMode = uni.getStorageSync('technicianLayoutMode');\n        if (savedLayoutMode !== '' && savedLayoutMode !== null && savedLayoutMode !== undefined) {\n          this.layoutMode = savedLayoutMode;\n        }\n      } catch (error) {\n        console.error('读取布局模式失败:', error);\n        // 读取失败时使用默认值\n        this.layoutMode = 0;\n      }\n    }\n  }\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755332569269\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}