@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(245, 245, 245);
  position: relative;
  width: 750rpx;
  height: 1802rpx;
  overflow: hidden;
}
.page .box_1 {
  width: 750rpx;
  height: 990rpx;
  margin-top: 814rpx;
}
.page .box_1 .section_1 {
  width: 186rpx;
  height: 40rpx;
  margin: 386rpx 0 0 26rpx;
}
.page .box_1 .section_1 .text_1 {
  width: 174rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.page .box_1 .section_1 .thumbnail_1 {
  width: 10rpx;
  height: 18rpx;
  margin-top: 22rpx;
}
.page .box_1 .text_2 {
  width: 312rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 6rpx 0 0 26rpx;
}
.page .box_1 .list_1 {
  width: 750rpx;
  height: 606rpx;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
  margin: 44rpx 0 112rpx 0;
}
.page .box_1 .list_1 .list-items_1 {
  background-color: rgb(255, 255, 255);
  height: 202rpx;
  width: 750rpx;
}
.page .box_1 .list_1 .list-items_1 .text-wrapper_1 {
  width: 700rpx;
  height: 24rpx;
  margin: 38rpx 0 0 24rpx;
}
.page .box_1 .list_1 .list-items_1 .text-wrapper_1 .text_3 {
  width: 376rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
}
.page .box_1 .list_1 .list-items_1 .text-wrapper_1 .text_4 {
  width: 96rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(24, 200, 99);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 2rpx;
}
.page .box_1 .list_1 .list-items_1 .text-wrapper_2 {
  width: 222rpx;
  height: 20rpx;
  margin: 28rpx 0 0 24rpx;
}
.page .box_1 .list_1 .list-items_1 .text-wrapper_2 .text_5 {
  width: 222rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.page .box_1 .list_1 .list-items_1 .section_2 {
  width: 698rpx;
  height: 44rpx;
  margin: 24rpx 0 24rpx 24rpx;
}
.page .box_1 .list_1 .list-items_1 .section_2 .text_6 {
  width: 96rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 4rpx;
}
.page .box_1 .list_1 .list-items_1 .section_2 .text-wrapper_3 {
  width: 102rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.page .box_1 .list_1 .list-items_1 .section_2 .text-wrapper_3 .text_7 {
  width: 106rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.page .box_1 .list_1 .list-items_1 .section_2 .text-wrapper_3 .text_8 {
  width: 106rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(11, 206, 148);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.page .box_2 {
  background-color: rgb(62, 200, 174);
  height: 814rpx;
  width: 750rpx;
  position: absolute;
  left: 0;
  top: 0;
}
.page .box_2 .group_1 {
  width: 688rpx;
  height: 38rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .box_2 .group_1 .text_9 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
}
.page .box_2 .group_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin-left: 502rpx;
}
.page .box_2 .group_1 .thumbnail_3 {
  width: 36rpx;
  height: 36rpx;
  margin-left: 6rpx;
}
.page .box_2 .group_1 .thumbnail_4 {
  width: 38rpx;
  height: 38rpx;
  margin-left: 6rpx;
}
.page .box_2 .group_2 {
  width: 702rpx;
  height: 64rpx;
  margin: 34rpx 0 0 36rpx;
}
.page .box_2 .group_2 .thumbnail_5 {
  width: 18rpx;
  height: 34rpx;
  margin-top: 16rpx;
}
.page .box_2 .group_2 .text_10 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(0, 0, 0);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 10rpx 0 0 22rpx;
}
.page .box_2 .group_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin-left: 360rpx;
}
.page .box_2 .group_3 {
  width: 700rpx;
  height: 140rpx;
  margin: 36rpx 0 0 26rpx;
}
.page .box_2 .group_3 .text-wrapper_4 {
  width: 410rpx;
  height: 76rpx;
  margin-top: 32rpx;
}
.page .box_2 .group_3 .text-wrapper_4 .text_11 {
  width: 410rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 40rpx;
  font-family: Source Han Sans CN-Bold;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_3 .text-wrapper_4 .text_12 {
  width: 180rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 24rpx;
}
.page .box_2 .group_3 .image_2 {
  width: 140rpx;
  height: 140rpx;
}
.page .box_2 .group_4 {
  width: 608rpx;
  height: 28rpx;
  margin: 60rpx 0 400rpx 74rpx;
}
.page .box_2 .group_4 .text_13 {
  width: 112rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 4rpx;
}
.page .box_2 .group_4 .image-text_1 {
  width: 124rpx;
  height: 28rpx;
}
.page .box_2 .group_4 .image-text_1 .text-group_1 {
  width: 84rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 4rpx;
}
.page .box_2 .group_4 .image-text_1 .thumbnail_6 {
  width: 18rpx;
  height: 28rpx;
}
.page .box_2 .group_5 {
  background-image: -webkit-linear-gradient(top, rgba(62, 200, 174, 0.1) 0, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(180deg, rgba(62, 200, 174, 0.1) 0, rgba(255, 255, 255, 0) 100%);
  border-radius: 10px;
  height: 718rpx;
  width: 702rpx;
  position: absolute;
  left: 24rpx;
  top: 442rpx;
}
.page .box_2 .group_5 .block_1 {
  width: 178rpx;
  height: 40rpx;
  margin: 30rpx 0 0 26rpx;
}
.page .box_2 .group_5 .block_1 .image-text_2 {
  width: 178rpx;
  height: 40rpx;
}
.page .box_2 .group_5 .block_1 .image-text_2 .block_2 {
  background-color: rgb(11, 206, 148);
  width: 40rpx;
  height: 40rpx;
}
.page .box_2 .group_5 .block_1 .image-text_2 .text-group_2 {
  width: 128rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 32rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 8rpx;
}
.page .box_2 .group_5 .block_3 {
  width: 644rpx;
  height: 184rpx;
  margin: 26rpx 0 0 30rpx;
}
.page .box_2 .group_5 .block_3 .group_6 {
  background-image: -webkit-linear-gradient(top, rgb(215, 246, 239) 0, rgb(240, 250, 249) 100%);
  background-image: linear-gradient(180deg, rgb(215, 246, 239) 0, rgb(240, 250, 249) 100%);
  border-radius: 8px;
  width: 200rpx;
  height: 184rpx;
}
.page .box_2 .group_5 .block_3 .group_6 .image-text_3 {
  width: 120rpx;
  height: 18rpx;
  margin: 28rpx 0 0 24rpx;
}
.page .box_2 .group_5 .block_3 .group_6 .image-text_3 .text-group_3 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_3 .group_6 .image-text_3 .group_7 {
  background-color: rgb(34, 34, 34);
  width: 16rpx;
  height: 16rpx;
}
.page .box_2 .group_5 .block_3 .group_6 .text-wrapper_5 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 64rpx 24rpx;
}
.page .box_2 .group_5 .block_3 .group_6 .text-wrapper_5 .text_14 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_3 .group_6 .text-wrapper_5 .text_15 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_3 .group_8 {
  background-image: -webkit-linear-gradient(top, rgb(211, 234, 245) 0, rgb(235, 249, 247) 100%);
  background-image: linear-gradient(180deg, rgb(211, 234, 245) 0, rgb(235, 249, 247) 100%);
  border-radius: 8px;
  width: 200rpx;
  height: 184rpx;
  margin-left: 22rpx;
}
.page .box_2 .group_5 .block_3 .group_8 .image-text_4 {
  width: 120rpx;
  height: 18rpx;
  margin: 28rpx 0 0 22rpx;
}
.page .box_2 .group_5 .block_3 .group_8 .image-text_4 .text-group_4 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_3 .group_8 .image-text_4 .section_3 {
  background-color: rgb(34, 34, 34);
  width: 16rpx;
  height: 16rpx;
}
.page .box_2 .group_5 .block_3 .group_8 .text-wrapper_6 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 64rpx 22rpx;
}
.page .box_2 .group_5 .block_3 .group_8 .text-wrapper_6 .text_16 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_3 .group_8 .text-wrapper_6 .text_17 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_3 .group_9 {
  background-image: -webkit-linear-gradient(top, rgb(210, 224, 253) 0, rgb(239, 242, 251) 100%);
  background-image: linear-gradient(180deg, rgb(210, 224, 253) 0, rgb(239, 242, 251) 100%);
  border-radius: 8px;
  width: 200rpx;
  height: 184rpx;
  margin-left: 22rpx;
}
.page .box_2 .group_5 .block_3 .group_9 .image-text_5 {
  width: 120rpx;
  height: 18rpx;
  margin: 28rpx 0 0 26rpx;
}
.page .box_2 .group_5 .block_3 .group_9 .image-text_5 .text-group_5 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_3 .group_9 .image-text_5 .group_10 {
  background-color: rgb(34, 34, 34);
  width: 16rpx;
  height: 16rpx;
}
.page .box_2 .group_5 .block_3 .group_9 .text-wrapper_7 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 64rpx 26rpx;
}
.page .box_2 .group_5 .block_3 .group_9 .text-wrapper_7 .text_18 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_3 .group_9 .text-wrapper_7 .text_19 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_4 {
  width: 644rpx;
  height: 184rpx;
  margin: 16rpx 0 0 30rpx;
}
.page .box_2 .group_5 .block_4 .box_3 {
  background-image: -webkit-linear-gradient(top, rgb(216, 246, 239) 0, rgb(239, 250, 248) 100%);
  background-image: linear-gradient(180deg, rgb(216, 246, 239) 0, rgb(239, 250, 248) 100%);
  border-radius: 8px;
  width: 200rpx;
  height: 184rpx;
}
.page .box_2 .group_5 .block_4 .box_3 .image-text_6 {
  width: 120rpx;
  height: 18rpx;
  margin: 30rpx 0 0 24rpx;
}
.page .box_2 .group_5 .block_4 .box_3 .image-text_6 .text-group_6 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_4 .box_3 .image-text_6 .block_5 {
  background-color: rgb(34, 34, 34);
  width: 16rpx;
  height: 16rpx;
}
.page .box_2 .group_5 .block_4 .box_3 .text-wrapper_8 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 62rpx 24rpx;
}
.page .box_2 .group_5 .block_4 .box_3 .text-wrapper_8 .text_20 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_4 .box_3 .text-wrapper_8 .text_21 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_4 .box_4 {
  background-image: -webkit-linear-gradient(top, rgb(211, 234, 245) 0, rgb(235, 249, 247) 100%);
  background-image: linear-gradient(180deg, rgb(211, 234, 245) 0, rgb(235, 249, 247) 100%);
  border-radius: 8px;
  width: 200rpx;
  height: 184rpx;
  margin-left: 22rpx;
}
.page .box_2 .group_5 .block_4 .box_4 .image-text_7 {
  width: 120rpx;
  height: 18rpx;
  margin: 30rpx 0 0 22rpx;
}
.page .box_2 .group_5 .block_4 .box_4 .image-text_7 .text-group_7 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_4 .box_4 .image-text_7 .box_5 {
  background-color: rgb(34, 34, 34);
  width: 16rpx;
  height: 16rpx;
}
.page .box_2 .group_5 .block_4 .box_4 .text-wrapper_9 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 62rpx 22rpx;
}
.page .box_2 .group_5 .block_4 .box_4 .text-wrapper_9 .text_22 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_4 .box_4 .text-wrapper_9 .text_23 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_4 .box_6 {
  background-image: -webkit-linear-gradient(top, rgb(210, 224, 253) 0, rgb(239, 242, 251) 100%);
  background-image: linear-gradient(180deg, rgb(210, 224, 253) 0, rgb(239, 242, 251) 100%);
  border-radius: 8px;
  width: 200rpx;
  height: 184rpx;
  margin-left: 22rpx;
}
.page .box_2 .group_5 .block_4 .box_6 .image-text_8 {
  width: 108rpx;
  height: 18rpx;
  margin: 30rpx 0 0 38rpx;
}
.page .box_2 .group_5 .block_4 .box_6 .image-text_8 .text-group_8 {
  width: 72rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_4 .box_6 .image-text_8 .group_11 {
  background-color: rgb(34, 34, 34);
  width: 16rpx;
  height: 16rpx;
}
.page .box_2 .group_5 .block_4 .box_6 .text-wrapper_10 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 62rpx 26rpx;
}
.page .box_2 .group_5 .block_4 .box_6 .text-wrapper_10 .text_24 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_4 .box_6 .text-wrapper_10 .text_25 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_6 {
  width: 422rpx;
  height: 184rpx;
  margin: 20rpx 0 34rpx 30rpx;
}
.page .box_2 .group_5 .block_6 .group_12 {
  background-image: -webkit-linear-gradient(top, rgb(216, 246, 239) 0, rgb(239, 250, 248) 100%);
  background-image: linear-gradient(180deg, rgb(216, 246, 239) 0, rgb(239, 250, 248) 100%);
  border-radius: 8px;
  width: 200rpx;
  height: 184rpx;
}
.page .box_2 .group_5 .block_6 .group_12 .image-text_9 {
  width: 146rpx;
  height: 18rpx;
  margin: 30rpx 0 0 18rpx;
}
.page .box_2 .group_5 .block_6 .group_12 .image-text_9 .text-group_9 {
  width: 120rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_6 .group_12 .image-text_9 .box_7 {
  background-color: rgb(34, 34, 34);
  width: 16rpx;
  height: 16rpx;
}
.page .box_2 .group_5 .block_6 .group_12 .text-wrapper_11 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 62rpx 24rpx;
}
.page .box_2 .group_5 .block_6 .group_12 .text-wrapper_11 .text_26 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_6 .group_12 .text-wrapper_11 .text_27 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_6 .group_13 {
  background-image: -webkit-linear-gradient(top, rgb(211, 234, 245) 0, rgb(235, 249, 247) 100%);
  background-image: linear-gradient(180deg, rgb(211, 234, 245) 0, rgb(235, 249, 247) 100%);
  border-radius: 8px;
  width: 200rpx;
  height: 184rpx;
}
.page .box_2 .group_5 .block_6 .group_13 .image-text_10 {
  width: 120rpx;
  height: 18rpx;
  margin: 30rpx 0 0 22rpx;
}
.page .box_2 .group_5 .block_6 .group_13 .image-text_10 .text-group_10 {
  width: 96rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_6 .group_13 .image-text_10 .box_8 {
  background-color: rgb(34, 34, 34);
  width: 16rpx;
  height: 16rpx;
}
.page .box_2 .group_5 .block_6 .group_13 .text-wrapper_12 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 44rpx 0 62rpx 22rpx;
}
.page .box_2 .group_5 .block_6 .group_13 .text-wrapper_12 .text_28 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .box_2 .group_5 .block_6 .group_13 .text-wrapper_12 .text_29 {
  width: 112rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
