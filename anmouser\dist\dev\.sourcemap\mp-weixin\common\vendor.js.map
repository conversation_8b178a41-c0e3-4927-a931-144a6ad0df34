{"version": 3, "sources": ["uni-app:///node_modules/@dcloudio/uni-mp-weixin/dist/index.js", "uni-app:///node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js", "uni-app:///src/config/map-config.js", "uni-app:///node_modules/@dcloudio/uni-ui/lib/uni-icons/uniicons_file_vue.js", "uni-app:///node_modules/webpack/buildin/global.js", "uni-app:///node_modules/@dcloudio/vue-cli-plugin-uni/packages/mp-vue/dist/mp.runtime.esm.js", "uni-app:///node_modules/@dcloudio/uni-i18n/dist/uni-i18n.es.js"], "names": ["_vue", "_interopRequireDefault", "require", "_uniI18n", "e", "__esModule", "default", "ownKeys", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "l", "Symbol", "iterator", "n", "i", "u", "a", "f", "call", "next", "done", "value", "return", "Array", "isArray", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "_toPrimitive", "_typeof", "toPrimitive", "String", "Number", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "from", "test", "prototype", "realAtob", "b64", "b64re", "atob", "str", "replace", "Error", "bitmap", "result", "r1", "r2", "indexOf", "char<PERSON>t", "fromCharCode", "b64DecodeUnicode", "decodeURIComponent", "split", "map", "c", "charCodeAt", "join", "getCurrentUserInfo", "token", "wx", "getStorageSync", "tokenArr", "uid", "role", "permission", "tokenExpired", "userInfo", "JSON", "parse", "error", "message", "exp", "iat", "uniIdMixin", "<PERSON><PERSON>", "uniIDHasRole", "roleId", "_getCurrentUserInfo", "uniIDHasPermission", "permissionId", "_getCurrentUserInfo2", "uniIDTokenValid", "_getCurrentUserInfo3", "Date", "now", "_toString", "hasOwnProperty", "isFn", "fn", "isStr", "isPlainObject", "obj", "hasOwn", "key", "noop", "cached", "cache", "create", "cachedFn", "hit", "camelizeRE", "camelize", "_", "toUpperCase", "HOOKS", "globalInterceptors", "scopedInterceptors", "mergeHook", "parentVal", "childVal", "res", "concat", "dedupe<PERSON><PERSON>s", "hooks", "removeH<PERSON>", "hook", "index", "splice", "mergeInterceptorHook", "interceptor", "option", "removeInterceptorHook", "addInterceptor", "method", "removeInterceptor", "wrapperHook", "data", "isPromise", "then", "queue", "promise", "Promise", "resolve", "callback", "wrapperOptions", "options", "undefined", "oldCallback", "callbackInterceptor", "wrapperReturnValue", "returnValue", "returnValueHooks", "getApiInterceptorHooks", "scopedInterceptor", "invokeApi", "api", "_len", "params", "_key", "invoke", "promiseInterceptor", "reject", "SYNC_API_RE", "CONTEXT_API_RE", "CONTEXT_API_RE_EXC", "ASYNC_API", "CALLBACK_API_RE", "isContextApi", "isSyncApi", "isCallbackApi", "handlePromise", "catch", "err", "shouldPromise", "finally", "reason", "promisify", "promiseApi", "_len2", "_key2", "success", "fail", "complete", "assign", "EPS", "BASE_DEVICE_WIDTH", "isIOS", "deviceWidth", "deviceDPR", "checkDeviceWidth", "_wx$getSystemInfoSync", "getSystemInfoSync", "platform", "pixelRatio", "windowWidth", "upx2px", "number", "newDeviceWidth", "Math", "floor", "getLocale", "app", "getApp", "allowDefault", "$vm", "$locale", "language", "setLocale", "locale", "oldLocale", "onLocaleChangeCallbacks", "onLocaleChange", "global", "interceptors", "baseApi", "freeze", "__proto__", "findExistsPageIndex", "url", "pages", "getCurrentPages", "len", "page", "$page", "fullPath", "redirectTo", "fromArgs", "exists", "delta", "args", "existsPageIndex", "previewImage", "currentIndex", "parseInt", "current", "isNaN", "urls", "item", "indicator", "loop", "UUID_KEY", "deviceId", "addUuid", "random", "setStorage", "addSafeAreaInsets", "safeArea", "safeAreaInsets", "top", "left", "right", "bottom", "windowHeight", "getSystemInfo", "protocols", "todos", "canIUses", "CALLBACKS", "processCallback", "methodName", "processReturnValue", "processArgs", "argsOption", "keepFromArgs", "<PERSON><PERSON><PERSON><PERSON>", "keyOption", "console", "warn", "keepReturnValue", "wrapper", "protocol", "arg1", "arg2", "todo<PERSON><PERSON>", "TODOS", "createTodoApi", "todoApi", "_ref", "errMsg", "providers", "o<PERSON>h", "share", "payment", "get<PERSON><PERSON><PERSON>", "_ref2", "service", "provider", "extraApi", "getEmitter", "Emitter", "getUniEmitter", "ctx", "$on", "$off", "$once", "$emit", "eventApi", "MPPage", "Page", "MPComponent", "Component", "customizeRE", "customize", "initTriggerEvent", "mpInstance", "oldTriggerEvent", "triggerEvent", "event", "_len3", "_key3", "initHook", "isComponent", "oldHook", "_len4", "_key4", "__$wrappered", "after", "PAGE_EVENT_HOOKS", "initMocks", "vm", "mocks", "$mp", "mpType", "mock", "hasH<PERSON>", "vueOptions", "extendOptions", "super", "mixins", "find", "mixin", "initHooks", "mpOptions", "__call_hook", "initVueComponent", "VueComponent", "extend", "initSlots", "vueSlots", "$slots", "slotName", "$scopedSlots", "initVueIds", "vueIds", "_$vueId", "_$vuePid", "initData", "context", "methods", "process", "VUE_APP_DEBUG", "stringify", "__lifecycle_hooks__", "PROP_TYPES", "Boolean", "createObserver", "observer", "newVal", "oldVal", "initBehaviors", "init<PERSON>eh<PERSON>or", "vueBehaviors", "behaviors", "vueExtends", "extends", "vueMixins", "vueProps", "props", "behavior", "type", "properties", "initProperties", "vueMixin", "parsePropType", "defaultValue", "file", "is<PERSON>eh<PERSON>or", "vueId", "generic", "scopedSlotsCompiler", "setData", "opts", "wrapper$1", "mp", "stopPropagation", "preventDefault", "target", "detail", "markerId", "getExtraValue", "dataPathsArray", "dataPathArray", "dataPath", "prop<PERSON>ath", "valuePath", "vFor", "isInteger", "substr", "__get_value", "vForItem", "vForKey", "processEventExtra", "extra", "extraObj", "__args__", "getObjByArray", "arr", "element", "processEventArgs", "isCustom", "isCustomMPEvent", "currentTarget", "dataset", "comType", "ret", "arg", "ONCE", "CUSTOM", "isMatchEventType", "eventType", "optType", "getContextVm", "$parent", "$options", "$scope", "handleEvent", "_this", "eventOpts", "eventOpt", "eventsArray", "isOnce", "eventArray", "handlerCtx", "handler", "once", "messages", "initI18nMessages", "isEnableLocale", "localeKeys", "__uniConfig", "locales", "curMessages", "userMessages", "i18n", "initVueI18n", "i18nMixin", "beforeCreate", "_this2", "unwatch", "watchLocale", "$forceUpdate", "$$t", "values", "setLocale$1", "getLocale$1", "initAppLocale", "appVm", "state", "observable", "localeWatchers", "$watchLocale", "get", "set", "v", "watch", "eventChannels", "eventChannelStack", "getEventChannel", "id", "eventChannel", "shift", "initEventChannel", "getOpenerEventChannel", "callHook", "__id__", "__eventChannel__", "initScopedSlotsParams", "center", "parents", "$hasScopedSlotsParams", "has", "$getScopedSlotsParams", "object", "$setScopedSlotsParams", "propsData", "destroyed", "parseBaseApp", "_ref3", "initRefs", "store", "$store", "mpHost", "$i18n", "_i18n", "appOptions", "onLaunch", "canIUse", "globalData", "_isMounted", "findVmByVueId", "vuePid", "$children", "childVm", "parentVm", "Behavior", "isPage", "route", "initRelation", "selectAllComponents", "selector", "$refs", "components", "component", "ref", "vueGeneric", "scopedComponent", "forComponents", "handleLink", "_ref4", "parent", "parseApp", "createApp", "App", "encodeReserveRE", "encodeReserveReplacer", "commaRE", "encode", "encodeURIComponent", "stringifyQuery", "encodeStr", "val", "val2", "x", "parseBaseComponent", "vueComponentOptions", "_ref5", "_initVueComponent", "_initVueComponent2", "multipleSlots", "addGlobalClass", "componentOptions", "__file", "lifetimes", "attached", "$mount", "ready", "detached", "$destroy", "pageLifetimes", "show", "hide", "resize", "size", "__l", "__e", "externalClasses", "wxsCallMethods", "callMethod", "parseComponent", "hooks$1", "parseBasePage", "vuePageOptions", "_ref6", "pageOptions", "onLoad", "query", "copyQuery", "is", "parsePage", "createPage", "createComponent", "createSubpackageApp", "onShow", "onAppShow", "_len5", "_key5", "onHide", "onAppHide", "_len6", "_key6", "getLaunchOptionsSync", "createPlugin", "_len7", "_key7", "_len8", "_key8", "canIUseApi", "apiName", "uni", "Proxy", "uni$1", "_default", "exports", "MAP_CONFIG", "TENCENT", "KEY", "GEOCODER_URL", "AMAP", "BAIDU", "isApiKeyConfigured", "config", "startsWith", "getAvailableProviders", "isObject", "defaultDelimiters", "BaseFormatter", "<PERSON><PERSON><PERSON>", "_classCallCheck", "_caches", "_createClass", "interpolate", "delimiters", "tokens", "compile", "RE_TOKEN_LIST_VALUE", "RE_TOKEN_NAMED_VALUE", "format", "startDelimiter", "endDelimiter", "position", "text", "char", "sub", "isClosed", "compiled", "mode", "LOCALE_ZH_HANS", "LOCALE_ZH_HANT", "LOCALE_EN", "LOCALE_FR", "LOCALE_ES", "defaultFormatter", "include", "parts", "part", "normalizeLocale", "trim", "toLowerCase", "lang", "I18n", "fallback<PERSON><PERSON><PERSON>", "watcher", "formater", "watchers", "add", "override", "watchAppLocale", "newLocale", "$watch", "getDefaultLocale", "isWatchedAppLocale", "isString", "hasI18nJson", "jsonObj", "walkJsonObj", "isI18nStr", "parseI18nJson", "compileStr", "compileI18nJsonStr", "jsonStr", "localeValues", "unshift", "compileJsonObj", "compileValue", "valueLocales", "localValue", "walk", "resolveLocale", "resolveLocaleChain", "chain", "pop"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,mBAAA;AACA,IAAAC,QAAA,GAAAD,mBAAA;AAAiD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,QAAAH,CAAA,EAAAI,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAP,CAAA,OAAAM,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAR,CAAA,GAAAI,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAX,CAAA,EAAAI,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAf,CAAA,aAAAI,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAD,OAAA,CAAAG,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,IAAAe,eAAA,CAAAnB,CAAA,EAAAI,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAc,yBAAA,GAAAd,MAAA,CAAAe,gBAAA,CAAArB,CAAA,EAAAM,MAAA,CAAAc,yBAAA,CAAAf,CAAA,KAAAF,OAAA,CAAAG,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAgB,cAAA,CAAAtB,CAAA,EAAAI,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAJ,CAAA;AAAA,SAAAuB,eAAAnB,CAAA,EAAAJ,CAAA,WAAAwB,eAAA,CAAApB,CAAA,KAAAqB,qBAAA,CAAArB,CAAA,EAAAJ,CAAA,KAAA0B,2BAAA,CAAAtB,CAAA,EAAAJ,CAAA,KAAA2B,gBAAA;AAAA,SAAAA,iBAAA,cAAAC,SAAA;AAAA,SAAAH,sBAAArB,CAAA,EAAAyB,CAAA,QAAAxB,CAAA,WAAAD,CAAA,gCAAA0B,MAAA,IAAA1B,CAAA,CAAA0B,MAAA,CAAAC,QAAA,KAAA3B,CAAA,4BAAAC,CAAA,QAAAL,CAAA,EAAAgC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,OAAAC,CAAA,OAAA3B,CAAA,iBAAAwB,CAAA,IAAA5B,CAAA,GAAAA,CAAA,CAAAgC,IAAA,CAAAjC,CAAA,GAAAkC,IAAA,QAAAT,CAAA,QAAAvB,MAAA,CAAAD,CAAA,MAAAA,CAAA,UAAA+B,CAAA,uBAAAA,CAAA,IAAApC,CAAA,GAAAiC,CAAA,CAAAI,IAAA,CAAAhC,CAAA,GAAAkC,IAAA,MAAAJ,CAAA,CAAAtB,IAAA,CAAAb,CAAA,CAAAwC,KAAA,GAAAL,CAAA,CAAAlB,MAAA,KAAAY,CAAA,GAAAO,CAAA,iBAAAhC,CAAA,IAAAK,CAAA,OAAAuB,CAAA,GAAA5B,CAAA,yBAAAgC,CAAA,YAAA/B,CAAA,CAAAoC,MAAA,KAAAP,CAAA,GAAA7B,CAAA,CAAAoC,MAAA,IAAAnC,MAAA,CAAA4B,CAAA,MAAAA,CAAA,2BAAAzB,CAAA,QAAAuB,CAAA,aAAAG,CAAA;AAAA,SAAAX,gBAAApB,CAAA,QAAAsC,KAAA,CAAAC,OAAA,CAAAvC,CAAA,UAAAA,CAAA;AAAA,SAAAe,gBAAAnB,CAAA,EAAAI,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAwC,cAAA,CAAAxC,CAAA,MAAAJ,CAAA,GAAAM,MAAA,CAAAgB,cAAA,CAAAtB,CAAA,EAAAI,CAAA,IAAAoC,KAAA,EAAAnC,CAAA,EAAAO,UAAA,MAAAiC,YAAA,MAAAC,QAAA,UAAA9C,CAAA,CAAAI,CAAA,IAAAC,CAAA,EAAAL,CAAA;AAAA,SAAA4C,eAAAvC,CAAA,QAAA4B,CAAA,GAAAc,YAAA,CAAA1C,CAAA,gCAAA2C,OAAA,CAAAf,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAc,aAAA1C,CAAA,EAAAD,CAAA,oBAAA4C,OAAA,CAAA3C,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAL,CAAA,GAAAK,CAAA,CAAAyB,MAAA,CAAAmB,WAAA,kBAAAjD,CAAA,QAAAiC,CAAA,GAAAjC,CAAA,CAAAqC,IAAA,CAAAhC,CAAA,EAAAD,CAAA,gCAAA4C,OAAA,CAAAf,CAAA,UAAAA,CAAA,YAAAL,SAAA,yEAAAxB,CAAA,GAAA8C,MAAA,GAAAC,MAAA,EAAA9C,CAAA;AAAA,SAAA+C,mBAAAhD,CAAA,WAAAiD,kBAAA,CAAAjD,CAAA,KAAAkD,gBAAA,CAAAlD,CAAA,KAAAsB,2BAAA,CAAAtB,CAAA,KAAAmD,kBAAA;AAAA,SAAAA,mBAAA,cAAA3B,SAAA;AAAA,SAAAF,4BAAAtB,CAAA,EAAA+B,CAAA,QAAA/B,CAAA,2BAAAA,CAAA,SAAAoD,iBAAA,CAAApD,CAAA,EAAA+B,CAAA,OAAA9B,CAAA,MAAAoD,QAAA,CAAApB,IAAA,CAAAjC,CAAA,EAAAsD,KAAA,6BAAArD,CAAA,IAAAD,CAAA,CAAAuD,WAAA,KAAAtD,CAAA,GAAAD,CAAA,CAAAuD,WAAA,CAAAC,IAAA,aAAAvD,CAAA,cAAAA,CAAA,GAAAqC,KAAA,CAAAmB,IAAA,CAAAzD,CAAA,oBAAAC,CAAA,+CAAAyD,IAAA,CAAAzD,CAAA,IAAAmD,iBAAA,CAAApD,CAAA,EAAA+B,CAAA;AAAA,SAAAmB,iBAAAlD,CAAA,8BAAA0B,MAAA,YAAA1B,CAAA,CAAA0B,MAAA,CAAAC,QAAA,aAAA3B,CAAA,uBAAAsC,KAAA,CAAAmB,IAAA,CAAAzD,CAAA;AAAA,SAAAiD,mBAAAjD,CAAA,QAAAsC,KAAA,CAAAC,OAAA,CAAAvC,CAAA,UAAAoD,iBAAA,CAAApD,CAAA;AAAA,SAAAoD,kBAAApD,CAAA,EAAA+B,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAA/B,CAAA,CAAAa,MAAA,MAAAkB,CAAA,GAAA/B,CAAA,CAAAa,MAAA,YAAAjB,CAAA,MAAAgC,CAAA,GAAAU,KAAA,CAAAP,CAAA,GAAAnC,CAAA,GAAAmC,CAAA,EAAAnC,CAAA,IAAAgC,CAAA,CAAAhC,CAAA,IAAAI,CAAA,CAAAJ,CAAA,UAAAgC,CAAA;AAAA,SAAAgB,QAAAvC,CAAA,sCAAAuC,OAAA,wBAAAlB,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAtB,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAqB,MAAA,IAAArB,CAAA,CAAAkD,WAAA,KAAA7B,MAAA,IAAArB,CAAA,KAAAqB,MAAA,CAAAiC,SAAA,qBAAAtD,CAAA,KAAAuC,OAAA,CAAAvC,CAAA;AAEjD,IAAIuD,QAAQ;AAEZ,IAAMC,GAAG,GAAG,mEAAmE;AAC/E,IAAMC,KAAK,GAAG,sEAAsE;AAEpF,IAAI,OAAOC,IAAI,KAAK,UAAU,EAAE;EAC9BH,QAAQ,GAAG,SAAXA,QAAQA,CAAaI,GAAG,EAAE;IACxBA,GAAG,GAAGlB,MAAM,CAACkB,GAAG,CAAC,CAACC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IAC9C,IAAI,CAACH,KAAK,CAACJ,IAAI,CAACM,GAAG,CAAC,EAAE;MAAE,MAAM,IAAIE,KAAK,CAAC,0FAA0F,CAAC;IAAC;;IAEpI;IACAF,GAAG,IAAI,IAAI,CAACV,KAAK,CAAC,CAAC,IAAIU,GAAG,CAACnD,MAAM,GAAG,CAAC,CAAC,CAAC;IACvC,IAAIsD,MAAM;IAAE,IAAIC,MAAM,GAAG,EAAE;IAAE,IAAIC,EAAE;IAAE,IAAIC,EAAE;IAAE,IAAIzC,CAAC,GAAG,CAAC;IACtD,OAAOA,CAAC,GAAGmC,GAAG,CAACnD,MAAM,GAAG;MACtBsD,MAAM,GAAGN,GAAG,CAACU,OAAO,CAACP,GAAG,CAACQ,MAAM,CAAC3C,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAAGgC,GAAG,CAACU,OAAO,CAACP,GAAG,CAACQ,MAAM,CAAC3C,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAClE,CAACwC,EAAE,GAAGR,GAAG,CAACU,OAAO,CAACP,GAAG,CAACQ,MAAM,CAAC3C,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAIyC,EAAE,GAAGT,GAAG,CAACU,OAAO,CAACP,GAAG,CAACQ,MAAM,CAAC3C,CAAC,EAAE,CAAC,CAAC,CAAC;MAE5FuC,MAAM,IAAIC,EAAE,KAAK,EAAE,GAAGvB,MAAM,CAAC2B,YAAY,CAACN,MAAM,IAAI,EAAE,GAAG,GAAG,CAAC,GACzDG,EAAE,KAAK,EAAE,GAAGxB,MAAM,CAAC2B,YAAY,CAACN,MAAM,IAAI,EAAE,GAAG,GAAG,EAAEA,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,GACpErB,MAAM,CAAC2B,YAAY,CAACN,MAAM,IAAI,EAAE,GAAG,GAAG,EAAEA,MAAM,IAAI,CAAC,GAAG,GAAG,EAAEA,MAAM,GAAG,GAAG,CAAC;IAChF;IACA,OAAOC,MAAM;EACf,CAAC;AACH,CAAC,MAAM;EACL;EACAR,QAAQ,GAAGG,IAAI;AACjB;AAEA,SAASW,gBAAgBA,CAAEV,GAAG,EAAE;EAC9B,OAAOW,kBAAkB,CAACf,QAAQ,CAACI,GAAG,CAAC,CAACY,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;IACjE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC1B,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,CAAC0B,IAAI,CAAC,EAAE,CAAC,CAAC;AACd;AAEA,SAASC,kBAAkBA,CAAA,EAAI;EAC7B,IAAMC,KAAK,GAAKC,EAAE,CAAEC,cAAc,CAAC,cAAc,CAAC,IAAI,EAAE;EACxD,IAAMC,QAAQ,GAAGH,KAAK,CAACN,KAAK,CAAC,GAAG,CAAC;EACjC,IAAI,CAACM,KAAK,IAAIG,QAAQ,CAACxE,MAAM,KAAK,CAAC,EAAE;IACnC,OAAO;MACLyE,GAAG,EAAE,IAAI;MACTC,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE;IAChB,CAAC;EACH;EACA,IAAIC,QAAQ;EACZ,IAAI;IACFA,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAClB,gBAAgB,CAACW,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACd,MAAM,IAAI3B,KAAK,CAAC,qBAAqB,GAAG2B,KAAK,CAACC,OAAO,CAAC;EACxD;EACAJ,QAAQ,CAACD,YAAY,GAAGC,QAAQ,CAACK,GAAG,GAAG,IAAI;EAC3C,OAAOL,QAAQ,CAACK,GAAG;EACnB,OAAOL,QAAQ,CAACM,GAAG;EACnB,OAAON,QAAQ;AACjB;AAEA,SAASO,UAAUA,CAAEC,GAAG,EAAE;EACxBA,GAAG,CAACvC,SAAS,CAACwC,YAAY,GAAG,UAAUC,MAAM,EAAE;IAC7C,IAAAC,mBAAA,GAEIpB,kBAAkB,CAAC,CAAC;MADtBM,IAAI,GAAAc,mBAAA,CAAJd,IAAI;IAEN,OAAOA,IAAI,CAAChB,OAAO,CAAC6B,MAAM,CAAC,GAAG,CAAC,CAAC;EAClC,CAAC;EACDF,GAAG,CAACvC,SAAS,CAAC2C,kBAAkB,GAAG,UAAUC,YAAY,EAAE;IACzD,IAAAC,oBAAA,GAEIvB,kBAAkB,CAAC,CAAC;MADtBO,UAAU,GAAAgB,oBAAA,CAAVhB,UAAU;IAEZ,OAAO,IAAI,CAACW,YAAY,CAAC,OAAO,CAAC,IAAIX,UAAU,CAACjB,OAAO,CAACgC,YAAY,CAAC,GAAG,CAAC,CAAC;EAC5E,CAAC;EACDL,GAAG,CAACvC,SAAS,CAAC8C,eAAe,GAAG,YAAY;IAC1C,IAAAC,oBAAA,GAEIzB,kBAAkB,CAAC,CAAC;MADtBQ,YAAY,GAAAiB,oBAAA,CAAZjB,YAAY;IAEd,OAAOA,YAAY,GAAGkB,IAAI,CAACC,GAAG,CAAC,CAAC;EAClC,CAAC;AACH;AAEA,IAAMC,SAAS,GAAG3G,MAAM,CAACyD,SAAS,CAACN,QAAQ;AAC3C,IAAMyD,cAAc,GAAG5G,MAAM,CAACyD,SAAS,CAACmD,cAAc;AAEtD,SAASC,IAAIA,CAAEC,EAAE,EAAE;EACjB,OAAO,OAAOA,EAAE,KAAK,UAAU;AACjC;AAEA,SAASC,KAAKA,CAAEjD,GAAG,EAAE;EACnB,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAChC;AAEA,SAASkD,aAAaA,CAAEC,GAAG,EAAE;EAC3B,OAAON,SAAS,CAAC5E,IAAI,CAACkF,GAAG,CAAC,KAAK,iBAAiB;AAClD;AAEA,SAASC,MAAMA,CAAED,GAAG,EAAEE,GAAG,EAAE;EACzB,OAAOP,cAAc,CAAC7E,IAAI,CAACkF,GAAG,EAAEE,GAAG,CAAC;AACtC;AAEA,SAASC,IAAIA,CAAA,EAAI,CAAC;;AAElB;AACA;AACA;AACA,SAASC,MAAMA,CAAEP,EAAE,EAAE;EACnB,IAAMQ,KAAK,GAAGtH,MAAM,CAACuH,MAAM,CAAC,IAAI,CAAC;EACjC,OAAO,SAASC,QAAQA,CAAE1D,GAAG,EAAE;IAC7B,IAAM2D,GAAG,GAAGH,KAAK,CAACxD,GAAG,CAAC;IACtB,OAAO2D,GAAG,KAAKH,KAAK,CAACxD,GAAG,CAAC,GAAGgD,EAAE,CAAChD,GAAG,CAAC,CAAC;EACtC,CAAC;AACH;;AAEA;AACA;AACA;AACA,IAAM4D,UAAU,GAAG,QAAQ;AAC3B,IAAMC,QAAQ,GAAGN,MAAM,CAAC,UAACvD,GAAG,EAAK;EAC/B,OAAOA,GAAG,CAACC,OAAO,CAAC2D,UAAU,EAAE,UAACE,CAAC,EAAEhD,CAAC;IAAA,OAAKA,CAAC,GAAGA,CAAC,CAACiD,WAAW,CAAC,CAAC,GAAG,EAAE;EAAA,EAAC;AACpE,CAAC,CAAC;AAEF,IAAMC,KAAK,GAAG,CACZ,QAAQ,EACR,SAAS,EACT,MAAM,EACN,UAAU,EACV,aAAa,CACd;AAED,IAAMC,kBAAkB,GAAG,CAAC,CAAC;AAC7B,IAAMC,kBAAkB,GAAG,CAAC,CAAC;AAE7B,SAASC,SAASA,CAAEC,SAAS,EAAEC,QAAQ,EAAE;EACvC,IAAMC,GAAG,GAAGD,QAAQ,GAChBD,SAAS,GACPA,SAAS,CAACG,MAAM,CAACF,QAAQ,CAAC,GAC1B/F,KAAK,CAACC,OAAO,CAAC8F,QAAQ,CAAC,GACrBA,QAAQ,GAAG,CAACA,QAAQ,CAAC,GACzBD,SAAS;EACb,OAAOE,GAAG,GACNE,WAAW,CAACF,GAAG,CAAC,GAChBA,GAAG;AACT;AAEA,SAASE,WAAWA,CAAEC,KAAK,EAAE;EAC3B,IAAMH,GAAG,GAAG,EAAE;EACd,KAAK,IAAIzG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4G,KAAK,CAAC5H,MAAM,EAAEgB,CAAC,EAAE,EAAE;IACrC,IAAIyG,GAAG,CAAC/D,OAAO,CAACkE,KAAK,CAAC5G,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MAChCyG,GAAG,CAAC7H,IAAI,CAACgI,KAAK,CAAC5G,CAAC,CAAC,CAAC;IACpB;EACF;EACA,OAAOyG,GAAG;AACZ;AAEA,SAASI,UAAUA,CAAED,KAAK,EAAEE,IAAI,EAAE;EAChC,IAAMC,KAAK,GAAGH,KAAK,CAAClE,OAAO,CAACoE,IAAI,CAAC;EACjC,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBH,KAAK,CAACI,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;EACxB;AACF;AAEA,SAASE,oBAAoBA,CAAEC,WAAW,EAAEC,MAAM,EAAE;EAClD9I,MAAM,CAACC,IAAI,CAAC6I,MAAM,CAAC,CAAClI,OAAO,CAAC,UAAA6H,IAAI,EAAI;IAClC,IAAIX,KAAK,CAACzD,OAAO,CAACoE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI5B,IAAI,CAACiC,MAAM,CAACL,IAAI,CAAC,CAAC,EAAE;MACpDI,WAAW,CAACJ,IAAI,CAAC,GAAGR,SAAS,CAACY,WAAW,CAACJ,IAAI,CAAC,EAAEK,MAAM,CAACL,IAAI,CAAC,CAAC;IAChE;EACF,CAAC,CAAC;AACJ;AAEA,SAASM,qBAAqBA,CAAEF,WAAW,EAAEC,MAAM,EAAE;EACnD,IAAI,CAACD,WAAW,IAAI,CAACC,MAAM,EAAE;IAC3B;EACF;EACA9I,MAAM,CAACC,IAAI,CAAC6I,MAAM,CAAC,CAAClI,OAAO,CAAC,UAAA6H,IAAI,EAAI;IAClC,IAAIX,KAAK,CAACzD,OAAO,CAACoE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI5B,IAAI,CAACiC,MAAM,CAACL,IAAI,CAAC,CAAC,EAAE;MACpDD,UAAU,CAACK,WAAW,CAACJ,IAAI,CAAC,EAAEK,MAAM,CAACL,IAAI,CAAC,CAAC;IAC7C;EACF,CAAC,CAAC;AACJ;AAEA,SAASO,cAAcA,CAAEC,MAAM,EAAEH,MAAM,EAAE;EACvC,IAAI,OAAOG,MAAM,KAAK,QAAQ,IAAIjC,aAAa,CAAC8B,MAAM,CAAC,EAAE;IACvDF,oBAAoB,CAACZ,kBAAkB,CAACiB,MAAM,CAAC,KAAKjB,kBAAkB,CAACiB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;EAC/F,CAAC,MAAM,IAAI9B,aAAa,CAACiC,MAAM,CAAC,EAAE;IAChCL,oBAAoB,CAACb,kBAAkB,EAAEkB,MAAM,CAAC;EAClD;AACF;AAEA,SAASC,iBAAiBA,CAAED,MAAM,EAAEH,MAAM,EAAE;EAC1C,IAAI,OAAOG,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAIjC,aAAa,CAAC8B,MAAM,CAAC,EAAE;MACzBC,qBAAqB,CAACf,kBAAkB,CAACiB,MAAM,CAAC,EAAEH,MAAM,CAAC;IAC3D,CAAC,MAAM;MACL,OAAOd,kBAAkB,CAACiB,MAAM,CAAC;IACnC;EACF,CAAC,MAAM,IAAIjC,aAAa,CAACiC,MAAM,CAAC,EAAE;IAChCF,qBAAqB,CAAChB,kBAAkB,EAAEkB,MAAM,CAAC;EACnD;AACF;AAEA,SAASE,WAAWA,CAAEV,IAAI,EAAE;EAC1B,OAAO,UAAUW,IAAI,EAAE;IACrB,OAAOX,IAAI,CAACW,IAAI,CAAC,IAAIA,IAAI;EAC3B,CAAC;AACH;AAEA,SAASC,SAASA,CAAEpC,GAAG,EAAE;EACvB,OAAO,CAAC,CAACA,GAAG,KAAKvE,OAAA,CAAOuE,GAAG,MAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAI,OAAOA,GAAG,CAACqC,IAAI,KAAK,UAAU;AAC1G;AAEA,SAASC,KAAKA,CAAEhB,KAAK,EAAEa,IAAI,EAAE;EAC3B,IAAII,OAAO,GAAG,KAAK;EACnB,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4G,KAAK,CAAC5H,MAAM,EAAEgB,CAAC,EAAE,EAAE;IACrC,IAAM8G,IAAI,GAAGF,KAAK,CAAC5G,CAAC,CAAC;IACrB,IAAI6H,OAAO,EAAE;MACXA,OAAO,GAAGC,OAAO,CAACC,OAAO,CAACP,WAAW,CAACV,IAAI,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL,IAAML,GAAG,GAAGK,IAAI,CAACW,IAAI,CAAC;MACtB,IAAIC,SAAS,CAACjB,GAAG,CAAC,EAAE;QAClBoB,OAAO,GAAGC,OAAO,CAACC,OAAO,CAACtB,GAAG,CAAC;MAChC;MACA,IAAIA,GAAG,KAAK,KAAK,EAAE;QACjB,OAAO;UACLkB,IAAI,WAAJA,IAAIA,CAAA,EAAI,CAAE;QACZ,CAAC;MACH;IACF;EACF;EACA,OAAOE,OAAO,IAAI;IAChBF,IAAI,WAAJA,IAAIA,CAAEK,QAAQ,EAAE;MACd,OAAOA,QAAQ,CAACP,IAAI,CAAC;IACvB;EACF,CAAC;AACH;AAEA,SAASQ,cAAcA,CAAEf,WAAW,EAAgB;EAAA,IAAdgB,OAAO,GAAAnJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,CAAC,CAAC;EAChD,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,CAACE,OAAO,CAAC,UAAA0C,IAAI,EAAI;IAC9C,IAAIlB,KAAK,CAACC,OAAO,CAACwG,WAAW,CAACvF,IAAI,CAAC,CAAC,EAAE;MACpC,IAAMyG,WAAW,GAAGF,OAAO,CAACvG,IAAI,CAAC;MACjCuG,OAAO,CAACvG,IAAI,CAAC,GAAG,SAAS0G,mBAAmBA,CAAE5B,GAAG,EAAE;QACjDmB,KAAK,CAACV,WAAW,CAACvF,IAAI,CAAC,EAAE8E,GAAG,CAAC,CAACkB,IAAI,CAAC,UAAClB,GAAG,EAAK;UAC1C;UACA,OAAOvB,IAAI,CAACkD,WAAW,CAAC,IAAIA,WAAW,CAAC3B,GAAG,CAAC,IAAIA,GAAG;QACrD,CAAC,CAAC;MACJ,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAOyB,OAAO;AAChB;AAEA,SAASI,kBAAkBA,CAAEhB,MAAM,EAAEiB,WAAW,EAAE;EAChD,IAAMC,gBAAgB,GAAG,EAAE;EAC3B,IAAI/H,KAAK,CAACC,OAAO,CAAC0F,kBAAkB,CAACmC,WAAW,CAAC,EAAE;IACjDC,gBAAgB,CAAC5J,IAAI,CAAAC,KAAA,CAArB2J,gBAAgB,EAAArH,kBAAA,CAASiF,kBAAkB,CAACmC,WAAW,EAAC;EAC1D;EACA,IAAMrB,WAAW,GAAGb,kBAAkB,CAACiB,MAAM,CAAC;EAC9C,IAAIJ,WAAW,IAAIzG,KAAK,CAACC,OAAO,CAACwG,WAAW,CAACqB,WAAW,CAAC,EAAE;IACzDC,gBAAgB,CAAC5J,IAAI,CAAAC,KAAA,CAArB2J,gBAAgB,EAAArH,kBAAA,CAAS+F,WAAW,CAACqB,WAAW,EAAC;EACnD;EACAC,gBAAgB,CAACvJ,OAAO,CAAC,UAAA6H,IAAI,EAAI;IAC/ByB,WAAW,GAAGzB,IAAI,CAACyB,WAAW,CAAC,IAAIA,WAAW;EAChD,CAAC,CAAC;EACF,OAAOA,WAAW;AACpB;AAEA,SAASE,sBAAsBA,CAAEnB,MAAM,EAAE;EACvC,IAAMJ,WAAW,GAAG7I,MAAM,CAACuH,MAAM,CAAC,IAAI,CAAC;EACvCvH,MAAM,CAACC,IAAI,CAAC8H,kBAAkB,CAAC,CAACnH,OAAO,CAAC,UAAA6H,IAAI,EAAI;IAC9C,IAAIA,IAAI,KAAK,aAAa,EAAE;MAC1BI,WAAW,CAACJ,IAAI,CAAC,GAAGV,kBAAkB,CAACU,IAAI,CAAC,CAACrF,KAAK,CAAC,CAAC;IACtD;EACF,CAAC,CAAC;EACF,IAAMiH,iBAAiB,GAAGrC,kBAAkB,CAACiB,MAAM,CAAC;EACpD,IAAIoB,iBAAiB,EAAE;IACrBrK,MAAM,CAACC,IAAI,CAACoK,iBAAiB,CAAC,CAACzJ,OAAO,CAAC,UAAA6H,IAAI,EAAI;MAC7C,IAAIA,IAAI,KAAK,aAAa,EAAE;QAC1BI,WAAW,CAACJ,IAAI,CAAC,GAAG,CAACI,WAAW,CAACJ,IAAI,CAAC,IAAI,EAAE,EAAEJ,MAAM,CAACgC,iBAAiB,CAAC5B,IAAI,CAAC,CAAC;MAC/E;IACF,CAAC,CAAC;EACJ;EACA,OAAOI,WAAW;AACpB;AAEA,SAASyB,SAASA,CAAErB,MAAM,EAAEsB,GAAG,EAAEV,OAAO,EAAa;EAAA,SAAAW,IAAA,GAAA9J,SAAA,CAAAC,MAAA,EAAR8J,MAAM,OAAArI,KAAA,CAAAoI,IAAA,OAAAA,IAAA,WAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;IAAND,MAAM,CAAAC,IAAA,QAAAhK,SAAA,CAAAgK,IAAA;EAAA;EACjD,IAAM7B,WAAW,GAAGuB,sBAAsB,CAACnB,MAAM,CAAC;EAClD,IAAIJ,WAAW,IAAI7I,MAAM,CAACC,IAAI,CAAC4I,WAAW,CAAC,CAAClI,MAAM,EAAE;IAClD,IAAIyB,KAAK,CAACC,OAAO,CAACwG,WAAW,CAAC8B,MAAM,CAAC,EAAE;MACrC,IAAMvC,GAAG,GAAGmB,KAAK,CAACV,WAAW,CAAC8B,MAAM,EAAEd,OAAO,CAAC;MAC9C,OAAOzB,GAAG,CAACkB,IAAI,CAAC,UAACO,OAAO,EAAK;QAC3B,OAAOU,GAAG,CAAA/J,KAAA,UAACoJ,cAAc,CAACf,WAAW,EAAEgB,OAAO,CAAC,EAAAxB,MAAA,CAAKoC,MAAM,EAAC;MAC7D,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOF,GAAG,CAAA/J,KAAA,UAACoJ,cAAc,CAACf,WAAW,EAAEgB,OAAO,CAAC,EAAAxB,MAAA,CAAKoC,MAAM,EAAC;IAC7D;EACF;EACA,OAAOF,GAAG,CAAA/J,KAAA,UAACqJ,OAAO,EAAAxB,MAAA,CAAKoC,MAAM,EAAC;AAChC;AAEA,IAAMG,kBAAkB,GAAG;EACzBV,WAAW,WAAXA,WAAWA,CAAE9B,GAAG,EAAE;IAChB,IAAI,CAACiB,SAAS,CAACjB,GAAG,CAAC,EAAE;MACnB,OAAOA,GAAG;IACZ;IACA,OAAO,IAAIqB,OAAO,CAAC,UAACC,OAAO,EAAEmB,MAAM,EAAK;MACtCzC,GAAG,CAACkB,IAAI,CAAC,UAAAlB,GAAG,EAAI;QACd,IAAIA,GAAG,CAAC,CAAC,CAAC,EAAE;UACVyC,MAAM,CAACzC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,MAAM;UACLsB,OAAO,CAACtB,GAAG,CAAC,CAAC,CAAC,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC;AAED,IAAM0C,WAAW,GACf,8RAA8R;AAEhS,IAAMC,cAAc,GAAG,kBAAkB;;AAEzC;AACA,IAAMC,kBAAkB,GAAG,CAAC,qBAAqB,CAAC;;AAElD;AACA,IAAMC,SAAS,GAAG,CAAC,qBAAqB,CAAC;AAEzC,IAAMC,eAAe,GAAG,UAAU;AAElC,SAASC,YAAYA,CAAE7H,IAAI,EAAE;EAC3B,OAAOyH,cAAc,CAACvH,IAAI,CAACF,IAAI,CAAC,IAAI0H,kBAAkB,CAAC3G,OAAO,CAACf,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7E;AACA,SAAS8H,SAASA,CAAE9H,IAAI,EAAE;EACxB,OAAOwH,WAAW,CAACtH,IAAI,CAACF,IAAI,CAAC,IAAI2H,SAAS,CAAC5G,OAAO,CAACf,IAAI,CAAC,KAAK,CAAC,CAAC;AACjE;AAEA,SAAS+H,aAAaA,CAAE/H,IAAI,EAAE;EAC5B,OAAO4H,eAAe,CAAC1H,IAAI,CAACF,IAAI,CAAC,IAAIA,IAAI,KAAK,QAAQ;AACxD;AAEA,SAASgI,aAAaA,CAAE9B,OAAO,EAAE;EAC/B,OAAOA,OAAO,CAACF,IAAI,CAAC,UAAAF,IAAI,EAAI;IAC1B,OAAO,CAAC,IAAI,EAAEA,IAAI,CAAC;EACrB,CAAC,CAAC,CACCmC,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAI,CAACA,GAAG,CAAC;EAAA,EAAC;AACxB;AAEA,SAASC,aAAaA,CAAEnI,IAAI,EAAE;EAC5B,IACE6H,YAAY,CAAC7H,IAAI,CAAC,IAClB8H,SAAS,CAAC9H,IAAI,CAAC,IACf+H,aAAa,CAAC/H,IAAI,CAAC,EACnB;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;;AAEA;AACA,IAAI,CAACmG,OAAO,CAAChG,SAAS,CAACiI,OAAO,EAAE;EAC9BjC,OAAO,CAAChG,SAAS,CAACiI,OAAO,GAAG,UAAU/B,QAAQ,EAAE;IAC9C,IAAMH,OAAO,GAAG,IAAI,CAACnG,WAAW;IAChC,OAAO,IAAI,CAACiG,IAAI,CACd,UAAApH,KAAK;MAAA,OAAIsH,OAAO,CAACE,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACL,IAAI,CAAC;QAAA,OAAMpH,KAAK;MAAA,EAAC;IAAA,GACtD,UAAAyJ,MAAM;MAAA,OAAInC,OAAO,CAACE,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACL,IAAI,CAAC,YAAM;QAC/C,MAAMqC,MAAM;MACd,CAAC,CAAC;IAAA,CACJ,CAAC;EACH,CAAC;AACH;AAEA,SAASC,SAASA,CAAEtI,IAAI,EAAEiH,GAAG,EAAE;EAC7B,IAAI,CAACkB,aAAa,CAACnI,IAAI,CAAC,EAAE;IACxB,OAAOiH,GAAG;EACZ;EACA,OAAO,SAASsB,UAAUA,CAAA,EAA2B;IAAA,IAAzBhC,OAAO,GAAAnJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,CAAC,CAAC;IAAA,SAAAoL,KAAA,GAAApL,SAAA,CAAAC,MAAA,EAAK8J,MAAM,OAAArI,KAAA,CAAA0J,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAANtB,MAAM,CAAAsB,KAAA,QAAArL,SAAA,CAAAqL,KAAA;IAAA;IACjD,IAAIlF,IAAI,CAACgD,OAAO,CAACmC,OAAO,CAAC,IAAInF,IAAI,CAACgD,OAAO,CAACoC,IAAI,CAAC,IAAIpF,IAAI,CAACgD,OAAO,CAACqC,QAAQ,CAAC,EAAE;MACzE,OAAOjC,kBAAkB,CAAC3G,IAAI,EAAEgH,SAAS,CAAA9J,KAAA,UAAC8C,IAAI,EAAEiH,GAAG,EAAEV,OAAO,EAAAxB,MAAA,CAAKoC,MAAM,EAAC,CAAC;IAC3E;IACA,OAAOR,kBAAkB,CAAC3G,IAAI,EAAEgI,aAAa,CAAC,IAAI7B,OAAO,CAAC,UAACC,OAAO,EAAEmB,MAAM,EAAK;MAC7EP,SAAS,CAAA9J,KAAA,UAAC8C,IAAI,EAAEiH,GAAG,EAAEvK,MAAM,CAACmM,MAAM,CAAC,CAAC,CAAC,EAAEtC,OAAO,EAAE;QAC9CmC,OAAO,EAAEtC,OAAO;QAChBuC,IAAI,EAAEpB;MACR,CAAC,CAAC,EAAAxC,MAAA,CAAKoC,MAAM,EAAC;IAChB,CAAC,CAAC,CAAC,CAAC;EACN,CAAC;AACH;AAEA,IAAM2B,GAAG,GAAG,IAAI;AAChB,IAAMC,iBAAiB,GAAG,GAAG;AAC7B,IAAIC,KAAK,GAAG,KAAK;AACjB,IAAIC,WAAW,GAAG,CAAC;AACnB,IAAIC,SAAS,GAAG,CAAC;AAEjB,SAASC,gBAAgBA,CAAA,EAAI;EAC3B,IAAAC,qBAAA,GAIIzH,EAAE,CAAC0H,iBAAiB,CAAC,CAAC;IAHxBC,QAAQ,GAAAF,qBAAA,CAARE,QAAQ;IACRC,UAAU,GAAAH,qBAAA,CAAVG,UAAU;IACVC,WAAW,GAAAJ,qBAAA,CAAXI,WAAW,CACc,CAAC;;EAE5BP,WAAW,GAAGO,WAAW;EACzBN,SAAS,GAAGK,UAAU;EACtBP,KAAK,GAAGM,QAAQ,KAAK,KAAK;AAC5B;AAEA,SAASG,MAAMA,CAAEC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIV,WAAW,KAAK,CAAC,EAAE;IACrBE,gBAAgB,CAAC,CAAC;EACpB;EAEAO,MAAM,GAAGnK,MAAM,CAACmK,MAAM,CAAC;EACvB,IAAIA,MAAM,KAAK,CAAC,EAAE;IAChB,OAAO,CAAC;EACV;EACA,IAAI9I,MAAM,GAAI8I,MAAM,GAAGX,iBAAiB,IAAKY,cAAc,IAAIV,WAAW,CAAC;EAC3E,IAAIrI,MAAM,GAAG,CAAC,EAAE;IACdA,MAAM,GAAG,CAACA,MAAM;EAClB;EACAA,MAAM,GAAGgJ,IAAI,CAACC,KAAK,CAACjJ,MAAM,GAAGkI,GAAG,CAAC;EACjC,IAAIlI,MAAM,KAAK,CAAC,EAAE;IAChB,IAAIsI,SAAS,KAAK,CAAC,IAAI,CAACF,KAAK,EAAE;MAC7BpI,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM;MACLA,MAAM,GAAG,GAAG;IACd;EACF;EACA,OAAO8I,MAAM,GAAG,CAAC,GAAG,CAAC9I,MAAM,GAAGA,MAAM;AACtC;AAEA,SAASkJ,SAASA,CAAA,EAAI;EACpB;EACA,IAAMC,GAAG,GAAGC,MAAM,CAAC;IACjBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,IAAIF,GAAG,IAAIA,GAAG,CAACG,GAAG,EAAE;IAClB,OAAOH,GAAG,CAACG,GAAG,CAACC,OAAO;EACxB;EACA,OAAOxI,EAAE,CAAC0H,iBAAiB,CAAC,CAAC,CAACe,QAAQ,IAAI,SAAS;AACrD;AAEA,SAASC,SAASA,CAAEC,MAAM,EAAE;EAC1B,IAAMP,GAAG,GAAGC,MAAM,CAAC,CAAC;EACpB,IAAI,CAACD,GAAG,EAAE;IACR,OAAO,KAAK;EACd;EACA,IAAMQ,SAAS,GAAGR,GAAG,CAACG,GAAG,CAACC,OAAO;EACjC,IAAII,SAAS,KAAKD,MAAM,EAAE;IACxBP,GAAG,CAACG,GAAG,CAACC,OAAO,GAAGG,MAAM;IACxBE,uBAAuB,CAAClN,OAAO,CAAC,UAACkG,EAAE;MAAA,OAAKA,EAAE,CAAC;QACzC8G,MAAM,EAANA;MACF,CAAC,CAAC;IAAA,EAAC;IACH,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAEA,IAAME,uBAAuB,GAAG,EAAE;AAClC,SAASC,cAAcA,CAAEjH,EAAE,EAAE;EAC3B,IAAIgH,uBAAuB,CAACzJ,OAAO,CAACyC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;IAC9CgH,uBAAuB,CAACvN,IAAI,CAACuG,EAAE,CAAC;EAClC;AACF;AAEA,IAAI,OAAOkH,MAAM,KAAK,WAAW,EAAE;EACjCA,MAAM,CAACZ,SAAS,GAAGA,SAAS;AAC9B;AAEA,IAAMa,YAAY,GAAG;EACnBrD,kBAAkB,EAAlBA;AACF,CAAC;AAED,IAAIsD,OAAO,GAAG,aAAalO,MAAM,CAACmO,MAAM,CAAC;EACvCC,SAAS,EAAE,IAAI;EACfrB,MAAM,EAAEA,MAAM;EACdK,SAAS,EAAEA,SAAS;EACpBO,SAAS,EAAEA,SAAS;EACpBI,cAAc,EAAEA,cAAc;EAC9B/E,cAAc,EAAEA,cAAc;EAC9BE,iBAAiB,EAAEA,iBAAiB;EACpC+E,YAAY,EAAEA;AAChB,CAAC,CAAC;AAEF,SAASI,mBAAmBA,CAAEC,GAAG,EAAE;EACjC,IAAMC,KAAK,GAAGC,eAAe,CAAC,CAAC;EAC/B,IAAIC,GAAG,GAAGF,KAAK,CAAC5N,MAAM;EACtB,OAAO8N,GAAG,EAAE,EAAE;IACZ,IAAMC,IAAI,GAAGH,KAAK,CAACE,GAAG,CAAC;IACvB,IAAIC,IAAI,CAACC,KAAK,IAAID,IAAI,CAACC,KAAK,CAACC,QAAQ,KAAKN,GAAG,EAAE;MAC7C,OAAOG,GAAG;IACZ;EACF;EACA,OAAO,CAAC,CAAC;AACX;AAEA,IAAII,UAAU,GAAG;EACfvL,IAAI,WAAJA,IAAIA,CAAEwL,QAAQ,EAAE;IACd,IAAIA,QAAQ,CAACC,MAAM,KAAK,MAAM,IAAID,QAAQ,CAACE,KAAK,EAAE;MAChD,OAAO,cAAc;IACvB;IACA,OAAO,YAAY;EACrB,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAEH,QAAQ,EAAE;IACd,IAAIA,QAAQ,CAACC,MAAM,KAAK,MAAM,IAAID,QAAQ,CAACR,GAAG,EAAE;MAC9C,IAAMY,eAAe,GAAGb,mBAAmB,CAACS,QAAQ,CAACR,GAAG,CAAC;MACzD,IAAIY,eAAe,KAAK,CAAC,CAAC,EAAE;QAC1B,IAAMF,KAAK,GAAGR,eAAe,CAAC,CAAC,CAAC7N,MAAM,GAAG,CAAC,GAAGuO,eAAe;QAC5D,IAAIF,KAAK,GAAG,CAAC,EAAE;UACbF,QAAQ,CAACE,KAAK,GAAGA,KAAK;QACxB;MACF;IACF;EACF;AACF,CAAC;AAED,IAAIG,YAAY,GAAG;EACjBF,IAAI,WAAJA,IAAIA,CAAEH,QAAQ,EAAE;IACd,IAAIM,YAAY,GAAGC,QAAQ,CAACP,QAAQ,CAACQ,OAAO,CAAC;IAC7C,IAAIC,KAAK,CAACH,YAAY,CAAC,EAAE;MACvB;IACF;IACA,IAAMI,IAAI,GAAGV,QAAQ,CAACU,IAAI;IAC1B,IAAI,CAACpN,KAAK,CAACC,OAAO,CAACmN,IAAI,CAAC,EAAE;MACxB;IACF;IACA,IAAMf,GAAG,GAAGe,IAAI,CAAC7O,MAAM;IACvB,IAAI,CAAC8N,GAAG,EAAE;MACR;IACF;IACA,IAAIW,YAAY,GAAG,CAAC,EAAE;MACpBA,YAAY,GAAG,CAAC;IAClB,CAAC,MAAM,IAAIA,YAAY,IAAIX,GAAG,EAAE;MAC9BW,YAAY,GAAGX,GAAG,GAAG,CAAC;IACxB;IACA,IAAIW,YAAY,GAAG,CAAC,EAAE;MACpBN,QAAQ,CAACQ,OAAO,GAAGE,IAAI,CAACJ,YAAY,CAAC;MACrCN,QAAQ,CAACU,IAAI,GAAGA,IAAI,CAACpP,MAAM,CACzB,UAACqP,IAAI,EAAE/G,KAAK;QAAA,OAAKA,KAAK,GAAG0G,YAAY,GAAGK,IAAI,KAAKD,IAAI,CAACJ,YAAY,CAAC,GAAG,IAAI;MAAA,CAC5E,CAAC;IACH,CAAC,MAAM;MACLN,QAAQ,CAACQ,OAAO,GAAGE,IAAI,CAAC,CAAC,CAAC;IAC5B;IACA,OAAO;MACLE,SAAS,EAAE,KAAK;MAChBC,IAAI,EAAE;IACR,CAAC;EACH;AACF,CAAC;AAED,IAAMC,QAAQ,GAAG,gBAAgB;AACjC,IAAIC,QAAQ;AACZ,SAASC,OAAOA,CAAE5L,MAAM,EAAE;EACxB2L,QAAQ,GAAGA,QAAQ,IAAI5K,EAAE,CAACC,cAAc,CAAC0K,QAAQ,CAAC;EAClD,IAAI,CAACC,QAAQ,EAAE;IACbA,QAAQ,GAAGpJ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGwG,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC6C,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;IAC5D9K,EAAE,CAAC+K,UAAU,CAAC;MACZ7I,GAAG,EAAEyI,QAAQ;MACbxG,IAAI,EAAEyG;IACR,CAAC,CAAC;EACJ;EACA3L,MAAM,CAAC2L,QAAQ,GAAGA,QAAQ;AAC5B;AAEA,SAASI,iBAAiBA,CAAE/L,MAAM,EAAE;EAClC,IAAIA,MAAM,CAACgM,QAAQ,EAAE;IACnB,IAAMA,QAAQ,GAAGhM,MAAM,CAACgM,QAAQ;IAChChM,MAAM,CAACiM,cAAc,GAAG;MACtBC,GAAG,EAAEF,QAAQ,CAACE,GAAG;MACjBC,IAAI,EAAEH,QAAQ,CAACG,IAAI;MACnBC,KAAK,EAAEpM,MAAM,CAAC4I,WAAW,GAAGoD,QAAQ,CAACI,KAAK;MAC1CC,MAAM,EAAErM,MAAM,CAACsM,YAAY,GAAGN,QAAQ,CAACK;IACzC,CAAC;EACH;AACF;AAEA,IAAIE,aAAa,GAAG;EAClBvG,WAAW,EAAE,SAAbA,WAAWA,CAAYhG,MAAM,EAAE;IAC7B4L,OAAO,CAAC5L,MAAM,CAAC;IACf+L,iBAAiB,CAAC/L,MAAM,CAAC;EAC3B;AACF,CAAC;;AAED;;AAEA,IAAMwM,SAAS,GAAG;EAChB7B,UAAU,EAAVA,UAAU;EACV;EACAM,YAAY,EAAZA,YAAY;EACZsB,aAAa,EAAbA,aAAa;EACb9D,iBAAiB,EAAE8D;AACrB,CAAC;AACD,IAAME,KAAK,GAAG,CACZ,SAAS,EACT,aAAa,EACb,eAAe,EACf,gBAAgB,CACjB;AACD,IAAMC,QAAQ,GAAG,EAAE;AAEnB,IAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC;AAE3D,SAASC,eAAeA,CAAEC,UAAU,EAAE9H,MAAM,EAAEiB,WAAW,EAAE;EACzD,OAAO,UAAU9B,GAAG,EAAE;IACpB,OAAOa,MAAM,CAAC+H,kBAAkB,CAACD,UAAU,EAAE3I,GAAG,EAAE8B,WAAW,CAAC,CAAC;EACjE,CAAC;AACH;AAEA,SAAS+G,WAAWA,CAAEF,UAAU,EAAEjC,QAAQ,EAA2D;EAAA,IAAzDoC,UAAU,GAAAxQ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEwJ,WAAW,GAAAxJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEyQ,YAAY,GAAAzQ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,KAAK;EACjG,IAAIsG,aAAa,CAAC8H,QAAQ,CAAC,EAAE;IAAE;IAC7B,IAAMsC,MAAM,GAAGD,YAAY,KAAK,IAAI,GAAGrC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;IACtD,IAAIjI,IAAI,CAACqK,UAAU,CAAC,EAAE;MACpBA,UAAU,GAAGA,UAAU,CAACpC,QAAQ,EAAEsC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjD;IACA,KAAK,IAAMjK,GAAG,IAAI2H,QAAQ,EAAE;MAC1B,IAAI5H,MAAM,CAACgK,UAAU,EAAE/J,GAAG,CAAC,EAAE;QAC3B,IAAIkK,SAAS,GAAGH,UAAU,CAAC/J,GAAG,CAAC;QAC/B,IAAIN,IAAI,CAACwK,SAAS,CAAC,EAAE;UACnBA,SAAS,GAAGA,SAAS,CAACvC,QAAQ,CAAC3H,GAAG,CAAC,EAAE2H,QAAQ,EAAEsC,MAAM,CAAC;QACxD;QACA,IAAI,CAACC,SAAS,EAAE;UAAE;UAChBC,OAAO,CAACC,IAAI,SAAAlJ,MAAA,CAAS0I,UAAU,qFAAA1I,MAAA,CAAyDlB,GAAG,MAAG,CAAC;QACjG,CAAC,MAAM,IAAIJ,KAAK,CAACsK,SAAS,CAAC,EAAE;UAAE;UAC7BD,MAAM,CAACC,SAAS,CAAC,GAAGvC,QAAQ,CAAC3H,GAAG,CAAC;QACnC,CAAC,MAAM,IAAIH,aAAa,CAACqK,SAAS,CAAC,EAAE;UAAE;UACrCD,MAAM,CAACC,SAAS,CAAC/N,IAAI,GAAG+N,SAAS,CAAC/N,IAAI,GAAG6D,GAAG,CAAC,GAAGkK,SAAS,CAACnP,KAAK;QACjE;MACF,CAAC,MAAM,IAAI2O,SAAS,CAACxM,OAAO,CAAC8C,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACxC,IAAIN,IAAI,CAACiI,QAAQ,CAAC3H,GAAG,CAAC,CAAC,EAAE;UACvBiK,MAAM,CAACjK,GAAG,CAAC,GAAG2J,eAAe,CAACC,UAAU,EAAEjC,QAAQ,CAAC3H,GAAG,CAAC,EAAE+C,WAAW,CAAC;QACvE;MACF,CAAC,MAAM;QACL,IAAI,CAACiH,YAAY,EAAE;UACjBC,MAAM,CAACjK,GAAG,CAAC,GAAG2H,QAAQ,CAAC3H,GAAG,CAAC;QAC7B;MACF;IACF;IACA,OAAOiK,MAAM;EACf,CAAC,MAAM,IAAIvK,IAAI,CAACiI,QAAQ,CAAC,EAAE;IACzBA,QAAQ,GAAGgC,eAAe,CAACC,UAAU,EAAEjC,QAAQ,EAAE5E,WAAW,CAAC;EAC/D;EACA,OAAO4E,QAAQ;AACjB;AAEA,SAASkC,kBAAkBA,CAAED,UAAU,EAAE3I,GAAG,EAAE8B,WAAW,EAA2B;EAAA,IAAzBsH,eAAe,GAAA9Q,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,KAAK;EAChF,IAAImG,IAAI,CAAC6J,SAAS,CAACxG,WAAW,CAAC,EAAE;IAAE;IACjC9B,GAAG,GAAGsI,SAAS,CAACxG,WAAW,CAAC6G,UAAU,EAAE3I,GAAG,CAAC;EAC9C;EACA,OAAO6I,WAAW,CAACF,UAAU,EAAE3I,GAAG,EAAE8B,WAAW,EAAE,CAAC,CAAC,EAAEsH,eAAe,CAAC;AACvE;AAEA,SAASC,OAAOA,CAAEV,UAAU,EAAE9H,MAAM,EAAE;EACpC,IAAI/B,MAAM,CAACwJ,SAAS,EAAEK,UAAU,CAAC,EAAE;IACjC,IAAMW,QAAQ,GAAGhB,SAAS,CAACK,UAAU,CAAC;IACtC,IAAI,CAACW,QAAQ,EAAE;MAAE;MACf,OAAO,YAAY;QACjBJ,OAAO,CAAC3L,KAAK,gEAAA0C,MAAA,CAAuC0I,UAAU,OAAI,CAAC;MACrE,CAAC;IACH;IACA,OAAO,UAAUY,IAAI,EAAEC,IAAI,EAAE;MAAE;MAC7B,IAAI/H,OAAO,GAAG6H,QAAQ;MACtB,IAAI7K,IAAI,CAAC6K,QAAQ,CAAC,EAAE;QAClB7H,OAAO,GAAG6H,QAAQ,CAACC,IAAI,CAAC;MAC1B;MAEAA,IAAI,GAAGV,WAAW,CAACF,UAAU,EAAEY,IAAI,EAAE9H,OAAO,CAACoF,IAAI,EAAEpF,OAAO,CAACK,WAAW,CAAC;MAEvE,IAAM+E,IAAI,GAAG,CAAC0C,IAAI,CAAC;MACnB,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;QAC/B3C,IAAI,CAAC1O,IAAI,CAACqR,IAAI,CAAC;MACjB;MACA,IAAI/K,IAAI,CAACgD,OAAO,CAACvG,IAAI,CAAC,EAAE;QACtByN,UAAU,GAAGlH,OAAO,CAACvG,IAAI,CAACqO,IAAI,CAAC;MACjC,CAAC,MAAM,IAAI5K,KAAK,CAAC8C,OAAO,CAACvG,IAAI,CAAC,EAAE;QAC9ByN,UAAU,GAAGlH,OAAO,CAACvG,IAAI;MAC3B;MACA,IAAM4G,WAAW,GAAGjF,EAAE,CAAC8L,UAAU,CAAC,CAACvQ,KAAK,CAACyE,EAAE,EAAEgK,IAAI,CAAC;MAClD,IAAI7D,SAAS,CAAC2F,UAAU,CAAC,EAAE;QAAE;QAC3B,OAAOC,kBAAkB,CAACD,UAAU,EAAE7G,WAAW,EAAEL,OAAO,CAACK,WAAW,EAAEiB,YAAY,CAAC4F,UAAU,CAAC,CAAC;MACnG;MACA,OAAO7G,WAAW;IACpB,CAAC;EACH;EACA,OAAOjB,MAAM;AACf;AAEA,IAAM4I,QAAQ,GAAG7R,MAAM,CAACuH,MAAM,CAAC,IAAI,CAAC;AAEpC,IAAMuK,KAAK,GAAG,CACZ,sBAAsB,EACtB,eAAe,EACf,iBAAiB,EACjB,QAAQ,EACR,SAAS,EACT,OAAO,CACR;AAED,SAASC,aAAaA,CAAEzO,IAAI,EAAE;EAC5B,OAAO,SAAS0O,OAAOA,CAAAC,IAAA,EAGpB;IAAA,IAFDhG,IAAI,GAAAgG,IAAA,CAAJhG,IAAI;MACJC,QAAQ,GAAA+F,IAAA,CAAR/F,QAAQ;IAER,IAAM9D,GAAG,GAAG;MACV8J,MAAM,KAAA7J,MAAA,CAAK/E,IAAI,oBAAA+E,MAAA,CAAiB/E,IAAI;IACtC,CAAC;IACDuD,IAAI,CAACoF,IAAI,CAAC,IAAIA,IAAI,CAAC7D,GAAG,CAAC;IACvBvB,IAAI,CAACqF,QAAQ,CAAC,IAAIA,QAAQ,CAAC9D,GAAG,CAAC;EACjC,CAAC;AACH;AAEA0J,KAAK,CAAClR,OAAO,CAAC,UAAU0C,IAAI,EAAE;EAC5BuO,QAAQ,CAACvO,IAAI,CAAC,GAAGyO,aAAa,CAACzO,IAAI,CAAC;AACtC,CAAC,CAAC;AAEF,IAAI6O,SAAS,GAAG;EACdC,KAAK,EAAE,CAAC,QAAQ,CAAC;EACjBC,KAAK,EAAE,CAAC,QAAQ,CAAC;EACjBC,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB/R,IAAI,EAAE,CAAC,QAAQ;AACjB,CAAC;AAED,SAASgS,WAAWA,CAAAC,KAAA,EAKjB;EAAA,IAJDC,OAAO,GAAAD,KAAA,CAAPC,OAAO;IACPzG,OAAO,GAAAwG,KAAA,CAAPxG,OAAO;IACPC,IAAI,GAAAuG,KAAA,CAAJvG,IAAI;IACJC,QAAQ,GAAAsG,KAAA,CAARtG,QAAQ;EAER,IAAI9D,GAAG,GAAG,KAAK;EACf,IAAI+J,SAAS,CAACM,OAAO,CAAC,EAAE;IACtBrK,GAAG,GAAG;MACJ8J,MAAM,EAAE,gBAAgB;MACxBO,OAAO,EAAPA,OAAO;MACPC,QAAQ,EAAEP,SAAS,CAACM,OAAO;IAC7B,CAAC;IACD5L,IAAI,CAACmF,OAAO,CAAC,IAAIA,OAAO,CAAC5D,GAAG,CAAC;EAC/B,CAAC,MAAM;IACLA,GAAG,GAAG;MACJ8J,MAAM,EAAE;IACV,CAAC;IACDrL,IAAI,CAACoF,IAAI,CAAC,IAAIA,IAAI,CAAC7D,GAAG,CAAC;EACzB;EACAvB,IAAI,CAACqF,QAAQ,CAAC,IAAIA,QAAQ,CAAC9D,GAAG,CAAC;AACjC;AAEA,IAAIuK,QAAQ,GAAG,aAAa3S,MAAM,CAACmO,MAAM,CAAC;EACxCC,SAAS,EAAE,IAAI;EACfmE,WAAW,EAAEA;AACf,CAAC,CAAC;AAEF,IAAMK,UAAU,GAAI,YAAY;EAC9B,IAAIC,OAAO;EACX,OAAO,SAASC,aAAaA,CAAA,EAAI;IAC/B,IAAI,CAACD,OAAO,EAAE;MACZA,OAAO,GAAG,IAAI7M,YAAG,CAAC,CAAC;IACrB;IACA,OAAO6M,OAAO;EAChB,CAAC;AACH,CAAC,CAAE,CAAC;AAEJ,SAASrS,KAAKA,CAAEuS,GAAG,EAAE9J,MAAM,EAAEgG,IAAI,EAAE;EACjC,OAAO8D,GAAG,CAAC9J,MAAM,CAAC,CAACzI,KAAK,CAACuS,GAAG,EAAE9D,IAAI,CAAC;AACrC;AAEA,SAAS+D,GAAGA,CAAA,EAAI;EACd,OAAOxS,KAAK,CAACoS,UAAU,CAAC,CAAC,EAAE,KAAK,EAAAxQ,KAAA,CAAAqB,SAAA,CAAAL,KAAA,CAAArB,IAAA,CAAMrB,SAAS,CAAC,CAAC;AACnD;AACA,SAASuS,IAAIA,CAAA,EAAI;EACf,OAAOzS,KAAK,CAACoS,UAAU,CAAC,CAAC,EAAE,MAAM,EAAAxQ,KAAA,CAAAqB,SAAA,CAAAL,KAAA,CAAArB,IAAA,CAAMrB,SAAS,CAAC,CAAC;AACpD;AACA,SAASwS,KAAKA,CAAA,EAAI;EAChB,OAAO1S,KAAK,CAACoS,UAAU,CAAC,CAAC,EAAE,OAAO,EAAAxQ,KAAA,CAAAqB,SAAA,CAAAL,KAAA,CAAArB,IAAA,CAAMrB,SAAS,CAAC,CAAC;AACrD;AACA,SAASyS,KAAKA,CAAA,EAAI;EAChB,OAAO3S,KAAK,CAACoS,UAAU,CAAC,CAAC,EAAE,OAAO,EAAAxQ,KAAA,CAAAqB,SAAA,CAAAL,KAAA,CAAArB,IAAA,CAAMrB,SAAS,CAAC,CAAC;AACrD;AAEA,IAAI0S,QAAQ,GAAG,aAAapT,MAAM,CAACmO,MAAM,CAAC;EACxCC,SAAS,EAAE,IAAI;EACf4E,GAAG,EAAEA,GAAG;EACRC,IAAI,EAAEA,IAAI;EACVC,KAAK,EAAEA,KAAK;EACZC,KAAK,EAAEA;AACT,CAAC,CAAC;AAEF,IAAI5I,GAAG,GAAG,aAAavK,MAAM,CAACmO,MAAM,CAAC;EACnCC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,IAAMiF,MAAM,GAAGC,IAAI;AACnB,IAAMC,WAAW,GAAGC,SAAS;AAE7B,IAAMC,WAAW,GAAG,IAAI;AAExB,IAAMC,SAAS,GAAGrM,MAAM,CAAC,UAACvD,GAAG,EAAK;EAChC,OAAO6D,QAAQ,CAAC7D,GAAG,CAACC,OAAO,CAAC0P,WAAW,EAAE,GAAG,CAAC,CAAC;AAChD,CAAC,CAAC;AAEF,SAASE,gBAAgBA,CAAEC,UAAU,EAAE;EACrC,IAAMC,eAAe,GAAGD,UAAU,CAACE,YAAY;EAC/CF,UAAU,CAACE,YAAY,GAAG,UAAUC,KAAK,EAAW;IAAA,SAAAC,KAAA,GAAAtT,SAAA,CAAAC,MAAA,EAANsO,IAAI,OAAA7M,KAAA,CAAA4R,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJhF,IAAI,CAAAgF,KAAA,QAAAvT,SAAA,CAAAuT,KAAA;IAAA;IAChD,OAAOJ,eAAe,CAACrT,KAAK,CAACoT,UAAU,GAAGF,SAAS,CAACK,KAAK,CAAC,EAAA1L,MAAA,CAAK4G,IAAI,CAAC,CAAC;EACvE,CAAC;AACH;AAEA,SAASiF,QAAQA,CAAE5Q,IAAI,EAAEuG,OAAO,EAAEsK,WAAW,EAAE;EAC7C,IAAMC,OAAO,GAAGvK,OAAO,CAACvG,IAAI,CAAC;EAC7B,IAAI,CAAC8Q,OAAO,EAAE;IACZvK,OAAO,CAACvG,IAAI,CAAC,GAAG,YAAY;MAC1BqQ,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC;EACH,CAAC,MAAM;IACL9J,OAAO,CAACvG,IAAI,CAAC,GAAG,YAAmB;MACjCqQ,gBAAgB,CAAC,IAAI,CAAC;MAAC,SAAAU,KAAA,GAAA3T,SAAA,CAAAC,MAAA,EADIsO,IAAI,OAAA7M,KAAA,CAAAiS,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJrF,IAAI,CAAAqF,KAAA,IAAA5T,SAAA,CAAA4T,KAAA;MAAA;MAE/B,OAAOF,OAAO,CAAC5T,KAAK,CAAC,IAAI,EAAEyO,IAAI,CAAC;IAClC,CAAC;EACH;AACF;AACA,IAAI,CAACoE,MAAM,CAACkB,YAAY,EAAE;EACxBlB,MAAM,CAACkB,YAAY,GAAG,IAAI;EAC1BjB,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAA2B;IAAA,IAAdzJ,OAAO,GAAAnJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,CAAC,CAAC;IAC3BwT,QAAQ,CAAC,QAAQ,EAAErK,OAAO,CAAC;IAC3B,OAAOwJ,MAAM,CAACxJ,OAAO,CAAC;EACxB,CAAC;EACDyJ,IAAI,CAACkB,KAAK,GAAGnB,MAAM,CAACmB,KAAK;EAEzBhB,SAAS,GAAG,SAAZA,SAASA,CAAA,EAA2B;IAAA,IAAd3J,OAAO,GAAAnJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,CAAC,CAAC;IAChCwT,QAAQ,CAAC,SAAS,EAAErK,OAAO,CAAC;IAC5B,OAAO0J,WAAW,CAAC1J,OAAO,CAAC;EAC7B,CAAC;AACH;AAEA,IAAM4K,gBAAgB,GAAG,CACvB,mBAAmB,EACnB,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,mBAAmB,EACnB,cAAc,EACd,UAAU,EACV,cAAc,CACf;AAED,SAASC,SAASA,CAAEC,EAAE,EAAEC,KAAK,EAAE;EAC7B,IAAMhB,UAAU,GAAGe,EAAE,CAACE,GAAG,CAACF,EAAE,CAACG,MAAM,CAAC;EACpCF,KAAK,CAAChU,OAAO,CAAC,UAAAmU,IAAI,EAAI;IACpB,IAAI7N,MAAM,CAAC0M,UAAU,EAAEmB,IAAI,CAAC,EAAE;MAC5BJ,EAAE,CAACI,IAAI,CAAC,GAAGnB,UAAU,CAACmB,IAAI,CAAC;IAC7B;EACF,CAAC,CAAC;AACJ;AAEA,SAASC,OAAOA,CAAEvM,IAAI,EAAEwM,UAAU,EAAE;EAClC,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,IAAIjP,YAAG,CAAC6D,OAAO,IAAIzH,KAAK,CAACC,OAAO,CAAC2D,YAAG,CAAC6D,OAAO,CAACpB,IAAI,CAAC,CAAC,EAAE;IACnD,OAAO,IAAI;EACb;EAEAwM,UAAU,GAAGA,UAAU,CAACrV,OAAO,IAAIqV,UAAU;EAE7C,IAAIpO,IAAI,CAACoO,UAAU,CAAC,EAAE;IACpB,IAAIpO,IAAI,CAACoO,UAAU,CAACC,aAAa,CAACzM,IAAI,CAAC,CAAC,EAAE;MACxC,OAAO,IAAI;IACb;IACA,IAAIwM,UAAU,CAACE,KAAK,IAClBF,UAAU,CAACE,KAAK,CAACtL,OAAO,IACxBzH,KAAK,CAACC,OAAO,CAAC4S,UAAU,CAACE,KAAK,CAACtL,OAAO,CAACpB,IAAI,CAAC,CAAC,EAAE;MAC/C,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEA,IAAI5B,IAAI,CAACoO,UAAU,CAACxM,IAAI,CAAC,CAAC,EAAE;IAC1B,OAAO,IAAI;EACb;EACA,IAAM2M,MAAM,GAAGH,UAAU,CAACG,MAAM;EAChC,IAAIhT,KAAK,CAACC,OAAO,CAAC+S,MAAM,CAAC,EAAE;IACzB,OAAO,CAAC,CAACA,MAAM,CAACC,IAAI,CAAC,UAAAC,KAAK;MAAA,OAAIN,OAAO,CAACvM,IAAI,EAAE6M,KAAK,CAAC;IAAA,EAAC;EACrD;AACF;AAEA,SAASC,SAASA,CAAEC,SAAS,EAAEjN,KAAK,EAAE0M,UAAU,EAAE;EAChD1M,KAAK,CAAC3H,OAAO,CAAC,UAAA6H,IAAI,EAAI;IACpB,IAAIuM,OAAO,CAACvM,IAAI,EAAEwM,UAAU,CAAC,EAAE;MAC7BO,SAAS,CAAC/M,IAAI,CAAC,GAAG,UAAUwG,IAAI,EAAE;QAChC,OAAO,IAAI,CAACzB,GAAG,IAAI,IAAI,CAACA,GAAG,CAACiI,WAAW,CAAChN,IAAI,EAAEwG,IAAI,CAAC;MACrD,CAAC;IACH;EACF,CAAC,CAAC;AACJ;AAEA,SAASyG,gBAAgBA,CAAE1P,GAAG,EAAEiP,UAAU,EAAE;EAC1CA,UAAU,GAAGA,UAAU,CAACrV,OAAO,IAAIqV,UAAU;EAC7C,IAAIU,YAAY;EAChB,IAAI9O,IAAI,CAACoO,UAAU,CAAC,EAAE;IACpBU,YAAY,GAAGV,UAAU;EAC3B,CAAC,MAAM;IACLU,YAAY,GAAG3P,GAAG,CAAC4P,MAAM,CAACX,UAAU,CAAC;EACvC;EACAA,UAAU,GAAGU,YAAY,CAAC9L,OAAO;EACjC,OAAO,CAAC8L,YAAY,EAAEV,UAAU,CAAC;AACnC;AAEA,SAASY,SAASA,CAAElB,EAAE,EAAEmB,QAAQ,EAAE;EAChC,IAAI1T,KAAK,CAACC,OAAO,CAACyT,QAAQ,CAAC,IAAIA,QAAQ,CAACnV,MAAM,EAAE;IAC9C,IAAMoV,MAAM,GAAG/V,MAAM,CAACuH,MAAM,CAAC,IAAI,CAAC;IAClCuO,QAAQ,CAAClV,OAAO,CAAC,UAAAoV,QAAQ,EAAI;MAC3BD,MAAM,CAACC,QAAQ,CAAC,GAAG,IAAI;IACzB,CAAC,CAAC;IACFrB,EAAE,CAACsB,YAAY,GAAGtB,EAAE,CAACoB,MAAM,GAAGA,MAAM;EACtC;AACF;AAEA,SAASG,UAAUA,CAAEC,MAAM,EAAEvC,UAAU,EAAE;EACvCuC,MAAM,GAAG,CAACA,MAAM,IAAI,EAAE,EAAEzR,KAAK,CAAC,GAAG,CAAC;EAClC,IAAM+J,GAAG,GAAG0H,MAAM,CAACxV,MAAM;EAEzB,IAAI8N,GAAG,KAAK,CAAC,EAAE;IACbmF,UAAU,CAACwC,OAAO,GAAGD,MAAM,CAAC,CAAC,CAAC;EAChC,CAAC,MAAM,IAAI1H,GAAG,KAAK,CAAC,EAAE;IACpBmF,UAAU,CAACwC,OAAO,GAAGD,MAAM,CAAC,CAAC,CAAC;IAC9BvC,UAAU,CAACyC,QAAQ,GAAGF,MAAM,CAAC,CAAC,CAAC;EACjC;AACF;AAEA,SAASG,QAAQA,CAAErB,UAAU,EAAEsB,OAAO,EAAE;EACtC,IAAInN,IAAI,GAAG6L,UAAU,CAAC7L,IAAI,IAAI,CAAC,CAAC;EAChC,IAAMoN,OAAO,GAAGvB,UAAU,CAACuB,OAAO,IAAI,CAAC,CAAC;EAExC,IAAI,OAAOpN,IAAI,KAAK,UAAU,EAAE;IAC9B,IAAI;MACFA,IAAI,GAAGA,IAAI,CAACrH,IAAI,CAACwU,OAAO,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAO7W,CAAC,EAAE;MACV,IAAI+W,sGAAW,CAACC,aAAa,EAAE;QAC7BpF,OAAO,CAACC,IAAI,CAAC,wEAAwE,EAAEnI,IAAI,CAAC;MAC9F;IACF;EACF,CAAC,MAAM;IACL,IAAI;MACF;MACAA,IAAI,GAAG3D,IAAI,CAACC,KAAK,CAACD,IAAI,CAACkR,SAAS,CAACvN,IAAI,CAAC,CAAC;IACzC,CAAC,CAAC,OAAO1J,CAAC,EAAE,CAAC;EACf;EAEA,IAAI,CAACsH,aAAa,CAACoC,IAAI,CAAC,EAAE;IACxBA,IAAI,GAAG,CAAC,CAAC;EACX;EAEApJ,MAAM,CAACC,IAAI,CAACuW,OAAO,CAAC,CAAC5V,OAAO,CAAC,UAAAmQ,UAAU,EAAI;IACzC,IAAIwF,OAAO,CAACK,mBAAmB,CAACvS,OAAO,CAAC0M,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC7J,MAAM,CAACkC,IAAI,EAAE2H,UAAU,CAAC,EAAE;MACvF3H,IAAI,CAAC2H,UAAU,CAAC,GAAGyF,OAAO,CAACzF,UAAU,CAAC;IACxC;EACF,CAAC,CAAC;EAEF,OAAO3H,IAAI;AACb;AAEA,IAAMyN,UAAU,GAAG,CAACjU,MAAM,EAAEC,MAAM,EAAEiU,OAAO,EAAE9W,MAAM,EAAEoC,KAAK,EAAE,IAAI,CAAC;AAEjE,SAAS2U,cAAcA,CAAEzT,IAAI,EAAE;EAC7B,OAAO,SAAS0T,QAAQA,CAAEC,MAAM,EAAEC,MAAM,EAAE;IACxC,IAAI,IAAI,CAAC1J,GAAG,EAAE;MACZ,IAAI,CAACA,GAAG,CAAClK,IAAI,CAAC,GAAG2T,MAAM,CAAC,CAAC;IAC3B;EACF,CAAC;AACH;AAEA,SAASE,aAAaA,CAAElC,UAAU,EAAEmC,YAAY,EAAE;EAChD,IAAMC,YAAY,GAAGpC,UAAU,CAACqC,SAAS;EACzC,IAAMC,UAAU,GAAGtC,UAAU,CAACuC,OAAO;EACrC,IAAMC,SAAS,GAAGxC,UAAU,CAACG,MAAM;EAEnC,IAAIsC,QAAQ,GAAGzC,UAAU,CAAC0C,KAAK;EAE/B,IAAI,CAACD,QAAQ,EAAE;IACbzC,UAAU,CAAC0C,KAAK,GAAGD,QAAQ,GAAG,EAAE;EAClC;EAEA,IAAMJ,SAAS,GAAG,EAAE;EACpB,IAAIlV,KAAK,CAACC,OAAO,CAACgV,YAAY,CAAC,EAAE;IAC/BA,YAAY,CAACzW,OAAO,CAAC,UAAAgX,QAAQ,EAAI;MAC/BN,SAAS,CAAC/W,IAAI,CAACqX,QAAQ,CAAC7T,OAAO,CAAC,QAAQ,EAAK,IAAI,CAAAsE,MAAA,OAAK,CAAC,CAAC;MACxD,IAAIuP,QAAQ,KAAK,kBAAkB,EAAE;QACnC,IAAIxV,KAAK,CAACC,OAAO,CAACqV,QAAQ,CAAC,EAAE;UAC3BA,QAAQ,CAACnX,IAAI,CAAC,MAAM,CAAC;UACrBmX,QAAQ,CAACnX,IAAI,CAAC,OAAO,CAAC;QACxB,CAAC,MAAM;UACLmX,QAAQ,CAACpU,IAAI,GAAG;YACduU,IAAI,EAAEjV,MAAM;YACZhD,OAAO,EAAE;UACX,CAAC;UACD8X,QAAQ,CAACxV,KAAK,GAAG;YACf2V,IAAI,EAAE,CAACjV,MAAM,EAAEC,MAAM,EAAEiU,OAAO,EAAE1U,KAAK,EAAEpC,MAAM,EAAEyG,IAAI,CAAC;YACpD7G,OAAO,EAAE;UACX,CAAC;QACH;MACF;IACF,CAAC,CAAC;EACJ;EACA,IAAIoH,aAAa,CAACuQ,UAAU,CAAC,IAAIA,UAAU,CAACI,KAAK,EAAE;IACjDL,SAAS,CAAC/W,IAAI,CACZ6W,YAAY,CAAC;MACXU,UAAU,EAAEC,cAAc,CAACR,UAAU,CAACI,KAAK,EAAE,IAAI;IACnD,CAAC,CACH,CAAC;EACH;EACA,IAAIvV,KAAK,CAACC,OAAO,CAACoV,SAAS,CAAC,EAAE;IAC5BA,SAAS,CAAC7W,OAAO,CAAC,UAAAoX,QAAQ,EAAI;MAC5B,IAAIhR,aAAa,CAACgR,QAAQ,CAAC,IAAIA,QAAQ,CAACL,KAAK,EAAE;QAC7CL,SAAS,CAAC/W,IAAI,CACZ6W,YAAY,CAAC;UACXU,UAAU,EAAEC,cAAc,CAACC,QAAQ,CAACL,KAAK,EAAE,IAAI;QACjD,CAAC,CACH,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EACA,OAAOL,SAAS;AAClB;AAEA,SAASW,aAAaA,CAAE9Q,GAAG,EAAE0Q,IAAI,EAAEK,YAAY,EAAEC,IAAI,EAAE;EACrD;EACA,IAAI/V,KAAK,CAACC,OAAO,CAACwV,IAAI,CAAC,IAAIA,IAAI,CAAClX,MAAM,KAAK,CAAC,EAAE;IAC5C,OAAOkX,IAAI,CAAC,CAAC,CAAC;EAChB;EACA,OAAOA,IAAI;AACb;AAEA,SAASE,cAAcA,CAAEJ,KAAK,EAAiC;EAAA,IAA/BS,UAAU,GAAA1X,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,KAAK;EAAA,IAAEyX,IAAI,GAAAzX,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,EAAE;EAC3D,IAAMoX,UAAU,GAAG,CAAC,CAAC;EACrB,IAAI,CAACM,UAAU,EAAE;IACfN,UAAU,CAACO,KAAK,GAAG;MACjBR,IAAI,EAAEjV,MAAM;MACZV,KAAK,EAAE;IACT,CAAC;IACD;IACA4V,UAAU,CAACQ,OAAO,GAAG;MACnBT,IAAI,EAAE7X,MAAM;MACZkC,KAAK,EAAE;IACT,CAAC;IACD;IACA4V,UAAU,CAACS,mBAAmB,GAAG;MAC/BV,IAAI,EAAEjV,MAAM;MACZV,KAAK,EAAE;IACT,CAAC;IACD4V,UAAU,CAAChC,QAAQ,GAAG;MAAE;MACtB+B,IAAI,EAAE,IAAI;MACV3V,KAAK,EAAE,EAAE;MACT8U,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,MAAM,EAAEC,MAAM,EAAE;QAClC,IAAMnB,MAAM,GAAG/V,MAAM,CAACuH,MAAM,CAAC,IAAI,CAAC;QAClC0P,MAAM,CAACrW,OAAO,CAAC,UAAAoV,QAAQ,EAAI;UACzBD,MAAM,CAACC,QAAQ,CAAC,GAAG,IAAI;QACzB,CAAC,CAAC;QACF,IAAI,CAACwC,OAAO,CAAC;UACXzC,MAAM,EAANA;QACF,CAAC,CAAC;MACJ;IACF,CAAC;EACH;EACA,IAAI3T,KAAK,CAACC,OAAO,CAACsV,KAAK,CAAC,EAAE;IAAE;IAC1BA,KAAK,CAAC/W,OAAO,CAAC,UAAAuG,GAAG,EAAI;MACnB2Q,UAAU,CAAC3Q,GAAG,CAAC,GAAG;QAChB0Q,IAAI,EAAE,IAAI;QACVb,QAAQ,EAAED,cAAc,CAAC5P,GAAG;MAC9B,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIH,aAAa,CAAC2Q,KAAK,CAAC,EAAE;IAAE;IACjC3X,MAAM,CAACC,IAAI,CAAC0X,KAAK,CAAC,CAAC/W,OAAO,CAAC,UAAAuG,GAAG,EAAI;MAChC,IAAMsR,IAAI,GAAGd,KAAK,CAACxQ,GAAG,CAAC;MACvB,IAAIH,aAAa,CAACyR,IAAI,CAAC,EAAE;QAAE;QACzB,IAAIvW,KAAK,GAAGuW,IAAI,CAAC7Y,OAAO;QACxB,IAAIiH,IAAI,CAAC3E,KAAK,CAAC,EAAE;UACfA,KAAK,GAAGA,KAAK,CAAC,CAAC;QACjB;QAEAuW,IAAI,CAACZ,IAAI,GAAGI,aAAa,CAAC9Q,GAAG,EAAEsR,IAAI,CAACZ,IAAI,CAAC;QAEzCC,UAAU,CAAC3Q,GAAG,CAAC,GAAG;UAChB0Q,IAAI,EAAEhB,UAAU,CAACxS,OAAO,CAACoU,IAAI,CAACZ,IAAI,CAAC,KAAK,CAAC,CAAC,GAAGY,IAAI,CAACZ,IAAI,GAAG,IAAI;UAC7D3V,KAAK,EAALA,KAAK;UACL8U,QAAQ,EAAED,cAAc,CAAC5P,GAAG;QAC9B,CAAC;MACH,CAAC,MAAM;QAAE;QACP,IAAM0Q,IAAI,GAAGI,aAAa,CAAC9Q,GAAG,EAAEsR,IAAI,CAAC;QACrCX,UAAU,CAAC3Q,GAAG,CAAC,GAAG;UAChB0Q,IAAI,EAAEhB,UAAU,CAACxS,OAAO,CAACwT,IAAI,CAAC,KAAK,CAAC,CAAC,GAAGA,IAAI,GAAG,IAAI;UACnDb,QAAQ,EAAED,cAAc,CAAC5P,GAAG;QAC9B,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EACA,OAAO2Q,UAAU;AACnB;AAEA,SAASY,SAASA,CAAE3E,KAAK,EAAE;EACzB;EACA,IAAI;IACFA,KAAK,CAAC4E,EAAE,GAAGlT,IAAI,CAACC,KAAK,CAACD,IAAI,CAACkR,SAAS,CAAC5C,KAAK,CAAC,CAAC;EAC9C,CAAC,CAAC,OAAOrU,CAAC,EAAE,CAAC;EAEbqU,KAAK,CAAC6E,eAAe,GAAGxR,IAAI;EAC5B2M,KAAK,CAAC8E,cAAc,GAAGzR,IAAI;EAE3B2M,KAAK,CAAC+E,MAAM,GAAG/E,KAAK,CAAC+E,MAAM,IAAI,CAAC,CAAC;EAEjC,IAAI,CAAC5R,MAAM,CAAC6M,KAAK,EAAE,QAAQ,CAAC,EAAE;IAC5BA,KAAK,CAACgF,MAAM,GAAG,CAAC,CAAC;EACnB;EAEA,IAAI7R,MAAM,CAAC6M,KAAK,EAAE,UAAU,CAAC,EAAE;IAC7BA,KAAK,CAACgF,MAAM,GAAGrW,OAAA,CAAOqR,KAAK,CAACgF,MAAM,MAAK,QAAQ,GAAGhF,KAAK,CAACgF,MAAM,GAAG,CAAC,CAAC;IACnEhF,KAAK,CAACgF,MAAM,CAACC,QAAQ,GAAGjF,KAAK,CAACiF,QAAQ;EACxC;EAEA,IAAIhS,aAAa,CAAC+M,KAAK,CAACgF,MAAM,CAAC,EAAE;IAC/BhF,KAAK,CAAC+E,MAAM,GAAG9Y,MAAM,CAACmM,MAAM,CAAC,CAAC,CAAC,EAAE4H,KAAK,CAAC+E,MAAM,EAAE/E,KAAK,CAACgF,MAAM,CAAC;EAC9D;EAEA,OAAOhF,KAAK;AACd;AAEA,SAASkF,aAAaA,CAAEtE,EAAE,EAAEuE,cAAc,EAAE;EAC1C,IAAI3C,OAAO,GAAG5B,EAAE;EAChBuE,cAAc,CAACtY,OAAO,CAAC,UAAAuY,aAAa,EAAI;IACtC,IAAMC,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC;IACjC,IAAMjX,KAAK,GAAGiX,aAAa,CAAC,CAAC,CAAC;IAC9B,IAAIC,QAAQ,IAAI,OAAOlX,KAAK,KAAK,WAAW,EAAE;MAAE;MAC9C,IAAMmX,QAAQ,GAAGF,aAAa,CAAC,CAAC,CAAC;MACjC,IAAMG,SAAS,GAAGH,aAAa,CAAC,CAAC,CAAC;MAElC,IAAII,IAAI;MACR,IAAI1W,MAAM,CAAC2W,SAAS,CAACJ,QAAQ,CAAC,EAAE;QAC9BG,IAAI,GAAGH,QAAQ;MACjB,CAAC,MAAM,IAAI,CAACA,QAAQ,EAAE;QACpBG,IAAI,GAAGhD,OAAO;MAChB,CAAC,MAAM,IAAI,OAAO6C,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,EAAE;QACnD,IAAIA,QAAQ,CAAC/U,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;UACjCkV,IAAI,GAAGH,QAAQ,CAACK,MAAM,CAAC,CAAC,CAAC;QAC3B,CAAC,MAAM;UACLF,IAAI,GAAG5E,EAAE,CAAC+E,WAAW,CAACN,QAAQ,EAAE7C,OAAO,CAAC;QAC1C;MACF;MAEA,IAAI1T,MAAM,CAAC2W,SAAS,CAACD,IAAI,CAAC,EAAE;QAC1BhD,OAAO,GAAGrU,KAAK;MACjB,CAAC,MAAM,IAAI,CAACmX,QAAQ,EAAE;QACpB9C,OAAO,GAAGgD,IAAI,CAACrX,KAAK,CAAC;MACvB,CAAC,MAAM;QACL,IAAIE,KAAK,CAACC,OAAO,CAACkX,IAAI,CAAC,EAAE;UACvBhD,OAAO,GAAGgD,IAAI,CAAClE,IAAI,CAAC,UAAAsE,QAAQ,EAAI;YAC9B,OAAOhF,EAAE,CAAC+E,WAAW,CAACL,QAAQ,EAAEM,QAAQ,CAAC,KAAKzX,KAAK;UACrD,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI8E,aAAa,CAACuS,IAAI,CAAC,EAAE;UAC9BhD,OAAO,GAAGvW,MAAM,CAACC,IAAI,CAACsZ,IAAI,CAAC,CAAClE,IAAI,CAAC,UAAAuE,OAAO,EAAI;YAC1C,OAAOjF,EAAE,CAAC+E,WAAW,CAACL,QAAQ,EAAEE,IAAI,CAACK,OAAO,CAAC,CAAC,KAAK1X,KAAK;UAC1D,CAAC,CAAC;QACJ,CAAC,MAAM;UACLoP,OAAO,CAAC3L,KAAK,CAAC,iBAAiB,EAAE4T,IAAI,CAAC;QACxC;MACF;MAEA,IAAID,SAAS,EAAE;QACb/C,OAAO,GAAG5B,EAAE,CAAC+E,WAAW,CAACJ,SAAS,EAAE/C,OAAO,CAAC;MAC9C;IACF;EACF,CAAC,CAAC;EACF,OAAOA,OAAO;AAChB;AAEA,SAASsD,iBAAiBA,CAAElF,EAAE,EAAEmF,KAAK,EAAE/F,KAAK,EAAE;EAC5C,IAAMgG,QAAQ,GAAG,CAAC,CAAC;EAEnB,IAAI3X,KAAK,CAACC,OAAO,CAACyX,KAAK,CAAC,IAAIA,KAAK,CAACnZ,MAAM,EAAE;IACxC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACImZ,KAAK,CAAClZ,OAAO,CAAC,UAACwY,QAAQ,EAAE1Q,KAAK,EAAK;MACjC,IAAI,OAAO0Q,QAAQ,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACA,QAAQ,EAAE;UAAE;UACfW,QAAQ,CAAC,GAAG,GAAGrR,KAAK,CAAC,GAAGiM,EAAE;QAC5B,CAAC,MAAM;UACL,IAAIyE,QAAQ,KAAK,QAAQ,EAAE;YAAE;YAC3BW,QAAQ,CAAC,GAAG,GAAGrR,KAAK,CAAC,GAAGqL,KAAK;UAC/B,CAAC,MAAM,IAAIqF,QAAQ,KAAK,WAAW,EAAE;YACnC,IAAIrF,KAAK,CAACgF,MAAM,IAAIhF,KAAK,CAACgF,MAAM,CAACiB,QAAQ,EAAE;cACzCD,QAAQ,CAAC,GAAG,GAAGrR,KAAK,CAAC,GAAGqL,KAAK,CAACgF,MAAM,CAACiB,QAAQ;YAC/C,CAAC,MAAM;cACLD,QAAQ,CAAC,GAAG,GAAGrR,KAAK,CAAC,GAAG,CAACqL,KAAK,CAAC;YACjC;UACF,CAAC,MAAM,IAAIqF,QAAQ,CAAC/U,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAAE;YAC9C0V,QAAQ,CAAC,GAAG,GAAGrR,KAAK,CAAC,GAAGiM,EAAE,CAAC+E,WAAW,CAACN,QAAQ,CAACrV,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAEgQ,KAAK,CAAC;UAChF,CAAC,MAAM;YACLgG,QAAQ,CAAC,GAAG,GAAGrR,KAAK,CAAC,GAAGiM,EAAE,CAAC+E,WAAW,CAACN,QAAQ,CAAC;UAClD;QACF;MACF,CAAC,MAAM;QACLW,QAAQ,CAAC,GAAG,GAAGrR,KAAK,CAAC,GAAGuQ,aAAa,CAACtE,EAAE,EAAEyE,QAAQ,CAAC;MACrD;IACF,CAAC,CAAC;EACJ;EAEA,OAAOW,QAAQ;AACjB;AAEA,SAASE,aAAaA,CAAEC,GAAG,EAAE;EAC3B,IAAMjT,GAAG,GAAG,CAAC,CAAC;EACd,KAAK,IAAItF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuY,GAAG,CAACvZ,MAAM,EAAEgB,CAAC,EAAE,EAAE;IACnC,IAAMwY,OAAO,GAAGD,GAAG,CAACvY,CAAC,CAAC;IACtBsF,GAAG,CAACkT,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;EAC9B;EACA,OAAOlT,GAAG;AACZ;AAEA,SAASmT,gBAAgBA,CAAEzF,EAAE,EAAEZ,KAAK,EAA+C;EAAA,IAA7C9E,IAAI,GAAAvO,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,EAAE;EAAA,IAAEoZ,KAAK,GAAApZ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,EAAE;EAAA,IAAE2Z,QAAQ,GAAA3Z,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAoJ,SAAA;EAAA,IAAEiH,UAAU,GAAArQ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAoJ,SAAA;EAC/E,IAAIwQ,eAAe,GAAG,KAAK,CAAC,CAAC;EAC7B,IAAID,QAAQ,EAAE;IAAE;IACdC,eAAe,GAAGvG,KAAK,CAACwG,aAAa,IACnCxG,KAAK,CAACwG,aAAa,CAACC,OAAO,IAC3BzG,KAAK,CAACwG,aAAa,CAACC,OAAO,CAACC,OAAO,KAAK,IAAI;IAC9C,IAAI,CAACxL,IAAI,CAACtO,MAAM,EAAE;MAAE;MAClB,IAAI2Z,eAAe,EAAE;QACnB,OAAO,CAACvG,KAAK,CAAC;MAChB;MACA,OAAOA,KAAK,CAACgF,MAAM,CAACiB,QAAQ,IAAIjG,KAAK,CAACgF,MAAM;IAC9C;EACF;EAEA,IAAMgB,QAAQ,GAAGF,iBAAiB,CAAClF,EAAE,EAAEmF,KAAK,EAAE/F,KAAK,CAAC;EAEpD,IAAM2G,GAAG,GAAG,EAAE;EACdzL,IAAI,CAACrO,OAAO,CAAC,UAAA+Z,GAAG,EAAI;IAClB,IAAIA,GAAG,KAAK,QAAQ,EAAE;MACpB,IAAI5J,UAAU,KAAK,aAAa,IAAI,CAACsJ,QAAQ,EAAE;QAAE;QAC/CK,GAAG,CAACna,IAAI,CAACwT,KAAK,CAAC+E,MAAM,CAAC5W,KAAK,CAAC;MAC9B,CAAC,MAAM;QACL,IAAImY,QAAQ,IAAI,CAACC,eAAe,EAAE;UAChCI,GAAG,CAACna,IAAI,CAACwT,KAAK,CAACgF,MAAM,CAACiB,QAAQ,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,MAAM;UAAE;UACPU,GAAG,CAACna,IAAI,CAACwT,KAAK,CAAC;QACjB;MACF;IACF,CAAC,MAAM;MACL,IAAI3R,KAAK,CAACC,OAAO,CAACsY,GAAG,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACxCD,GAAG,CAACna,IAAI,CAAC0Z,aAAa,CAACU,GAAG,CAAC,CAAC;MAC9B,CAAC,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIzT,MAAM,CAAC6S,QAAQ,EAAEY,GAAG,CAAC,EAAE;QAC3DD,GAAG,CAACna,IAAI,CAACwZ,QAAQ,CAACY,GAAG,CAAC,CAAC;MACzB,CAAC,MAAM;QACLD,GAAG,CAACna,IAAI,CAACoa,GAAG,CAAC;MACf;IACF;EACF,CAAC,CAAC;EAEF,OAAOD,GAAG;AACZ;AAEA,IAAME,IAAI,GAAG,GAAG;AAChB,IAAMC,MAAM,GAAG,GAAG;AAElB,SAASC,gBAAgBA,CAAEC,SAAS,EAAEC,OAAO,EAAE;EAC7C,OAAQD,SAAS,KAAKC,OAAO,IAEzBA,OAAO,KAAK,cAAc,KAExBD,SAAS,KAAK,OAAO,IACrBA,SAAS,KAAK,KAAK,CAEtB;AACL;AAEA,SAASE,YAAYA,CAAEtG,EAAE,EAAE;EACzB,IAAIuG,OAAO,GAAGvG,EAAE,CAACuG,OAAO;EACxB;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACA,OAAO,KAAKA,OAAO,CAACC,QAAQ,CAAC7C,OAAO,IAAI4C,OAAO,CAACA,OAAO,CAACC,QAAQ,CAAC7C,OAAO,IAAI4C,OAAO,CAACE,MAAM,CAAC/E,QAAQ,CAAC,EAAE;IAC9H6E,OAAO,GAAGA,OAAO,CAACA,OAAO;EAC3B;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACA,OAAO;AACnC;AAEA,SAASG,WAAWA,CAAEtH,KAAK,EAAE;EAAA,IAAAuH,KAAA;EAC3BvH,KAAK,GAAG2E,SAAS,CAAC3E,KAAK,CAAC;;EAExB;EACA,IAAMyG,OAAO,GAAG,CAACzG,KAAK,CAACwG,aAAa,IAAIxG,KAAK,CAAC+E,MAAM,EAAE0B,OAAO;EAC7D,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOlJ,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;EAChC;EACA,IAAMgK,SAAS,GAAGf,OAAO,CAACe,SAAS,IAAIf,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;EAC9D,IAAI,CAACe,SAAS,EAAE;IACd,OAAOjK,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC;EAChC;;EAEA;EACA,IAAMwJ,SAAS,GAAGhH,KAAK,CAAC8D,IAAI;EAE5B,IAAM6C,GAAG,GAAG,EAAE;EAEda,SAAS,CAAC3a,OAAO,CAAC,UAAA4a,QAAQ,EAAI;IAC5B,IAAI3D,IAAI,GAAG2D,QAAQ,CAAC,CAAC,CAAC;IACtB,IAAMC,WAAW,GAAGD,QAAQ,CAAC,CAAC,CAAC;IAE/B,IAAMnB,QAAQ,GAAGxC,IAAI,CAACvT,MAAM,CAAC,CAAC,CAAC,KAAKuW,MAAM;IAC1ChD,IAAI,GAAGwC,QAAQ,GAAGxC,IAAI,CAACzU,KAAK,CAAC,CAAC,CAAC,GAAGyU,IAAI;IACtC,IAAM6D,MAAM,GAAG7D,IAAI,CAACvT,MAAM,CAAC,CAAC,CAAC,KAAKsW,IAAI;IACtC/C,IAAI,GAAG6D,MAAM,GAAG7D,IAAI,CAACzU,KAAK,CAAC,CAAC,CAAC,GAAGyU,IAAI;IAEpC,IAAI4D,WAAW,IAAIX,gBAAgB,CAACC,SAAS,EAAElD,IAAI,CAAC,EAAE;MACpD4D,WAAW,CAAC7a,OAAO,CAAC,UAAA+a,UAAU,EAAI;QAChC,IAAM5K,UAAU,GAAG4K,UAAU,CAAC,CAAC,CAAC;QAChC,IAAI5K,UAAU,EAAE;UACd,IAAI6K,UAAU,GAAGN,KAAI,CAAC9N,GAAG;UACzB,IAAIoO,UAAU,CAACT,QAAQ,CAAC7C,OAAO,EAAE;YAAE;YACjCsD,UAAU,GAAGX,YAAY,CAACW,UAAU,CAAC,IAAIA,UAAU;UACrD;UACA,IAAI7K,UAAU,KAAK,OAAO,EAAE;YAC1B6K,UAAU,CAACzI,KAAK,CAAC3S,KAAK,CAACob,UAAU,EAC/BxB,gBAAgB,CACdkB,KAAI,CAAC9N,GAAG,EACRuG,KAAK,EACL4H,UAAU,CAAC,CAAC,CAAC,EACbA,UAAU,CAAC,CAAC,CAAC,EACbtB,QAAQ,EACRtJ,UACF,CAAC,CAAC;YACJ;UACF;UACA,IAAM8K,OAAO,GAAGD,UAAU,CAAC7K,UAAU,CAAC;UACtC,IAAI,CAAClK,IAAI,CAACgV,OAAO,CAAC,EAAE;YAClB,MAAM,IAAI7X,KAAK,SAAAqE,MAAA,CAAS0I,UAAU,uBAAoB,CAAC;UACzD;UACA,IAAI2K,MAAM,EAAE;YACV,IAAIG,OAAO,CAACC,IAAI,EAAE;cAChB;YACF;YACAD,OAAO,CAACC,IAAI,GAAG,IAAI;UACrB;UACA,IAAIrR,MAAM,GAAG2P,gBAAgB,CAC3BkB,KAAI,CAAC9N,GAAG,EACRuG,KAAK,EACL4H,UAAU,CAAC,CAAC,CAAC,EACbA,UAAU,CAAC,CAAC,CAAC,EACbtB,QAAQ,EACRtJ,UACF,CAAC;UACDtG,MAAM,GAAGrI,KAAK,CAACC,OAAO,CAACoI,MAAM,CAAC,GAAGA,MAAM,GAAG,EAAE;UAC5C;UACA,IAAI,2DAA2D,CAACjH,IAAI,CAACqY,OAAO,CAAC1Y,QAAQ,CAAC,CAAC,CAAC,EAAE;YACxF;YACAsH,MAAM,GAAGA,MAAM,CAACpC,MAAM,CAAC,YAAqB0L,KAAK,CAAC,CAAC;UACrD;UACA2G,GAAG,CAACna,IAAI,CAACsb,OAAO,CAACrb,KAAK,CAACob,UAAU,EAAEnR,MAAM,CAAC,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,IACEsQ,SAAS,KAAK,OAAO,IACrBL,GAAG,CAAC/Z,MAAM,KAAK,CAAC,IAChB,OAAO+Z,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW,EAC7B;IACA,OAAOA,GAAG,CAAC,CAAC,CAAC;EACf;AACF;AAEA,IAAMqB,QAAQ,GAAG,CAAC,CAAC;AAEnB,IAAInO,MAAM;AAEV;EACEA,MAAM,GAAG3I,EAAE,CAAC0H,iBAAiB,CAAC,CAAC,CAACe,QAAQ;AAC1C;AAEA,SAASsO,gBAAgBA,CAAA,EAAI;EAC3B,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE;IACrB;EACF;EACA,IAAMC,UAAU,GAAGlc,MAAM,CAACC,IAAI,CAACkc,WAAW,CAACC,OAAO,CAAC;EACnD,IAAIF,UAAU,CAACvb,MAAM,EAAE;IACrBub,UAAU,CAACtb,OAAO,CAAC,UAACgN,MAAM,EAAK;MAC7B,IAAMyO,WAAW,GAAGN,QAAQ,CAACnO,MAAM,CAAC;MACpC,IAAM0O,YAAY,GAAGH,WAAW,CAACC,OAAO,CAACxO,MAAM,CAAC;MAChD,IAAIyO,WAAW,EAAE;QACfrc,MAAM,CAACmM,MAAM,CAACkQ,WAAW,EAAEC,YAAY,CAAC;MAC1C,CAAC,MAAM;QACLP,QAAQ,CAACnO,MAAM,CAAC,GAAG0O,YAAY;MACjC;IACF,CAAC,CAAC;EACJ;AACF;AAEAN,gBAAgB,CAAC,CAAC;AAElB,IAAMO,IAAI,GAAG,IAAAC,oBAAW,EACtB5O,MAAM,EACL,CAAC,CACJ,CAAC;AACD,IAAM7N,CAAC,GAAGwc,IAAI,CAACxc,CAAC;AAChB,IAAM0c,SAAS,GAAIF,IAAI,CAACjH,KAAK,GAAG;EAC9BoH,YAAY,WAAZA,YAAYA,CAAA,EAAI;IAAA,IAAAC,MAAA;IACd,IAAMC,OAAO,GAAGL,IAAI,CAACA,IAAI,CAACM,WAAW,CAAC,YAAM;MAC1CF,MAAI,CAACG,YAAY,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAAC5J,KAAK,CAAC,oBAAoB,EAAE,YAAY;MAC3C0J,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ,CAAC;EACDpG,OAAO,EAAE;IACPuG,GAAG,WAAHA,GAAGA,CAAE5V,GAAG,EAAE6V,MAAM,EAAE;MAChB,OAAOjd,CAAC,CAACoH,GAAG,EAAE6V,MAAM,CAAC;IACvB;EACF;AACF,CAAE;AACF,IAAMC,WAAW,GAAGV,IAAI,CAAC5O,SAAS;AAClC,IAAMuP,WAAW,GAAGX,IAAI,CAACnP,SAAS;AAElC,SAAS+P,aAAaA,CAAEnX,GAAG,EAAEoX,KAAK,EAAExP,MAAM,EAAE;EAC1C,IAAMyP,KAAK,GAAGrX,GAAG,CAACsX,UAAU,CAAC;IAC3B1P,MAAM,EAAEA,MAAM,IAAI2O,IAAI,CAACnP,SAAS,CAAC;EACnC,CAAC,CAAC;EACF,IAAMmQ,cAAc,GAAG,EAAE;EACzBH,KAAK,CAACI,YAAY,GAAG,UAAA1W,EAAE,EAAI;IACzByW,cAAc,CAAChd,IAAI,CAACuG,EAAE,CAAC;EACzB,CAAC;EACD9G,MAAM,CAACgB,cAAc,CAACoc,KAAK,EAAE,SAAS,EAAE;IACtCK,GAAG,WAAHA,GAAGA,CAAA,EAAI;MACL,OAAOJ,KAAK,CAACzP,MAAM;IACrB,CAAC;IACD8P,GAAG,WAAHA,GAAGA,CAAEC,CAAC,EAAE;MACNN,KAAK,CAACzP,MAAM,GAAG+P,CAAC;MAChBJ,cAAc,CAAC3c,OAAO,CAAC,UAAAgd,KAAK;QAAA,OAAIA,KAAK,CAACD,CAAC,CAAC;MAAA,EAAC;IAC3C;EACF,CAAC,CAAC;AACJ;AAEA,SAAS1B,cAAcA,CAAA,EAAI;EACzB,OAAO,OAAOE,WAAW,KAAK,WAAW,IAAIA,WAAW,CAACC,OAAO,IAAI,CAAC,CAACpc,MAAM,CAACC,IAAI,CAACkc,WAAW,CAACC,OAAO,CAAC,CAACzb,MAAM;AAC/G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAMkd,aAAa,GAAG,CAAC,CAAC;AAExB,IAAMC,iBAAiB,GAAG,EAAE;AAE5B,SAASC,eAAeA,CAAEC,EAAE,EAAE;EAC5B,IAAIA,EAAE,EAAE;IACN,IAAMC,YAAY,GAAGJ,aAAa,CAACG,EAAE,CAAC;IACtC,OAAOH,aAAa,CAACG,EAAE,CAAC;IACxB,OAAOC,YAAY;EACrB;EACA,OAAOH,iBAAiB,CAACI,KAAK,CAAC,CAAC;AAClC;AAEA,IAAM3V,KAAK,GAAG,CACZ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,gBAAgB,EAChB,eAAe,EACf,sBAAsB,CACvB;AAED,SAAS4V,gBAAgBA,CAAA,EAAI;EAC3BnY,YAAG,CAACvC,SAAS,CAAC2a,qBAAqB,GAAG,YAAY;IAChD;IACA;MACE,OAAO,IAAI,CAAChD,MAAM,CAACgD,qBAAqB,CAAC,CAAC;IAC5C;EACF,CAAC;EACD,IAAMC,QAAQ,GAAGrY,YAAG,CAACvC,SAAS,CAACgS,WAAW;EAC1CzP,YAAG,CAACvC,SAAS,CAACgS,WAAW,GAAG,UAAUhN,IAAI,EAAEwG,IAAI,EAAE;IAChD,IAAIxG,IAAI,KAAK,QAAQ,IAAIwG,IAAI,IAAIA,IAAI,CAACqP,MAAM,EAAE;MAC5C,IAAI,CAACC,gBAAgB,GAAGR,eAAe,CAAC9O,IAAI,CAACqP,MAAM,CAAC;MACpD,OAAOrP,IAAI,CAACqP,MAAM;IACpB;IACA,OAAOD,QAAQ,CAACtc,IAAI,CAAC,IAAI,EAAE0G,IAAI,EAAEwG,IAAI,CAAC;EACxC,CAAC;AACH;AAEA,SAASuP,qBAAqBA,CAAA,EAAI;EAChC,IAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,IAAMC,OAAO,GAAG,CAAC,CAAC;EAElB1Y,YAAG,CAACvC,SAAS,CAACkb,qBAAqB,GAAG,UAAUtG,KAAK,EAAE;IACrD,IAAMuG,GAAG,GAAGH,MAAM,CAACpG,KAAK,CAAC;IACzB,IAAI,CAACuG,GAAG,EAAE;MACRF,OAAO,CAACrG,KAAK,CAAC,GAAG,IAAI;MACrB,IAAI,CAACrF,GAAG,CAAC,gBAAgB,EAAE,YAAM;QAC/B,OAAO0L,OAAO,CAACrG,KAAK,CAAC;MACvB,CAAC,CAAC;IACJ;IACA,OAAOuG,GAAG;EACZ,CAAC;EAED5Y,YAAG,CAACvC,SAAS,CAACob,qBAAqB,GAAG,UAAUxG,KAAK,EAAE/U,IAAI,EAAE6D,GAAG,EAAE;IAChE,IAAMiC,IAAI,GAAGqV,MAAM,CAACpG,KAAK,CAAC;IAC1B,IAAIjP,IAAI,EAAE;MACR,IAAM0V,MAAM,GAAG1V,IAAI,CAAC9F,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/B,OAAO6D,GAAG,GAAG2X,MAAM,CAAC3X,GAAG,CAAC,GAAG2X,MAAM;IACnC,CAAC,MAAM;MACLJ,OAAO,CAACrG,KAAK,CAAC,GAAG,IAAI;MACrB,IAAI,CAACrF,GAAG,CAAC,gBAAgB,EAAE,YAAM;QAC/B,OAAO0L,OAAO,CAACrG,KAAK,CAAC;MACvB,CAAC,CAAC;IACJ;EACF,CAAC;EAEDrS,YAAG,CAACvC,SAAS,CAACsb,qBAAqB,GAAG,UAAUzb,IAAI,EAAEpB,KAAK,EAAE;IAC3D,IAAMiU,MAAM,GAAG,IAAI,CAACgF,QAAQ,CAAC6D,SAAS,CAAC3G,KAAK;IAC5C,IAAIlC,MAAM,EAAE;MACV,IAAMkC,KAAK,GAAGlC,MAAM,CAACzR,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAClC,IAAMoa,MAAM,GAAGL,MAAM,CAACpG,KAAK,CAAC,GAAGoG,MAAM,CAACpG,KAAK,CAAC,IAAI,CAAC,CAAC;MAClDyG,MAAM,CAACxb,IAAI,CAAC,GAAGpB,KAAK;MACpB,IAAIwc,OAAO,CAACrG,KAAK,CAAC,EAAE;QAClBqG,OAAO,CAACrG,KAAK,CAAC,CAACyE,YAAY,CAAC,CAAC;MAC/B;IACF;EACF,CAAC;EAED9W,YAAG,CAACsP,KAAK,CAAC;IACR2J,SAAS,WAATA,SAASA,CAAA,EAAI;MACX,IAAMD,SAAS,GAAG,IAAI,CAAC7D,QAAQ,CAAC6D,SAAS;MACzC,IAAM3G,KAAK,GAAG2G,SAAS,IAAIA,SAAS,CAAC3G,KAAK;MAC1C,IAAIA,KAAK,EAAE;QACT,OAAOoG,MAAM,CAACpG,KAAK,CAAC;QACpB,OAAOqG,OAAO,CAACrG,KAAK,CAAC;MACvB;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAAS6G,YAAYA,CAAEvK,EAAE,EAAAwK,KAAA,EAGtB;EAAA,IAFDvK,KAAK,GAAAuK,KAAA,CAALvK,KAAK;IACLwK,QAAQ,GAAAD,KAAA,CAARC,QAAQ;EAERjB,gBAAgB,CAAC,CAAC;EAClB;IACEK,qBAAqB,CAAC,CAAC;EACzB;EACA,IAAI7J,EAAE,CAACwG,QAAQ,CAACkE,KAAK,EAAE;IACrBrZ,YAAG,CAACvC,SAAS,CAAC6b,MAAM,GAAG3K,EAAE,CAACwG,QAAQ,CAACkE,KAAK;EAC1C;EACAtZ,UAAU,CAACC,YAAG,CAAC;EAEfA,YAAG,CAACvC,SAAS,CAAC8b,MAAM,GAAG,WAAW;EAElCvZ,YAAG,CAACsP,KAAK,CAAC;IACRoH,YAAY,WAAZA,YAAYA,CAAA,EAAI;MACd,IAAI,CAAC,IAAI,CAACvB,QAAQ,CAACrG,MAAM,EAAE;QACzB;MACF;MAEA,IAAI,CAACA,MAAM,GAAG,IAAI,CAACqG,QAAQ,CAACrG,MAAM;MAElC,IAAI,CAACD,GAAG,GAAAhU,eAAA;QACNuI,IAAI,EAAE,CAAC;MAAC,GACP,IAAI,CAAC0L,MAAM,EAAG,IAAI,CAACqG,QAAQ,CAACvH,UAAU,CACxC;MAED,IAAI,CAACwH,MAAM,GAAG,IAAI,CAACD,QAAQ,CAACvH,UAAU;MAEtC,OAAO,IAAI,CAACuH,QAAQ,CAACrG,MAAM;MAC3B,OAAO,IAAI,CAACqG,QAAQ,CAACvH,UAAU;MAC/B,IAAI,IAAI,CAACkB,MAAM,KAAK,MAAM,IAAI,OAAOxH,MAAM,KAAK,UAAU,EAAE;QAAE;QAC5D,IAAMD,GAAG,GAAGC,MAAM,CAAC,CAAC;QACpB,IAAID,GAAG,CAACG,GAAG,IAAIH,GAAG,CAACG,GAAG,CAACgS,KAAK,EAAE;UAC5B,IAAI,CAACC,KAAK,GAAGpS,GAAG,CAACG,GAAG,CAACgS,KAAK;QAC5B;MACF;MACA,IAAI,IAAI,CAAC1K,MAAM,KAAK,KAAK,EAAE;QACzBsK,QAAQ,CAAC,IAAI,CAAC;QACd1K,SAAS,CAAC,IAAI,EAAEE,KAAK,CAAC;MACxB;IACF;EACF,CAAC,CAAC;EAEF,IAAM8K,UAAU,GAAG;IACjBC,QAAQ,WAARA,QAAQA,CAAE1Q,IAAI,EAAE;MACd,IAAI,IAAI,CAACzB,GAAG,EAAE;QAAE;QACd;MACF;MACA;QACE,IAAIvI,EAAE,CAAC2a,OAAO,IAAI,CAAC3a,EAAE,CAAC2a,OAAO,CAAC,UAAU,CAAC,EAAE;UAAE;UAC3CtO,OAAO,CAAC3L,KAAK,CAAC,qDAAqD,CAAC;QACtE;MACF;MAEA,IAAI,CAAC6H,GAAG,GAAGmH,EAAE;MAEb,IAAI,CAACnH,GAAG,CAACqH,GAAG,GAAG;QACbxH,GAAG,EAAE;MACP,CAAC;MAED,IAAI,CAACG,GAAG,CAAC4N,MAAM,GAAG,IAAI;MACtB;MACA,IAAI,CAAC5N,GAAG,CAACqS,UAAU,GAAG,IAAI,CAACA,UAAU;MAErC,IAAI,CAACrS,GAAG,CAACsS,UAAU,GAAG,IAAI;MAC1B,IAAI,CAACtS,GAAG,CAACiI,WAAW,CAAC,SAAS,EAAExG,IAAI,CAAC;MAErC,IAAI,CAACzB,GAAG,CAACiI,WAAW,CAAC,UAAU,EAAExG,IAAI,CAAC;IACxC;EACF,CAAC;;EAED;EACAyQ,UAAU,CAACG,UAAU,GAAGlL,EAAE,CAACwG,QAAQ,CAAC0E,UAAU,IAAI,CAAC,CAAC;EACpD;EACA,IAAMrJ,OAAO,GAAG7B,EAAE,CAACwG,QAAQ,CAAC3E,OAAO;EACnC,IAAIA,OAAO,EAAE;IACXxW,MAAM,CAACC,IAAI,CAACuW,OAAO,CAAC,CAAC5V,OAAO,CAAC,UAAA0C,IAAI,EAAI;MACnCoc,UAAU,CAACpc,IAAI,CAAC,GAAGkT,OAAO,CAAClT,IAAI,CAAC;IAClC,CAAC,CAAC;EACJ;EAEA6Z,aAAa,CAACnX,YAAG,EAAE2O,EAAE,EAAE1P,EAAE,CAAC0H,iBAAiB,CAAC,CAAC,CAACe,QAAQ,IAAI,SAAS,CAAC;EAEpE6H,SAAS,CAACmK,UAAU,EAAEnX,KAAK,CAAC;EAE5B,OAAOmX,UAAU;AACnB;AAEA,IAAM9K,KAAK,GAAG,CAAC,WAAW,EAAE,sBAAsB,EAAE,iBAAiB,CAAC;AAEtE,SAASmL,aAAaA,CAAEpL,EAAE,EAAEqL,MAAM,EAAE;EAClC,IAAMC,SAAS,GAAGtL,EAAE,CAACsL,SAAS;EAC9B;EACA,KAAK,IAAIte,CAAC,GAAGse,SAAS,CAACtf,MAAM,GAAG,CAAC,EAAEgB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC9C,IAAMue,OAAO,GAAGD,SAAS,CAACte,CAAC,CAAC;IAC5B,IAAIue,OAAO,CAAC9E,MAAM,CAAChF,OAAO,KAAK4J,MAAM,EAAE;MACrC,OAAOE,OAAO;IAChB;EACF;EACA;EACA,IAAIC,QAAQ;EACZ,KAAK,IAAIxe,EAAC,GAAGse,SAAS,CAACtf,MAAM,GAAG,CAAC,EAAEgB,EAAC,IAAI,CAAC,EAAEA,EAAC,EAAE,EAAE;IAC9Cwe,QAAQ,GAAGJ,aAAa,CAACE,SAAS,CAACte,EAAC,CAAC,EAAEqe,MAAM,CAAC;IAC9C,IAAIG,QAAQ,EAAE;MACZ,OAAOA,QAAQ;IACjB;EACF;AACF;AAEA,SAAS/I,YAAYA,CAAEvN,OAAO,EAAE;EAC9B,OAAOuW,QAAQ,CAACvW,OAAO,CAAC;AAC1B;AAEA,SAASwW,MAAMA,CAAA,EAAI;EACjB,OAAO,CAAC,CAAC,IAAI,CAACC,KAAK;AACrB;AAEA,SAASC,YAAYA,CAAExH,MAAM,EAAE;EAC7B,IAAI,CAACjF,YAAY,CAAC,KAAK,EAAEiF,MAAM,CAAC;AAClC;AAEA,SAASyH,mBAAmBA,CAAE5M,UAAU,EAAE6M,QAAQ,EAAEC,KAAK,EAAE;EACzD,IAAMC,UAAU,GAAG/M,UAAU,CAAC4M,mBAAmB,CAACC,QAAQ,CAAC;EAC3DE,UAAU,CAAC/f,OAAO,CAAC,UAAAggB,SAAS,EAAI;IAC9B,IAAMC,GAAG,GAAGD,SAAS,CAACpG,OAAO,CAACqG,GAAG;IACjCH,KAAK,CAACG,GAAG,CAAC,GAAGD,SAAS,CAACpT,GAAG,IAAIoT,SAAS;IACvC;MACE,IAAIA,SAAS,CAACpG,OAAO,CAACsG,UAAU,KAAK,QAAQ,EAAE;QAC7CF,SAAS,CAACJ,mBAAmB,CAAC,aAAa,CAAC,CAAC5f,OAAO,CAAC,UAAAmgB,eAAe,EAAI;UACtEP,mBAAmB,CAACO,eAAe,EAAEN,QAAQ,EAAEC,KAAK,CAAC;QACvD,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAAStB,QAAQA,CAAEzK,EAAE,EAAE;EACrB,IAAMf,UAAU,GAAGe,EAAE,CAACyG,MAAM;EAC5Bpb,MAAM,CAACgB,cAAc,CAAC2T,EAAE,EAAE,OAAO,EAAE;IACjC8I,GAAG,WAAHA,GAAGA,CAAA,EAAI;MACL,IAAMiD,KAAK,GAAG,CAAC,CAAC;MAChBF,mBAAmB,CAAC5M,UAAU,EAAE,UAAU,EAAE8M,KAAK,CAAC;MAClD;MACA,IAAMM,aAAa,GAAGpN,UAAU,CAAC4M,mBAAmB,CAAC,iBAAiB,CAAC;MACvEQ,aAAa,CAACpgB,OAAO,CAAC,UAAAggB,SAAS,EAAI;QACjC,IAAMC,GAAG,GAAGD,SAAS,CAACpG,OAAO,CAACqG,GAAG;QACjC,IAAI,CAACH,KAAK,CAACG,GAAG,CAAC,EAAE;UACfH,KAAK,CAACG,GAAG,CAAC,GAAG,EAAE;QACjB;QACAH,KAAK,CAACG,GAAG,CAAC,CAACtgB,IAAI,CAACqgB,SAAS,CAACpT,GAAG,IAAIoT,SAAS,CAAC;MAC7C,CAAC,CAAC;MACF,OAAOF,KAAK;IACd;EACF,CAAC,CAAC;AACJ;AAEA,SAASO,UAAUA,CAAElN,KAAK,EAAE;EAC1B,IAAAmN,KAAA,GAGInN,KAAK,CAACgF,MAAM,IAAIhF,KAAK,CAAC7R,KAAK;IAF7B8d,MAAM,GAAAkB,KAAA,CAANlB,MAAM;IACN/K,UAAU,GAAAiM,KAAA,CAAVjM,UAAU,CACoB,CAAC;;EAEjC,IAAIkL,QAAQ;EAEZ,IAAIH,MAAM,EAAE;IACVG,QAAQ,GAAGJ,aAAa,CAAC,IAAI,CAACvS,GAAG,EAAEwS,MAAM,CAAC;EAC5C;EAEA,IAAI,CAACG,QAAQ,EAAE;IACbA,QAAQ,GAAG,IAAI,CAAC3S,GAAG;EACrB;EAEAyH,UAAU,CAACkM,MAAM,GAAGhB,QAAQ;AAC9B;AAEA,SAASiB,QAAQA,CAAEzM,EAAE,EAAE;EACrB,OAAOuK,YAAY,CAACvK,EAAE,EAAE;IACtBC,KAAK,EAALA,KAAK;IACLwK,QAAQ,EAARA;EACF,CAAC,CAAC;AACJ;AAEA,SAASiC,SAASA,CAAE1M,EAAE,EAAE;EACtB2M,GAAG,CAACF,QAAQ,CAACzM,EAAE,CAAC,CAAC;EACjB,OAAOA,EAAE;AACX;AAEA,IAAM4M,eAAe,GAAG,UAAU;AAClC,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAG5c,CAAC;EAAA,OAAI,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC1B,QAAQ,CAAC,EAAE,CAAC;AAAA;AACrE,IAAMse,OAAO,GAAG,MAAM;;AAEtB;AACA;AACA;AACA,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAG5d,GAAG;EAAA,OAAI6d,kBAAkB,CAAC7d,GAAG,CAAC,CAC1CC,OAAO,CAACwd,eAAe,EAAEC,qBAAqB,CAAC,CAC/Czd,OAAO,CAAC0d,OAAO,EAAE,GAAG,CAAC;AAAA;AAExB,SAASG,cAAcA,CAAE3a,GAAG,EAAsB;EAAA,IAApB4a,SAAS,GAAAnhB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAGghB,MAAM;EAC9C,IAAMtZ,GAAG,GAAGnB,GAAG,GAAGjH,MAAM,CAACC,IAAI,CAACgH,GAAG,CAAC,CAACtC,GAAG,CAAC,UAAAwC,GAAG,EAAI;IAC5C,IAAM2a,GAAG,GAAG7a,GAAG,CAACE,GAAG,CAAC;IAEpB,IAAI2a,GAAG,KAAKhY,SAAS,EAAE;MACrB,OAAO,EAAE;IACX;IAEA,IAAIgY,GAAG,KAAK,IAAI,EAAE;MAChB,OAAOD,SAAS,CAAC1a,GAAG,CAAC;IACvB;IAEA,IAAI/E,KAAK,CAACC,OAAO,CAACyf,GAAG,CAAC,EAAE;MACtB,IAAM5d,MAAM,GAAG,EAAE;MACjB4d,GAAG,CAAClhB,OAAO,CAAC,UAAAmhB,IAAI,EAAI;QAClB,IAAIA,IAAI,KAAKjY,SAAS,EAAE;UACtB;QACF;QACA,IAAIiY,IAAI,KAAK,IAAI,EAAE;UACjB7d,MAAM,CAAC3D,IAAI,CAACshB,SAAS,CAAC1a,GAAG,CAAC,CAAC;QAC7B,CAAC,MAAM;UACLjD,MAAM,CAAC3D,IAAI,CAACshB,SAAS,CAAC1a,GAAG,CAAC,GAAG,GAAG,GAAG0a,SAAS,CAACE,IAAI,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MACF,OAAO7d,MAAM,CAACY,IAAI,CAAC,GAAG,CAAC;IACzB;IAEA,OAAO+c,SAAS,CAAC1a,GAAG,CAAC,GAAG,GAAG,GAAG0a,SAAS,CAACC,GAAG,CAAC;EAC9C,CAAC,CAAC,CAAC1hB,MAAM,CAAC,UAAA4hB,CAAC;IAAA,OAAIA,CAAC,CAACrhB,MAAM,GAAG,CAAC;EAAA,EAAC,CAACmE,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;EAC7C,OAAOsD,GAAG,OAAAC,MAAA,CAAOD,GAAG,IAAK,EAAE;AAC7B;AAEA,SAAS6Z,kBAAkBA,CAAEC,mBAAmB,EAGxC;EAAA,IAAAC,KAAA,GAAAzhB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAJ,CAAC,CAAC;IAFJ2f,MAAM,GAAA8B,KAAA,CAAN9B,MAAM;IACNE,YAAY,GAAA4B,KAAA,CAAZ5B,YAAY;EAEZ,IAAA6B,iBAAA,GAAmC1M,gBAAgB,CAAC1P,YAAG,EAAEkc,mBAAmB,CAAC;IAAAG,kBAAA,GAAAphB,cAAA,CAAAmhB,iBAAA;IAAtEzM,YAAY,GAAA0M,kBAAA;IAAEpN,UAAU,GAAAoN,kBAAA;EAE/B,IAAMxY,OAAO,GAAApJ,aAAA;IACX6hB,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE;EAAI,GAChBtN,UAAU,CAACpL,OAAO,IAAI,CAAC,CAAC,CAC7B;EAED;IACE;IACA,IAAIoL,UAAU,CAAC,WAAW,CAAC,IAAIA,UAAU,CAAC,WAAW,CAAC,CAACpL,OAAO,EAAE;MAC9D7J,MAAM,CAACmM,MAAM,CAACtC,OAAO,EAAEoL,UAAU,CAAC,WAAW,CAAC,CAACpL,OAAO,CAAC;IACzD;EACF;EAEA,IAAM2Y,gBAAgB,GAAG;IACvB3Y,OAAO,EAAPA,OAAO;IACPT,IAAI,EAAEkN,QAAQ,CAACrB,UAAU,EAAEjP,YAAG,CAACvC,SAAS,CAAC;IACzC6T,SAAS,EAAEH,aAAa,CAAClC,UAAU,EAAEmC,YAAY,CAAC;IAClDU,UAAU,EAAEC,cAAc,CAAC9C,UAAU,CAAC0C,KAAK,EAAE,KAAK,EAAE1C,UAAU,CAACwN,MAAM,CAAC;IACtEC,SAAS,EAAE;MACTC,QAAQ,WAARA,QAAQA,CAAA,EAAI;QACV,IAAM7K,UAAU,GAAG,IAAI,CAACA,UAAU;QAElC,IAAMjO,OAAO,GAAG;UACdiL,MAAM,EAAEuL,MAAM,CAACte,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,WAAW;UAChD6R,UAAU,EAAE,IAAI;UAChBoL,SAAS,EAAElH;QACb,CAAC;QAED5B,UAAU,CAAC4B,UAAU,CAACO,KAAK,EAAE,IAAI,CAAC;;QAElC;QACAkI,YAAY,CAACxe,IAAI,CAAC,IAAI,EAAE;UACtBie,MAAM,EAAE,IAAI,CAAC3J,QAAQ;UACrBpB,UAAU,EAAEpL;QACd,CAAC,CAAC;;QAEF;QACA,IAAI,CAAC2D,GAAG,GAAG,IAAImI,YAAY,CAAC9L,OAAO,CAAC;;QAEpC;QACAgM,SAAS,CAAC,IAAI,CAACrI,GAAG,EAAEsK,UAAU,CAAChC,QAAQ,CAAC;;QAExC;QACA,IAAI,CAACtI,GAAG,CAACoV,MAAM,CAAC,CAAC;MACnB,CAAC;MACDC,KAAK,WAALA,KAAKA,CAAA,EAAI;QACP;QACA;QACA,IAAI,IAAI,CAACrV,GAAG,EAAE;UACZ,IAAI,CAACA,GAAG,CAACsS,UAAU,GAAG,IAAI;UAC1B,IAAI,CAACtS,GAAG,CAACiI,WAAW,CAAC,SAAS,CAAC;UAC/B,IAAI,CAACjI,GAAG,CAACiI,WAAW,CAAC,SAAS,CAAC;QACjC;MACF,CAAC;MACDqN,QAAQ,WAARA,QAAQA,CAAA,EAAI;QACV,IAAI,CAACtV,GAAG,IAAI,IAAI,CAACA,GAAG,CAACuV,QAAQ,CAAC,CAAC;MACjC;IACF,CAAC;IACDC,aAAa,EAAE;MACbC,IAAI,WAAJA,IAAIA,CAAEhU,IAAI,EAAE;QACV,IAAI,CAACzB,GAAG,IAAI,IAAI,CAACA,GAAG,CAACiI,WAAW,CAAC,YAAY,EAAExG,IAAI,CAAC;MACtD,CAAC;MACDiU,IAAI,WAAJA,IAAIA,CAAA,EAAI;QACN,IAAI,CAAC1V,GAAG,IAAI,IAAI,CAACA,GAAG,CAACiI,WAAW,CAAC,YAAY,CAAC;MAChD,CAAC;MACD0N,MAAM,WAANA,MAAMA,CAAEC,IAAI,EAAE;QACZ,IAAI,CAAC5V,GAAG,IAAI,IAAI,CAACA,GAAG,CAACiI,WAAW,CAAC,cAAc,EAAE2N,IAAI,CAAC;MACxD;IACF,CAAC;IACD5M,OAAO,EAAE;MACP6M,GAAG,EAAEpC,UAAU;MACfqC,GAAG,EAAEjI;IACP;EACF,CAAC;EACD;EACA,IAAIpG,UAAU,CAACsO,eAAe,EAAE;IAC9Bf,gBAAgB,CAACe,eAAe,GAAGtO,UAAU,CAACsO,eAAe;EAC/D;EAEA,IAAInhB,KAAK,CAACC,OAAO,CAAC4S,UAAU,CAACuO,cAAc,CAAC,EAAE;IAC5CvO,UAAU,CAACuO,cAAc,CAAC5iB,OAAO,CAAC,UAAA6iB,UAAU,EAAI;MAC9CjB,gBAAgB,CAAChM,OAAO,CAACiN,UAAU,CAAC,GAAG,UAAUxU,IAAI,EAAE;QACrD,OAAO,IAAI,CAACzB,GAAG,CAACiW,UAAU,CAAC,CAACxU,IAAI,CAAC;MACnC,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,IAAIoR,MAAM,EAAE;IACV,OAAOmC,gBAAgB;EACzB;EACA,OAAO,CAACA,gBAAgB,EAAE7M,YAAY,CAAC;AACzC;AAEA,SAAS+N,cAAcA,CAAExB,mBAAmB,EAAE;EAC5C,OAAOD,kBAAkB,CAACC,mBAAmB,EAAE;IAC7C7B,MAAM,EAANA,MAAM;IACNE,YAAY,EAAZA;EACF,CAAC,CAAC;AACJ;AAEA,IAAMoD,OAAO,GAAG,CACd,QAAQ,EACR,QAAQ,EACR,UAAU,CACX;AAEDA,OAAO,CAACpjB,IAAI,CAAAC,KAAA,CAAZmjB,OAAO,EAASlP,gBAAgB,CAAC;AAEjC,SAASmP,aAAaA,CAAEC,cAAc,EAAAC,KAAA,EAGnC;EAAA,IAFDzD,MAAM,GAAAyD,KAAA,CAANzD,MAAM;IACNE,YAAY,GAAAuD,KAAA,CAAZvD,YAAY;EAEZ,IAAMwD,WAAW,GAAGL,cAAc,CAACG,cAAc,CAAC;EAElDtO,SAAS,CAACwO,WAAW,CAACvN,OAAO,EAAEmN,OAAO,EAAEE,cAAc,CAAC;EAEvDE,WAAW,CAACvN,OAAO,CAACwN,MAAM,GAAG,UAAUC,KAAK,EAAE;IAC5C,IAAI,CAACpa,OAAO,GAAGoa,KAAK;IACpB,IAAMC,SAAS,GAAGlkB,MAAM,CAACmM,MAAM,CAAC,CAAC,CAAC,EAAE8X,KAAK,CAAC;IAC1C,OAAOC,SAAS,CAAC5F,MAAM;IACvB,IAAI,CAAC3P,KAAK,GAAG;MACXC,QAAQ,EAAE,GAAG,IAAI,IAAI,CAAC0R,KAAK,IAAI,IAAI,CAAC6D,EAAE,CAAC,GAAGvC,cAAc,CAACsC,SAAS;IACpE,CAAC;IACD,IAAI,CAAC1W,GAAG,CAACqH,GAAG,CAACoP,KAAK,GAAGA,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACzW,GAAG,CAACiI,WAAW,CAAC,QAAQ,EAAEwO,KAAK,CAAC;EACvC,CAAC;EAED,OAAOF,WAAW;AACpB;AAEA,SAASK,SAASA,CAAEP,cAAc,EAAE;EAClC,OAAOD,aAAa,CAACC,cAAc,EAAE;IACnCxD,MAAM,EAANA,MAAM;IACNE,YAAY,EAAZA;EACF,CAAC,CAAC;AACJ;AAEA,SAAS8D,UAAUA,CAAER,cAAc,EAAE;EACnC;IACE,OAAOrQ,SAAS,CAAC4Q,SAAS,CAACP,cAAc,CAAC,CAAC;EAC7C;AACF;AAEA,SAASS,eAAeA,CAAErP,UAAU,EAAE;EACpC;IACE,OAAOzB,SAAS,CAACkQ,cAAc,CAACzO,UAAU,CAAC,CAAC;EAC9C;AACF;AAEA,SAASsP,mBAAmBA,CAAE5P,EAAE,EAAE;EAChC,IAAM+K,UAAU,GAAG0B,QAAQ,CAACzM,EAAE,CAAC;EAC/B,IAAMtH,GAAG,GAAGC,MAAM,CAAC;IACjBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACFoH,EAAE,CAACyG,MAAM,GAAG/N,GAAG;EACf,IAAMwS,UAAU,GAAGxS,GAAG,CAACwS,UAAU;EACjC,IAAIA,UAAU,EAAE;IACd7f,MAAM,CAACC,IAAI,CAACyf,UAAU,CAACG,UAAU,CAAC,CAACjf,OAAO,CAAC,UAAA0C,IAAI,EAAI;MACjD,IAAI,CAAC4D,MAAM,CAAC2Y,UAAU,EAAEvc,IAAI,CAAC,EAAE;QAC7Buc,UAAU,CAACvc,IAAI,CAAC,GAAGoc,UAAU,CAACG,UAAU,CAACvc,IAAI,CAAC;MAChD;IACF,CAAC,CAAC;EACJ;EACAtD,MAAM,CAACC,IAAI,CAACyf,UAAU,CAAC,CAAC9e,OAAO,CAAC,UAAA0C,IAAI,EAAI;IACtC,IAAI,CAAC4D,MAAM,CAACmG,GAAG,EAAE/J,IAAI,CAAC,EAAE;MACtB+J,GAAG,CAAC/J,IAAI,CAAC,GAAGoc,UAAU,CAACpc,IAAI,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,IAAIuD,IAAI,CAAC6Y,UAAU,CAAC8E,MAAM,CAAC,IAAIvf,EAAE,CAACwf,SAAS,EAAE;IAC3Cxf,EAAE,CAACwf,SAAS,CAAC,YAAa;MAAA,SAAAC,KAAA,GAAAhkB,SAAA,CAAAC,MAAA,EAATsO,IAAI,OAAA7M,KAAA,CAAAsiB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJ1V,IAAI,CAAA0V,KAAA,IAAAjkB,SAAA,CAAAikB,KAAA;MAAA;MACnBhQ,EAAE,CAACc,WAAW,CAAC,QAAQ,EAAExG,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ;EACA,IAAIpI,IAAI,CAAC6Y,UAAU,CAACkF,MAAM,CAAC,IAAI3f,EAAE,CAAC4f,SAAS,EAAE;IAC3C5f,EAAE,CAAC4f,SAAS,CAAC,YAAa;MAAA,SAAAC,KAAA,GAAApkB,SAAA,CAAAC,MAAA,EAATsO,IAAI,OAAA7M,KAAA,CAAA0iB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJ9V,IAAI,CAAA8V,KAAA,IAAArkB,SAAA,CAAAqkB,KAAA;MAAA;MACnBpQ,EAAE,CAACc,WAAW,CAAC,QAAQ,EAAExG,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ;EACA,IAAIpI,IAAI,CAAC6Y,UAAU,CAACC,QAAQ,CAAC,EAAE;IAC7B,IAAM1Q,IAAI,GAAGhK,EAAE,CAAC+f,oBAAoB,IAAI/f,EAAE,CAAC+f,oBAAoB,CAAC,CAAC;IACjErQ,EAAE,CAACc,WAAW,CAAC,UAAU,EAAExG,IAAI,CAAC;EAClC;EACA,OAAO0F,EAAE;AACX;AAEA,SAASsQ,YAAYA,CAAEtQ,EAAE,EAAE;EACzB,IAAM+K,UAAU,GAAG0B,QAAQ,CAACzM,EAAE,CAAC;EAC/B,IAAI9N,IAAI,CAAC6Y,UAAU,CAAC8E,MAAM,CAAC,IAAIvf,EAAE,CAACwf,SAAS,EAAE;IAC3Cxf,EAAE,CAACwf,SAAS,CAAC,YAAa;MAAA,SAAAS,KAAA,GAAAxkB,SAAA,CAAAC,MAAA,EAATsO,IAAI,OAAA7M,KAAA,CAAA8iB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJlW,IAAI,CAAAkW,KAAA,IAAAzkB,SAAA,CAAAykB,KAAA;MAAA;MACnBzF,UAAU,CAAC8E,MAAM,CAAChkB,KAAK,CAACmU,EAAE,EAAE1F,IAAI,CAAC;IACnC,CAAC,CAAC;EACJ;EACA,IAAIpI,IAAI,CAAC6Y,UAAU,CAACkF,MAAM,CAAC,IAAI3f,EAAE,CAAC4f,SAAS,EAAE;IAC3C5f,EAAE,CAAC4f,SAAS,CAAC,YAAa;MAAA,SAAAO,KAAA,GAAA1kB,SAAA,CAAAC,MAAA,EAATsO,IAAI,OAAA7M,KAAA,CAAAgjB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJpW,IAAI,CAAAoW,KAAA,IAAA3kB,SAAA,CAAA2kB,KAAA;MAAA;MACnB3F,UAAU,CAACkF,MAAM,CAACpkB,KAAK,CAACmU,EAAE,EAAE1F,IAAI,CAAC;IACnC,CAAC,CAAC;EACJ;EACA,IAAIpI,IAAI,CAAC6Y,UAAU,CAACC,QAAQ,CAAC,EAAE;IAC7B,IAAM1Q,IAAI,GAAGhK,EAAE,CAAC+f,oBAAoB,IAAI/f,EAAE,CAAC+f,oBAAoB,CAAC,CAAC;IACjEtF,UAAU,CAACC,QAAQ,CAAC5d,IAAI,CAAC4S,EAAE,EAAE1F,IAAI,CAAC;EACpC;EACA,OAAO0F,EAAE;AACX;AAEAhE,KAAK,CAAC/P,OAAO,CAAC,UAAAoR,OAAO,EAAI;EACvBtB,SAAS,CAACsB,OAAO,CAAC,GAAG,KAAK;AAC5B,CAAC,CAAC;AAEFpB,QAAQ,CAAChQ,OAAO,CAAC,UAAA0kB,UAAU,EAAI;EAC7B,IAAMC,OAAO,GAAG7U,SAAS,CAAC4U,UAAU,CAAC,IAAI5U,SAAS,CAAC4U,UAAU,CAAC,CAAChiB,IAAI,GAAGoN,SAAS,CAAC4U,UAAU,CAAC,CAAChiB,IAAI,GAC5FgiB,UAAU;EACd,IAAI,CAACrgB,EAAE,CAAC2a,OAAO,CAAC2F,OAAO,CAAC,EAAE;IACxB7U,SAAS,CAAC4U,UAAU,CAAC,GAAG,KAAK;EAC/B;AACF,CAAC,CAAC;AAEF,IAAIE,GAAG,GAAG,CAAC,CAAC;AAEZ,IAAI,OAAOC,KAAK,KAAK,WAAW,IAAI,WAAW,KAAK,UAAU,EAAE;EAC9DD,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,EAAE;IAClBhI,GAAG,WAAHA,GAAGA,CAAE3E,MAAM,EAAExV,IAAI,EAAE;MACjB,IAAI4D,MAAM,CAAC4R,MAAM,EAAExV,IAAI,CAAC,EAAE;QACxB,OAAOwV,MAAM,CAACxV,IAAI,CAAC;MACrB;MACA,IAAI4K,OAAO,CAAC5K,IAAI,CAAC,EAAE;QACjB,OAAO4K,OAAO,CAAC5K,IAAI,CAAC;MACtB;MACA,IAAIiH,GAAG,CAACjH,IAAI,CAAC,EAAE;QACb,OAAOsI,SAAS,CAACtI,IAAI,EAAEiH,GAAG,CAACjH,IAAI,CAAC,CAAC;MACnC;MACA;QACE,IAAIqP,QAAQ,CAACrP,IAAI,CAAC,EAAE;UAClB,OAAOsI,SAAS,CAACtI,IAAI,EAAEqP,QAAQ,CAACrP,IAAI,CAAC,CAAC;QACxC;QACA,IAAIuO,QAAQ,CAACvO,IAAI,CAAC,EAAE;UAClB,OAAOsI,SAAS,CAACtI,IAAI,EAAEuO,QAAQ,CAACvO,IAAI,CAAC,CAAC;QACxC;MACF;MACA,IAAI8P,QAAQ,CAAC9P,IAAI,CAAC,EAAE;QAClB,OAAO8P,QAAQ,CAAC9P,IAAI,CAAC;MACvB;MACA,IAAI,CAAC4D,MAAM,CAACjC,EAAE,EAAE3B,IAAI,CAAC,IAAI,CAAC4D,MAAM,CAACwJ,SAAS,EAAEpN,IAAI,CAAC,EAAE;QACjD;MACF;MACA,OAAOsI,SAAS,CAACtI,IAAI,EAAEmO,OAAO,CAACnO,IAAI,EAAE2B,EAAE,CAAC3B,IAAI,CAAC,CAAC,CAAC;IACjD,CAAC;IACDoa,GAAG,WAAHA,GAAGA,CAAE5E,MAAM,EAAExV,IAAI,EAAEpB,KAAK,EAAE;MACxB4W,MAAM,CAACxV,IAAI,CAAC,GAAGpB,KAAK;MACpB,OAAO,IAAI;IACb;EACF,CAAC,CAAC;AACJ,CAAC,MAAM;EACLlC,MAAM,CAACC,IAAI,CAACiO,OAAO,CAAC,CAACtN,OAAO,CAAC,UAAA0C,IAAI,EAAI;IACnCkiB,GAAG,CAACliB,IAAI,CAAC,GAAG4K,OAAO,CAAC5K,IAAI,CAAC;EAC3B,CAAC,CAAC;EAEF;IACEtD,MAAM,CAACC,IAAI,CAAC4R,QAAQ,CAAC,CAACjR,OAAO,CAAC,UAAA0C,IAAI,EAAI;MACpCkiB,GAAG,CAACliB,IAAI,CAAC,GAAGsI,SAAS,CAACtI,IAAI,EAAEuO,QAAQ,CAACvO,IAAI,CAAC,CAAC;IAC7C,CAAC,CAAC;IACFtD,MAAM,CAACC,IAAI,CAAC0S,QAAQ,CAAC,CAAC/R,OAAO,CAAC,UAAA0C,IAAI,EAAI;MACpCkiB,GAAG,CAACliB,IAAI,CAAC,GAAGsI,SAAS,CAACtI,IAAI,EAAEuO,QAAQ,CAACvO,IAAI,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ;EAEAtD,MAAM,CAACC,IAAI,CAACmT,QAAQ,CAAC,CAACxS,OAAO,CAAC,UAAA0C,IAAI,EAAI;IACpCkiB,GAAG,CAACliB,IAAI,CAAC,GAAG8P,QAAQ,CAAC9P,IAAI,CAAC;EAC5B,CAAC,CAAC;EAEFtD,MAAM,CAACC,IAAI,CAACsK,GAAG,CAAC,CAAC3J,OAAO,CAAC,UAAA0C,IAAI,EAAI;IAC/BkiB,GAAG,CAACliB,IAAI,CAAC,GAAGsI,SAAS,CAACtI,IAAI,EAAEiH,GAAG,CAACjH,IAAI,CAAC,CAAC;EACxC,CAAC,CAAC;EAEFtD,MAAM,CAACC,IAAI,CAACgF,EAAE,CAAC,CAACrE,OAAO,CAAC,UAAA0C,IAAI,EAAI;IAC9B,IAAI4D,MAAM,CAACjC,EAAE,EAAE3B,IAAI,CAAC,IAAI4D,MAAM,CAACwJ,SAAS,EAAEpN,IAAI,CAAC,EAAE;MAC/CkiB,GAAG,CAACliB,IAAI,CAAC,GAAGsI,SAAS,CAACtI,IAAI,EAAEmO,OAAO,CAACnO,IAAI,EAAE2B,EAAE,CAAC3B,IAAI,CAAC,CAAC,CAAC;IACtD;EACF,CAAC,CAAC;AACJ;AAEA2B,EAAE,CAACoc,SAAS,GAAGA,SAAS;AACxBpc,EAAE,CAACof,UAAU,GAAGA,UAAU;AAC1Bpf,EAAE,CAACqf,eAAe,GAAGA,eAAe;AACpCrf,EAAE,CAACsf,mBAAmB,GAAGA,mBAAmB;AAC5Ctf,EAAE,CAACggB,YAAY,GAAGA,YAAY;AAE9B,IAAIS,KAAK,GAAGF,GAAG;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAhmB,OAAA,GAED8lB,KAAK,C;;;;;;;;;;;;;ACxhEpB;AAAA;AAAA;;AAEA;AACA;AACA;;AAEe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,qBAAqB;AACrB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AClHA;AACA;AACA;AACA;;AAEO,IAAMG,UAAU,GAAAD,OAAA,CAAAC,UAAA,GAAG;EACxB;EACA;EACAC,OAAO,EAAE;IACPC,GAAG,EAAE,qCAAqC;IAAE;IAC5CC,YAAY,EAAE;EAChB,CAAC;EAED;EACA;EACAC,IAAI,EAAE;IACJF,GAAG,EAAE,eAAe;IAAE;IACtBC,YAAY,EAAE;EAChB,CAAC;EAED;EACA;EACAE,KAAK,EAAE;IACLH,GAAG,EAAE,oBAAoB;IAAE;IAC3BC,YAAY,EAAE;EAChB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACO,SAASG,kBAAkBA,CAACzT,QAAQ,EAAE;EAC3C,IAAM0T,MAAM,GAAGP,UAAU,CAACnT,QAAQ,CAAC;EACnC,OAAO0T,MAAM,IAAIA,MAAM,CAACL,GAAG,IAAI,CAACK,MAAM,CAACL,GAAG,CAACM,UAAU,CAAC,OAAO,CAAC;AAChE;;AAEA;AACA;AACA;AACA;AACO,SAASC,qBAAqBA,CAAA,EAAG;EACtC,OAAOtmB,MAAM,CAACC,IAAI,CAAC4lB,UAAU,CAAC,CAACzlB,MAAM,CAAC,UAAAsS,QAAQ;IAAA,OAAIyT,kBAAkB,CAACzT,QAAQ,CAAC;EAAA,EAAC;AACjF,C;;;;;;;;;;;;;;;AC3CO;AACP;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;ACxoBA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;;;;;;;ACnBA;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,kCAAkC;;AAElC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;AACA,sBAAsB,+BAA+B;AACrD,sBAAsB,iBAAiB;AACvC;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,kDAAkD,iCAAiC,EAAE;AACrF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,6BAA6B,cAAc;;AAE3C;;AAEA;AACA;AACA;AACA,6BAA6B,UAAU;;AAEvC;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,gBAAgB;AACjC,kCAAkC;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB,aAAoB;;AAErC;AACA;AACA;AACA,YAAY,aAAoB;;AAEhC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,qBAAqB;AACxC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;AACR;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;AACA,oCAAoC;AACpC;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA,iCAAiC;AACjC,uCAAuC,wBAAwB,EAAE;AACjE,0BAA0B;;AAE1B;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,YAAY;AACpC,kBAAkB,YAAY;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA,wCAAwC,EAAE;AAC1C;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,+BAA+B,oBAAoB,EAAE;AACrD;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0BAA0B,SAAS,qBAAqB;;AAExD;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,2BAA2B;AAC9C;AACA;AACA;AACA,GAAG;AACH,CAAC;;AAED;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mCAAmC,OAAO;AAC1C;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA;;AAEA,iBAAiB,iBAAiB;AAClC;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,uBAAuB;AACzD,iCAAiC,sBAAsB;AACvD;AACA,kBAAkB;AAClB,MAAM,IAAqC;AAC3C;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,aAAoB;AACtC;AACA;AACA,mBAAmB;AACnB;AACA;AACA,iBAAiB,uBAAuB;AACxC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,OAAO,UAAU,IAAqC;AACtD;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,GAAG,UAAU,IAAqC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,mBAAmB,mBAAmB;AACtC,+BAA+B;AAC/B;AACA,GAAG;AACH;AACA;AACA;AACA,kBAAkB,YAAY;AAC9B,WAAW;AACX;AACA,GAAG,UAAU,IAAqC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO;AACrD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA,oCAAoC;AACpC;AACA,qCAAqC;AACrC;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAEQ;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,2BAA2B;AAC9C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,6CAA6C,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,6CAA6C,qCAAqC,EAAE;AACpF;;AAEA;AACA;AACA;;AAEA,oCAAoC,yCAAyC,EAAE;AAC/E;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,kBAAkB;AAC3C;AACA;AACA,4BAA4B;AAC5B,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,sDAAsD,EAAE;AACtF;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB,mBAAmB;AACpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,kBAAkB;AAClC;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;;AAEA;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,iCAAiC;AACnE,cAAc,6BAA6B;AAC3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,kCAAkC,iCAAiC;AACnE,cAAc,6BAA6B;AAC3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,yBAAyB;AAC1C,GAAG;AACH;AACA;AACA,iBAAiB,+BAA+B;AAChD;AACA;;AAEA;AACA;;AAEA,IAAI,IAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,uBAAuB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB,mBAAmB;AACxC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,IAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,qBAAqB;AACtC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAa,qBAAqB;AAClC;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,IAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO,MAAM,EAEN;AACP,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,mBAAmB,iBAAiB;AACpC;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,IAAqC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,OAAO;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA,sBAAsB,mBAAmB;AACzC;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,OAAO;AACtC,uCAAuC;AACvC;AACA,GAAG;AACH;AACA,eAAe,SAAS;AACxB,sCAAsC;AACtC;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA;AACA,KAAK;AACL;AACA;AACA,kCAAkC,OAAO;AACzC;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA,4CAA4C,eAAe;AAC3D,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,kDAAkD;AAClD,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA,KAAK;AACL,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,iBAAiB,mBAAmB;AACpC;AACA;AACA;AACA,KAAK,UAAU,KAAqC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,qCAAqC,gEAAgE;AACrG;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,4BAA4B,+BAA+B;AAC3D,4BAA4B,+BAA+B;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA,mBAAmB,mBAAmB;AACtC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C,kDAAkD;AAClD;AACA;AACA,mCAAmC;AACnC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,sEAAsE;;AAEtE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,uFAAuF;AAC5F;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0CAA0C;AAC1C,iBAAiB,yBAAyB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG,+BAA+B;AAClC,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,oBAAoB,oBAAoB;AACxC,sBAAsB,4BAA4B;AAClD;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,mBAAmB;AACnB,yBAAyB;AACzB;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,6CAA6C;AAC9E;AACA;AACA,6CAA6C,4CAA4C;;AAEzF;AACA;AACA;;AAEA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL,GAAG,MAAM,EAGN;AACH;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,KAAK,2CAA2C,8BAA8B,EAAE;;AAEhF;AACA,wCAAwC,OAAO;AAC/C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;;AAEL;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB,KAAqC;AACrD;AACA,oBAAoB,SAAI;AACxB;AACA;AACA,WAAW;AACX;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,mBAAmB,qBAAqB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,uCAAuC,OAAO;AAC9C;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,SAAS;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,OAAO;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;;AAE1B,kBAAkB;AAClB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,mBAAmB,qBAAqB;AACxC;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAM,IAAqC;AAC3C;AACA;AACA;;AAEA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,mBAAmB,yBAAyB;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,yBAAyB;AAC5C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,OAAO;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,0BAA0B;AACpD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,oBAAoB,EAAE;;AAEpD;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB,kBAAkB;AACnC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,UAAU,KAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,oBAAoB;AACpB;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,oBAAoB,KAAqC;AACzD;AACA,MAAM,SAAE;AACR;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mBAAmB,2BAA2B;AAC9C,qBAAqB,+BAA+B;AACpD;AACA;AACA,GAAG;AACH,yBAAyB;AACzB;AACA,sBAAsB,iCAAiC;AACvD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK,MAAM,EAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAqC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA,8BAA8B;;AAE9B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,UAAU,IAAqC;AACpD;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qBAAqB,oBAAoB;AACzC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,8BAA8B;AAC9B,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA,KAAK,MAAM,EAEN;AACL;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA,sCAAsC;AACtC,8C;;AAEA;AACA,QAAQ,KAAqC;AAC7C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,eAAe;AACrC;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAM,KAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE;AACtE;AACA;AACA;;AAEA;AACA,QAAQ,KAAqC;AAC7C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iCAAiC;;AAEjC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,YAAY,KAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA,0CAA0C,2BAA2B,EAAE;AACvE,KAAK;AACL;AACA,0CAA0C,4BAA4B,EAAE;AACxE,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,+BAA+B,eAAe;AAC9C,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,yBAAyB;AACzB;AACA;AACA,6BAA6B;AAC7B;AACA;AACA,iBAAiB;AACjB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA,SAAS;AACT;AACA;AACA,aAAa;AACb;AACA;AACA,iBAAiB;AACjB;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,YAAY,sGAAW;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,mBAAmB;AAC1C;AACA;AACA;AACA;;AAEA;AACA,0CAA0C,gCAAgC,EAAE;AAC5E;;AAEA;AACA;AACA;AACA;AACA,WAAW,sGAAW;AACtB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,WAAW,sGAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,0CAA0C;;AAE1C;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA,sCAAsC;AACtC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,8CAA8C;AAC9C;AACA,KAAK;AACL;AACA;AACA,UAAU,sGAAW;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mCAAmC,OAAO;AAC1C;AACA,gBAAgB,YAAY;AAC5B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gBAAgB,YAAY;AAC5B;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,OAAO;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,qDAAqD,EAAE,SAAS;AACtH;;AAEA;AACA;AACA;AACA;AACA;AACA,iCAAiC,OAAO;AACxC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,kCAAkC,OAAO;AACzC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,0BAA0B,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEe,kEAAG,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC75LnB,IAAMrQ,OAAO,GAAGD,KAAK,CAACC,OAAO;AAC7B,IAAMkkB,QAAQ,GAAG,SAAXA,QAAQA,CAAIzE,GAAG;EAAA,OAAKA,GAAG,KAAK,IAAI,IAAIpf,OAAA,CAAOof,GAAG,MAAK,QAAQ;AAAA;AACjE,IAAM0E,iBAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAAC,IAC/BC,aAAa,GAAAb,OAAA,CAAAc,SAAA;EACf,SAAAD,cAAA,EAAc;IAAAE,eAAA,OAAAF,aAAA;IACV,IAAI,CAACG,OAAO,GAAG5mB,MAAM,CAACuH,MAAM,CAAC,IAAI,CAAC;EACtC;EAAC,OAAAsf,YAAA,CAAAJ,aAAA;IAAAtf,GAAA;IAAAjF,KAAA,EACD,SAAA4kB,WAAWA,CAAClhB,OAAO,EAAEoX,MAAM,EAAkC;MAAA,IAAhC+J,UAAU,GAAArmB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG8lB,iBAAiB;MACvD,IAAI,CAACxJ,MAAM,EAAE;QACT,OAAO,CAACpX,OAAO,CAAC;MACpB;MACA,IAAIohB,MAAM,GAAG,IAAI,CAACJ,OAAO,CAAChhB,OAAO,CAAC;MAClC,IAAI,CAACohB,MAAM,EAAE;QACTA,MAAM,GAAGthB,KAAK,CAACE,OAAO,EAAEmhB,UAAU,CAAC;QACnC,IAAI,CAACH,OAAO,CAAChhB,OAAO,CAAC,GAAGohB,MAAM;MAClC;MACA,OAAOC,OAAO,CAACD,MAAM,EAAEhK,MAAM,CAAC;IAClC;EAAC;AAAA;AAEL,IAAMkK,mBAAmB,GAAG,UAAU;AACtC,IAAMC,oBAAoB,GAAG,UAAU;AACvC,SAASzhB,KAAKA,CAAC0hB,MAAM,EAAAnV,IAAA,EAAkC;EAAA,IAAAO,KAAA,GAAAvR,cAAA,CAAAgR,IAAA;IAA/BoV,cAAc,GAAA7U,KAAA;IAAE8U,YAAY,GAAA9U,KAAA;EAChD,IAAMwU,MAAM,GAAG,EAAE;EACjB,IAAIO,QAAQ,GAAG,CAAC;EAChB,IAAIC,IAAI,GAAG,EAAE;EACb,OAAOD,QAAQ,GAAGH,MAAM,CAACzmB,MAAM,EAAE;IAC7B,IAAI8mB,KAAI,GAAGL,MAAM,CAACG,QAAQ,EAAE,CAAC;IAC7B,IAAIE,KAAI,KAAKJ,cAAc,EAAE;MACzB,IAAIG,IAAI,EAAE;QACNR,MAAM,CAACzmB,IAAI,CAAC;UAAEsX,IAAI,EAAE,MAAM;UAAE3V,KAAK,EAAEslB;QAAK,CAAC,CAAC;MAC9C;MACAA,IAAI,GAAG,EAAE;MACT,IAAIE,GAAG,GAAG,EAAE;MACZD,KAAI,GAAGL,MAAM,CAACG,QAAQ,EAAE,CAAC;MACzB,OAAOE,KAAI,KAAK3d,SAAS,IAAI2d,KAAI,KAAKH,YAAY,EAAE;QAChDI,GAAG,IAAID,KAAI;QACXA,KAAI,GAAGL,MAAM,CAACG,QAAQ,EAAE,CAAC;MAC7B;MACA,IAAMI,QAAQ,GAAGF,KAAI,KAAKH,YAAY;MACtC,IAAMzP,IAAI,GAAGqP,mBAAmB,CAAC1jB,IAAI,CAACkkB,GAAG,CAAC,GACpC,MAAM,GACNC,QAAQ,IAAIR,oBAAoB,CAAC3jB,IAAI,CAACkkB,GAAG,CAAC,GACtC,OAAO,GACP,SAAS;MACnBV,MAAM,CAACzmB,IAAI,CAAC;QAAE2B,KAAK,EAAEwlB,GAAG;QAAE7P,IAAI,EAAJA;MAAK,CAAC,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IAAA,KACK;MACD2P,IAAI,IAAIC,KAAI;IAChB;EACJ;EACAD,IAAI,IAAIR,MAAM,CAACzmB,IAAI,CAAC;IAAEsX,IAAI,EAAE,MAAM;IAAE3V,KAAK,EAAEslB;EAAK,CAAC,CAAC;EAClD,OAAOR,MAAM;AACjB;AACA,SAASC,OAAOA,CAACD,MAAM,EAAEhK,MAAM,EAAE;EAC7B,IAAM4K,QAAQ,GAAG,EAAE;EACnB,IAAIlf,KAAK,GAAG,CAAC;EACb,IAAMmf,IAAI,GAAGxlB,OAAO,CAAC2a,MAAM,CAAC,GACtB,MAAM,GACNuJ,QAAQ,CAACvJ,MAAM,CAAC,GACZ,OAAO,GACP,SAAS;EACnB,IAAI6K,IAAI,KAAK,SAAS,EAAE;IACpB,OAAOD,QAAQ;EACnB;EACA,OAAOlf,KAAK,GAAGse,MAAM,CAACrmB,MAAM,EAAE;IAC1B,IAAMqE,KAAK,GAAGgiB,MAAM,CAACte,KAAK,CAAC;IAC3B,QAAQ1D,KAAK,CAAC6S,IAAI;MACd,KAAK,MAAM;QACP+P,QAAQ,CAACrnB,IAAI,CAACyE,KAAK,CAAC9C,KAAK,CAAC;QAC1B;MACJ,KAAK,MAAM;QACP0lB,QAAQ,CAACrnB,IAAI,CAACyc,MAAM,CAAC3N,QAAQ,CAACrK,KAAK,CAAC9C,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;QAChD;MACJ,KAAK,OAAO;QACR,IAAI2lB,IAAI,KAAK,OAAO,EAAE;UAClBD,QAAQ,CAACrnB,IAAI,CAACyc,MAAM,CAAChY,KAAK,CAAC9C,KAAK,CAAC,CAAC;QACtC,CAAC,MACI;UACD,IAAIuU,IAAqC,EAAE;YACvCnF,OAAO,CAACC,IAAI,mBAAAlJ,MAAA,CAAmBrD,KAAK,CAAC6S,IAAI,6BAAAxP,MAAA,CAA0Bwf,IAAI,mBAAgB,CAAC;UAC5F;QACJ;QACA;MACJ,KAAK,SAAS;QACV,IAAIpR,IAAqC,EAAE;UACvCnF,OAAO,CAACC,IAAI,kCAAkC,CAAC;QACnD;QACA;IACR;IACA7I,KAAK,EAAE;EACX;EACA,OAAOkf,QAAQ;AACnB;AAEA,IAAME,cAAc,GAAAlC,OAAA,CAAAkC,cAAA,GAAG,SAAS;AAChC,IAAMC,cAAc,GAAAnC,OAAA,CAAAmC,cAAA,GAAG,SAAS;AAChC,IAAMC,SAAS,GAAApC,OAAA,CAAAoC,SAAA,GAAG,IAAI;AACtB,IAAMC,SAAS,GAAArC,OAAA,CAAAqC,SAAA,GAAG,IAAI;AACtB,IAAMC,SAAS,GAAAtC,OAAA,CAAAsC,SAAA,GAAG,IAAI;AACtB,IAAMthB,cAAc,GAAG5G,MAAM,CAACyD,SAAS,CAACmD,cAAc;AACtD,IAAMM,MAAM,GAAG,SAATA,MAAMA,CAAI4a,GAAG,EAAE3a,GAAG;EAAA,OAAKP,cAAc,CAAC7E,IAAI,CAAC+f,GAAG,EAAE3a,GAAG,CAAC;AAAA;AAC1D,IAAMghB,gBAAgB,GAAG,IAAI1B,aAAa,CAAC,CAAC;AAC5C,SAAS2B,OAAOA,CAACtkB,GAAG,EAAEukB,KAAK,EAAE;EACzB,OAAO,CAAC,CAACA,KAAK,CAAChT,IAAI,CAAC,UAACiT,IAAI;IAAA,OAAKxkB,GAAG,CAACO,OAAO,CAACikB,IAAI,CAAC,KAAK,CAAC,CAAC;EAAA,EAAC;AAC3D;AACA,SAASjC,UAAUA,CAACviB,GAAG,EAAEukB,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAAChT,IAAI,CAAC,UAACiT,IAAI;IAAA,OAAKxkB,GAAG,CAACO,OAAO,CAACikB,IAAI,CAAC,KAAK,CAAC;EAAA,EAAC;AACxD;AACA,SAASC,eAAeA,CAAC3a,MAAM,EAAEmO,QAAQ,EAAE;EACvC,IAAI,CAACnO,MAAM,EAAE;IACT;EACJ;EACAA,MAAM,GAAGA,MAAM,CAAC4a,IAAI,CAAC,CAAC,CAACzkB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACzC,IAAIgY,QAAQ,IAAIA,QAAQ,CAACnO,MAAM,CAAC,EAAE;IAC9B,OAAOA,MAAM;EACjB;EACAA,MAAM,GAAGA,MAAM,CAAC6a,WAAW,CAAC,CAAC;EAC7B,IAAI7a,MAAM,CAACvJ,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IAC5B,IAAIuJ,MAAM,CAACvJ,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9B,OAAOyjB,cAAc;IACzB;IACA,IAAIla,MAAM,CAACvJ,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9B,OAAO0jB,cAAc;IACzB;IACA,IAAIK,OAAO,CAACxa,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE;MAChD,OAAOma,cAAc;IACzB;IACA,OAAOD,cAAc;EACzB;EACA,IAAMY,IAAI,GAAGrC,UAAU,CAACzY,MAAM,EAAE,CAACoa,SAAS,EAAEC,SAAS,EAAEC,SAAS,CAAC,CAAC;EAClE,IAAIQ,IAAI,EAAE;IACN,OAAOA,IAAI;EACf;AACJ;AAAC,IACKC,IAAI,GAAA/C,OAAA,CAAA+C,IAAA;EACN,SAAAA,KAAAxJ,KAAA,EAAsE;IAAA,IAAxDvR,MAAM,GAAAuR,KAAA,CAANvR,MAAM;MAAEgb,cAAc,GAAAzJ,KAAA,CAAdyJ,cAAc;MAAE7M,QAAQ,GAAAoD,KAAA,CAARpD,QAAQ;MAAE8M,OAAO,GAAA1J,KAAA,CAAP0J,OAAO;MAAEC,QAAQ,GAAA3J,KAAA,CAAR2J,QAAQ;IAAAnC,eAAA,OAAAgC,IAAA;IAC7D,IAAI,CAAC/a,MAAM,GAAGoa,SAAS;IACvB,IAAI,CAACY,cAAc,GAAGZ,SAAS;IAC/B,IAAI,CAACpiB,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACmW,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACgN,QAAQ,GAAG,EAAE;IAClB,IAAIH,cAAc,EAAE;MAChB,IAAI,CAACA,cAAc,GAAGA,cAAc;IACxC;IACA,IAAI,CAACE,QAAQ,GAAGA,QAAQ,IAAIX,gBAAgB;IAC5C,IAAI,CAACpM,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC;IAC9B,IAAI,CAACpO,SAAS,CAACC,MAAM,IAAIoa,SAAS,CAAC;IACnC,IAAIa,OAAO,EAAE;MACT,IAAI,CAAChM,WAAW,CAACgM,OAAO,CAAC;IAC7B;EACJ;EAAC,OAAAhC,YAAA,CAAA8B,IAAA;IAAAxhB,GAAA;IAAAjF,KAAA,EACD,SAAAyL,SAASA,CAACC,MAAM,EAAE;MAAA,IAAA0N,KAAA;MACd,IAAMzN,SAAS,GAAG,IAAI,CAACD,MAAM;MAC7B,IAAI,CAACA,MAAM,GAAG2a,eAAe,CAAC3a,MAAM,EAAE,IAAI,CAACmO,QAAQ,CAAC,IAAI,IAAI,CAAC6M,cAAc;MAC3E,IAAI,CAAC,IAAI,CAAC7M,QAAQ,CAAC,IAAI,CAACnO,MAAM,CAAC,EAAE;QAC7B;QACA,IAAI,CAACmO,QAAQ,CAAC,IAAI,CAACnO,MAAM,CAAC,GAAG,CAAC,CAAC;MACnC;MACA,IAAI,CAAChI,OAAO,GAAG,IAAI,CAACmW,QAAQ,CAAC,IAAI,CAACnO,MAAM,CAAC;MACzC;MACA,IAAIC,SAAS,KAAK,IAAI,CAACD,MAAM,EAAE;QAC3B,IAAI,CAACmb,QAAQ,CAACnoB,OAAO,CAAC,UAACioB,OAAO,EAAK;UAC/BA,OAAO,CAACvN,KAAI,CAAC1N,MAAM,EAAEC,SAAS,CAAC;QACnC,CAAC,CAAC;MACN;IACJ;EAAC;IAAA1G,GAAA;IAAAjF,KAAA,EACD,SAAAkL,SAASA,CAAA,EAAG;MACR,OAAO,IAAI,CAACQ,MAAM;IACtB;EAAC;IAAAzG,GAAA;IAAAjF,KAAA,EACD,SAAA2a,WAAWA,CAAC/V,EAAE,EAAE;MAAA,IAAA6V,MAAA;MACZ,IAAMjU,KAAK,GAAG,IAAI,CAACqgB,QAAQ,CAACxoB,IAAI,CAACuG,EAAE,CAAC,GAAG,CAAC;MACxC,OAAO,YAAM;QACT6V,MAAI,CAACoM,QAAQ,CAACpgB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAClC,CAAC;IACL;EAAC;IAAAvB,GAAA;IAAAjF,KAAA,EACD,SAAA8mB,GAAGA,CAACpb,MAAM,EAAEhI,OAAO,EAAmB;MAAA,IAAjBqjB,QAAQ,GAAAvoB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,IAAI;MAChC,IAAM2b,WAAW,GAAG,IAAI,CAACN,QAAQ,CAACnO,MAAM,CAAC;MACzC,IAAIyO,WAAW,EAAE;QACb,IAAI4M,QAAQ,EAAE;UACVjpB,MAAM,CAACmM,MAAM,CAACkQ,WAAW,EAAEzW,OAAO,CAAC;QACvC,CAAC,MACI;UACD5F,MAAM,CAACC,IAAI,CAAC2F,OAAO,CAAC,CAAChF,OAAO,CAAC,UAACuG,GAAG,EAAK;YAClC,IAAI,CAACD,MAAM,CAACmV,WAAW,EAAElV,GAAG,CAAC,EAAE;cAC3BkV,WAAW,CAAClV,GAAG,CAAC,GAAGvB,OAAO,CAACuB,GAAG,CAAC;YACnC;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,MACI;QACD,IAAI,CAAC4U,QAAQ,CAACnO,MAAM,CAAC,GAAGhI,OAAO;MACnC;IACJ;EAAC;IAAAuB,GAAA;IAAAjF,KAAA,EACD,SAAAJ,CAACA,CAAC8D,OAAO,EAAEoX,MAAM,EAAE+J,UAAU,EAAE;MAC3B,OAAO,IAAI,CAAC+B,QAAQ,CAAChC,WAAW,CAAClhB,OAAO,EAAEoX,MAAM,EAAE+J,UAAU,CAAC,CAACjiB,IAAI,CAAC,EAAE,CAAC;IAC1E;EAAC;IAAAqC,GAAA;IAAAjF,KAAA,EACD,SAAAnC,CAACA,CAACoH,GAAG,EAAEyG,MAAM,EAAEoP,MAAM,EAAE;MACnB,IAAIpX,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAI,OAAOgI,MAAM,KAAK,QAAQ,EAAE;QAC5BA,MAAM,GAAG2a,eAAe,CAAC3a,MAAM,EAAE,IAAI,CAACmO,QAAQ,CAAC;QAC/CnO,MAAM,KAAKhI,OAAO,GAAG,IAAI,CAACmW,QAAQ,CAACnO,MAAM,CAAC,CAAC;MAC/C,CAAC,MACI;QACDoP,MAAM,GAAGpP,MAAM;MACnB;MACA,IAAI,CAAC1G,MAAM,CAACtB,OAAO,EAAEuB,GAAG,CAAC,EAAE;QACvBmK,OAAO,CAACC,IAAI,0CAAAlJ,MAAA,CAA0ClB,GAAG,2CAAwC,CAAC;QAClG,OAAOA,GAAG;MACd;MACA,OAAO,IAAI,CAAC2hB,QAAQ,CAAChC,WAAW,CAAClhB,OAAO,CAACuB,GAAG,CAAC,EAAE6V,MAAM,CAAC,CAAClY,IAAI,CAAC,EAAE,CAAC;IACnE;EAAC;AAAA;AAGL,SAASokB,cAAcA,CAAC9L,KAAK,EAAEb,IAAI,EAAE;EACjC;EACA,IAAIa,KAAK,CAACI,YAAY,EAAE;IACpB;IACAJ,KAAK,CAACI,YAAY,CAAC,UAAC2L,SAAS,EAAK;MAC9B5M,IAAI,CAAC5O,SAAS,CAACwb,SAAS,CAAC;IAC7B,CAAC,CAAC;EACN,CAAC,MACI;IACD/L,KAAK,CAACgM,MAAM,CAAC;MAAA,OAAMhM,KAAK,CAAC3P,OAAO;IAAA,GAAE,UAAC0b,SAAS,EAAK;MAC7C5M,IAAI,CAAC5O,SAAS,CAACwb,SAAS,CAAC;IAC7B,CAAC,CAAC;EACN;AACJ;AACA,SAASE,gBAAgBA,CAAA,EAAG;EACxB,IAAI,OAAO7D,GAAG,KAAK,WAAW,IAAIA,GAAG,CAACpY,SAAS,EAAE;IAC7C,OAAOoY,GAAG,CAACpY,SAAS,CAAC,CAAC;EAC1B;EACA;EACA,IAAI,OAAOY,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACZ,SAAS,EAAE;IACnD,OAAOY,MAAM,CAACZ,SAAS,CAAC,CAAC;EAC7B;EACA,OAAO4a,SAAS;AACpB;AACA,SAASxL,WAAWA,CAAC5O,MAAM,EAA0C;EAAA,IAAxCmO,QAAQ,GAAArb,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEkoB,cAAc,GAAAloB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAoJ,SAAA;EAAA,IAAE+e,OAAO,GAAAnoB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAoJ,SAAA;EAC/D;EACA,IAAI,OAAO8D,MAAM,KAAK,QAAQ,EAAE;IAAA,IAAAsT,KAAA,GACP,CACjBnF,QAAQ,EACRnO,MAAM,CACT;IAHAA,MAAM,GAAAsT,KAAA;IAAEnF,QAAQ,GAAAmF,KAAA;EAIrB;EACA,IAAI,OAAOtT,MAAM,KAAK,QAAQ,EAAE;IAC5B;IACAA,MAAM,GAAGyb,gBAAgB,CAAC,CAAC;EAC/B;EACA,IAAI,OAAOT,cAAc,KAAK,QAAQ,EAAE;IACpCA,cAAc,GACT,OAAOzM,WAAW,KAAK,WAAW,IAAIA,WAAW,CAACyM,cAAc,IAC7DZ,SAAS;EACrB;EACA,IAAMzL,IAAI,GAAG,IAAIoM,IAAI,CAAC;IAClB/a,MAAM,EAANA,MAAM;IACNgb,cAAc,EAAdA,cAAc;IACd7M,QAAQ,EAARA,QAAQ;IACR8M,OAAO,EAAPA;EACJ,CAAC,CAAC;EACF,IAAI9oB,EAAC,GAAG,SAAJA,CAACA,CAAIoH,GAAG,EAAE6V,MAAM,EAAK;IACrB,IAAI,OAAO1P,MAAM,KAAK,UAAU,EAAE;MAC9B;MACA;MACAvN,EAAC,GAAG,SAAJA,CAACA,CAAaoH,GAAG,EAAE6V,MAAM,EAAE;QACvB,OAAOT,IAAI,CAACxc,CAAC,CAACoH,GAAG,EAAE6V,MAAM,CAAC;MAC9B,CAAC;IACL,CAAC,MACI;MACD,IAAIsM,kBAAkB,GAAG,KAAK;MAC9BvpB,EAAC,GAAG,SAAJA,CAACA,CAAaoH,GAAG,EAAE6V,MAAM,EAAE;QACvB,IAAMI,KAAK,GAAG9P,MAAM,CAAC,CAAC,CAACE,GAAG;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI4P,KAAK,EAAE;UACP;UACAA,KAAK,CAAC3P,OAAO;UACb,IAAI,CAAC6b,kBAAkB,EAAE;YACrBA,kBAAkB,GAAG,IAAI;YACzBJ,cAAc,CAAC9L,KAAK,EAAEb,IAAI,CAAC;UAC/B;QACJ;QACA,OAAOA,IAAI,CAACxc,CAAC,CAACoH,GAAG,EAAE6V,MAAM,CAAC;MAC9B,CAAC;IACL;IACA,OAAOjd,EAAC,CAACoH,GAAG,EAAE6V,MAAM,CAAC;EACzB,CAAC;EACD,OAAO;IACHT,IAAI,EAAJA,IAAI;IACJza,CAAC,WAADA,CAACA,CAAC8D,OAAO,EAAEoX,MAAM,EAAE+J,UAAU,EAAE;MAC3B,OAAOxK,IAAI,CAACza,CAAC,CAAC8D,OAAO,EAAEoX,MAAM,EAAE+J,UAAU,CAAC;IAC9C,CAAC;IACDhnB,CAAC,WAADA,CAACA,CAACoH,GAAG,EAAE6V,MAAM,EAAE;MACX,OAAOjd,EAAC,CAACoH,GAAG,EAAE6V,MAAM,CAAC;IACzB,CAAC;IACDgM,GAAG,WAAHA,GAAGA,CAACpb,MAAM,EAAEhI,OAAO,EAAmB;MAAA,IAAjBqjB,QAAQ,GAAAvoB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoJ,SAAA,GAAApJ,SAAA,MAAG,IAAI;MAChC,OAAO6b,IAAI,CAACyM,GAAG,CAACpb,MAAM,EAAEhI,OAAO,EAAEqjB,QAAQ,CAAC;IAC9C,CAAC;IACDrL,KAAK,WAALA,KAAKA,CAAC9W,EAAE,EAAE;MACN,OAAOyV,IAAI,CAACM,WAAW,CAAC/V,EAAE,CAAC;IAC/B,CAAC;IACDsG,SAAS,WAATA,SAASA,CAAA,EAAG;MACR,OAAOmP,IAAI,CAACnP,SAAS,CAAC,CAAC;IAC3B,CAAC;IACDO,SAAS,WAATA,SAASA,CAACwb,SAAS,EAAE;MACjB,OAAO5M,IAAI,CAAC5O,SAAS,CAACwb,SAAS,CAAC;IACpC;EACJ,CAAC;AACL;AAEA,IAAMI,QAAQ,GAAA3D,OAAA,CAAA2D,QAAA,GAAG,SAAXA,QAAQA,CAAIzH,GAAG;EAAA,OAAK,OAAOA,GAAG,KAAK,QAAQ;AAAA;AACjD,IAAIgH,QAAQ;AACZ,SAASU,WAAWA,CAACC,OAAO,EAAE1C,UAAU,EAAE;EACtC,IAAI,CAAC+B,QAAQ,EAAE;IACXA,QAAQ,GAAG,IAAIrC,aAAa,CAAC,CAAC;EAClC;EACA,OAAOiD,WAAW,CAACD,OAAO,EAAE,UAACA,OAAO,EAAEtiB,GAAG,EAAK;IAC1C,IAAMjF,KAAK,GAAGunB,OAAO,CAACtiB,GAAG,CAAC;IAC1B,IAAIoiB,QAAQ,CAACrnB,KAAK,CAAC,EAAE;MACjB,IAAIynB,SAAS,CAACznB,KAAK,EAAE6kB,UAAU,CAAC,EAAE;QAC9B,OAAO,IAAI;MACf;IACJ,CAAC,MACI;MACD,OAAOyC,WAAW,CAACtnB,KAAK,EAAE6kB,UAAU,CAAC;IACzC;EACJ,CAAC,CAAC;AACN;AACA,SAAS6C,aAAaA,CAACH,OAAO,EAAEzM,MAAM,EAAE+J,UAAU,EAAE;EAChD,IAAI,CAAC+B,QAAQ,EAAE;IACXA,QAAQ,GAAG,IAAIrC,aAAa,CAAC,CAAC;EAClC;EACAiD,WAAW,CAACD,OAAO,EAAE,UAACA,OAAO,EAAEtiB,GAAG,EAAK;IACnC,IAAMjF,KAAK,GAAGunB,OAAO,CAACtiB,GAAG,CAAC;IAC1B,IAAIoiB,QAAQ,CAACrnB,KAAK,CAAC,EAAE;MACjB,IAAIynB,SAAS,CAACznB,KAAK,EAAE6kB,UAAU,CAAC,EAAE;QAC9B0C,OAAO,CAACtiB,GAAG,CAAC,GAAG0iB,UAAU,CAAC3nB,KAAK,EAAE8a,MAAM,EAAE+J,UAAU,CAAC;MACxD;IACJ,CAAC,MACI;MACD6C,aAAa,CAAC1nB,KAAK,EAAE8a,MAAM,EAAE+J,UAAU,CAAC;IAC5C;EACJ,CAAC,CAAC;EACF,OAAO0C,OAAO;AAClB;AACA,SAASK,kBAAkBA,CAACC,OAAO,EAAA5H,KAAA,EAAoC;EAAA,IAAhCvU,MAAM,GAAAuU,KAAA,CAANvU,MAAM;IAAEwO,OAAO,GAAA+F,KAAA,CAAP/F,OAAO;IAAE2K,UAAU,GAAA5E,KAAA,CAAV4E,UAAU;EAC9D,IAAI,CAAC4C,SAAS,CAACI,OAAO,EAAEhD,UAAU,CAAC,EAAE;IACjC,OAAOgD,OAAO;EAClB;EACA,IAAI,CAACjB,QAAQ,EAAE;IACXA,QAAQ,GAAG,IAAIrC,aAAa,CAAC,CAAC;EAClC;EACA,IAAMuD,YAAY,GAAG,EAAE;EACvBhqB,MAAM,CAACC,IAAI,CAACmc,OAAO,CAAC,CAACxb,OAAO,CAAC,UAAC0C,IAAI,EAAK;IACnC,IAAIA,IAAI,KAAKsK,MAAM,EAAE;MACjBoc,YAAY,CAACzpB,IAAI,CAAC;QACdqN,MAAM,EAAEtK,IAAI;QACZ0Z,MAAM,EAAEZ,OAAO,CAAC9Y,IAAI;MACxB,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF0mB,YAAY,CAACC,OAAO,CAAC;IAAErc,MAAM,EAANA,MAAM;IAAEoP,MAAM,EAAEZ,OAAO,CAACxO,MAAM;EAAE,CAAC,CAAC;EACzD,IAAI;IACA,OAAOnI,IAAI,CAACkR,SAAS,CAACuT,cAAc,CAACzkB,IAAI,CAACC,KAAK,CAACqkB,OAAO,CAAC,EAAEC,YAAY,EAAEjD,UAAU,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;EACjG,CAAC,CACD,OAAOrnB,CAAC,EAAE,CAAE;EACZ,OAAOqqB,OAAO;AAClB;AACA,SAASJ,SAASA,CAACznB,KAAK,EAAE6kB,UAAU,EAAE;EAClC,OAAO7kB,KAAK,CAACmC,OAAO,CAAC0iB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC5C;AACA,SAAS8C,UAAUA,CAAC3nB,KAAK,EAAE8a,MAAM,EAAE+J,UAAU,EAAE;EAC3C,OAAO+B,QAAQ,CAAChC,WAAW,CAAC5kB,KAAK,EAAE8a,MAAM,EAAE+J,UAAU,CAAC,CAACjiB,IAAI,CAAC,EAAE,CAAC;AACnE;AACA,SAASqlB,YAAYA,CAACV,OAAO,EAAEtiB,GAAG,EAAE6iB,YAAY,EAAEjD,UAAU,EAAE;EAC1D,IAAM7kB,KAAK,GAAGunB,OAAO,CAACtiB,GAAG,CAAC;EAC1B,IAAIoiB,QAAQ,CAACrnB,KAAK,CAAC,EAAE;IACjB;IACA,IAAIynB,SAAS,CAACznB,KAAK,EAAE6kB,UAAU,CAAC,EAAE;MAC9B0C,OAAO,CAACtiB,GAAG,CAAC,GAAG0iB,UAAU,CAAC3nB,KAAK,EAAE8nB,YAAY,CAAC,CAAC,CAAC,CAAChN,MAAM,EAAE+J,UAAU,CAAC;MACpE,IAAIiD,YAAY,CAACrpB,MAAM,GAAG,CAAC,EAAE;QACzB;QACA,IAAMypB,YAAY,GAAIX,OAAO,CAACtiB,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAE;QACpD6iB,YAAY,CAACppB,OAAO,CAAC,UAACypB,UAAU,EAAK;UACjCD,YAAY,CAACC,UAAU,CAACzc,MAAM,CAAC,GAAGic,UAAU,CAAC3nB,KAAK,EAAEmoB,UAAU,CAACrN,MAAM,EAAE+J,UAAU,CAAC;QACtF,CAAC,CAAC;MACN;IACJ;EACJ,CAAC,MACI;IACDmD,cAAc,CAAChoB,KAAK,EAAE8nB,YAAY,EAAEjD,UAAU,CAAC;EACnD;AACJ;AACA,SAASmD,cAAcA,CAACT,OAAO,EAAEO,YAAY,EAAEjD,UAAU,EAAE;EACvD2C,WAAW,CAACD,OAAO,EAAE,UAACA,OAAO,EAAEtiB,GAAG,EAAK;IACnCgjB,YAAY,CAACV,OAAO,EAAEtiB,GAAG,EAAE6iB,YAAY,EAAEjD,UAAU,CAAC;EACxD,CAAC,CAAC;EACF,OAAO0C,OAAO;AAClB;AACA,SAASC,WAAWA,CAACD,OAAO,EAAEa,IAAI,EAAE;EAChC,IAAIjoB,OAAO,CAAConB,OAAO,CAAC,EAAE;IAClB,KAAK,IAAI9nB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8nB,OAAO,CAAC9oB,MAAM,EAAEgB,CAAC,EAAE,EAAE;MACrC,IAAI2oB,IAAI,CAACb,OAAO,EAAE9nB,CAAC,CAAC,EAAE;QAClB,OAAO,IAAI;MACf;IACJ;EACJ,CAAC,MACI,IAAI4kB,QAAQ,CAACkD,OAAO,CAAC,EAAE;IACxB,KAAK,IAAMtiB,GAAG,IAAIsiB,OAAO,EAAE;MACvB,IAAIa,IAAI,CAACb,OAAO,EAAEtiB,GAAG,CAAC,EAAE;QACpB,OAAO,IAAI;MACf;IACJ;EACJ;EACA,OAAO,KAAK;AAChB;AAEA,SAASojB,aAAaA,CAACnO,OAAO,EAAE;EAC5B,OAAO,UAACxO,MAAM,EAAK;IACf,IAAI,CAACA,MAAM,EAAE;MACT,OAAOA,MAAM;IACjB;IACAA,MAAM,GAAG2a,eAAe,CAAC3a,MAAM,CAAC,IAAIA,MAAM;IAC1C,OAAO4c,kBAAkB,CAAC5c,MAAM,CAAC,CAACyH,IAAI,CAAC,UAACzH,MAAM;MAAA,OAAKwO,OAAO,CAAC/X,OAAO,CAACuJ,MAAM,CAAC,GAAG,CAAC,CAAC;IAAA,EAAC;EACpF,CAAC;AACL;AACA,SAAS4c,kBAAkBA,CAAC5c,MAAM,EAAE;EAChC,IAAM6c,KAAK,GAAG,EAAE;EAChB,IAAMzD,MAAM,GAAGpZ,MAAM,CAAClJ,KAAK,CAAC,GAAG,CAAC;EAChC,OAAOsiB,MAAM,CAACrmB,MAAM,EAAE;IAClB8pB,KAAK,CAAClqB,IAAI,CAACymB,MAAM,CAACliB,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5BkiB,MAAM,CAAC0D,GAAG,CAAC,CAAC;EAChB;EACA,OAAOD,KAAK;AAChB,C", "file": "common/vendor.js", "sourcesContent": ["import Vue from 'vue';\r\nimport { initVueI18n } from '@dcloudio/uni-i18n';\r\n\r\nlet realAtob;\r\n\r\nconst b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\r\nconst b64re = /^(?:[A-Za-z\\d+/]{4})*?(?:[A-Za-z\\d+/]{2}(?:==)?|[A-Za-z\\d+/]{3}=?)?$/;\r\n\r\nif (typeof atob !== 'function') {\r\n  realAtob = function (str) {\r\n    str = String(str).replace(/[\\t\\n\\f\\r ]+/g, '');\r\n    if (!b64re.test(str)) { throw new Error(\"Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.\") }\r\n\r\n    // Adding the padding if missing, for semplicity\r\n    str += '=='.slice(2 - (str.length & 3));\r\n    var bitmap; var result = ''; var r1; var r2; var i = 0;\r\n    for (; i < str.length;) {\r\n      bitmap = b64.indexOf(str.charAt(i++)) << 18 | b64.indexOf(str.charAt(i++)) << 12 |\r\n                    (r1 = b64.indexOf(str.charAt(i++))) << 6 | (r2 = b64.indexOf(str.charAt(i++)));\r\n\r\n      result += r1 === 64 ? String.fromCharCode(bitmap >> 16 & 255)\r\n        : r2 === 64 ? String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255)\r\n          : String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255, bitmap & 255);\r\n    }\r\n    return result\r\n  };\r\n} else {\r\n  // 注意atob只能在全局对象上调用，例如：`const Base64 = {atob};Base64.atob('xxxx')`是错误的用法\r\n  realAtob = atob;\r\n}\r\n\r\nfunction b64DecodeUnicode (str) {\r\n  return decodeURIComponent(realAtob(str).split('').map(function (c) {\r\n    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)\r\n  }).join(''))\r\n}\r\n\r\nfunction getCurrentUserInfo () {\r\n  const token = ( wx).getStorageSync('uni_id_token') || '';\r\n  const tokenArr = token.split('.');\r\n  if (!token || tokenArr.length !== 3) {\r\n    return {\r\n      uid: null,\r\n      role: [],\r\n      permission: [],\r\n      tokenExpired: 0\r\n    }\r\n  }\r\n  let userInfo;\r\n  try {\r\n    userInfo = JSON.parse(b64DecodeUnicode(tokenArr[1]));\r\n  } catch (error) {\r\n    throw new Error('获取当前用户信息出错，详细错误信息为：' + error.message)\r\n  }\r\n  userInfo.tokenExpired = userInfo.exp * 1000;\r\n  delete userInfo.exp;\r\n  delete userInfo.iat;\r\n  return userInfo\r\n}\r\n\r\nfunction uniIdMixin (Vue) {\r\n  Vue.prototype.uniIDHasRole = function (roleId) {\r\n    const {\r\n      role\r\n    } = getCurrentUserInfo();\r\n    return role.indexOf(roleId) > -1\r\n  };\r\n  Vue.prototype.uniIDHasPermission = function (permissionId) {\r\n    const {\r\n      permission\r\n    } = getCurrentUserInfo();\r\n    return this.uniIDHasRole('admin') || permission.indexOf(permissionId) > -1\r\n  };\r\n  Vue.prototype.uniIDTokenValid = function () {\r\n    const {\r\n      tokenExpired\r\n    } = getCurrentUserInfo();\r\n    return tokenExpired > Date.now()\r\n  };\r\n}\r\n\r\nconst _toString = Object.prototype.toString;\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\n\r\nfunction isFn (fn) {\r\n  return typeof fn === 'function'\r\n}\r\n\r\nfunction isStr (str) {\r\n  return typeof str === 'string'\r\n}\r\n\r\nfunction isPlainObject (obj) {\r\n  return _toString.call(obj) === '[object Object]'\r\n}\r\n\r\nfunction hasOwn (obj, key) {\r\n  return hasOwnProperty.call(obj, key)\r\n}\r\n\r\nfunction noop () {}\r\n\r\n/**\r\n * Create a cached version of a pure function.\r\n */\r\nfunction cached (fn) {\r\n  const cache = Object.create(null);\r\n  return function cachedFn (str) {\r\n    const hit = cache[str];\r\n    return hit || (cache[str] = fn(str))\r\n  }\r\n}\r\n\r\n/**\r\n * Camelize a hyphen-delimited string.\r\n */\r\nconst camelizeRE = /-(\\w)/g;\r\nconst camelize = cached((str) => {\r\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : '')\r\n});\r\n\r\nconst HOOKS = [\r\n  'invoke',\r\n  'success',\r\n  'fail',\r\n  'complete',\r\n  'returnValue'\r\n];\r\n\r\nconst globalInterceptors = {};\r\nconst scopedInterceptors = {};\r\n\r\nfunction mergeHook (parentVal, childVal) {\r\n  const res = childVal\r\n    ? parentVal\r\n      ? parentVal.concat(childVal)\r\n      : Array.isArray(childVal)\r\n        ? childVal : [childVal]\r\n    : parentVal;\r\n  return res\r\n    ? dedupeHooks(res)\r\n    : res\r\n}\r\n\r\nfunction dedupeHooks (hooks) {\r\n  const res = [];\r\n  for (let i = 0; i < hooks.length; i++) {\r\n    if (res.indexOf(hooks[i]) === -1) {\r\n      res.push(hooks[i]);\r\n    }\r\n  }\r\n  return res\r\n}\r\n\r\nfunction removeHook (hooks, hook) {\r\n  const index = hooks.indexOf(hook);\r\n  if (index !== -1) {\r\n    hooks.splice(index, 1);\r\n  }\r\n}\r\n\r\nfunction mergeInterceptorHook (interceptor, option) {\r\n  Object.keys(option).forEach(hook => {\r\n    if (HOOKS.indexOf(hook) !== -1 && isFn(option[hook])) {\r\n      interceptor[hook] = mergeHook(interceptor[hook], option[hook]);\r\n    }\r\n  });\r\n}\r\n\r\nfunction removeInterceptorHook (interceptor, option) {\r\n  if (!interceptor || !option) {\r\n    return\r\n  }\r\n  Object.keys(option).forEach(hook => {\r\n    if (HOOKS.indexOf(hook) !== -1 && isFn(option[hook])) {\r\n      removeHook(interceptor[hook], option[hook]);\r\n    }\r\n  });\r\n}\r\n\r\nfunction addInterceptor (method, option) {\r\n  if (typeof method === 'string' && isPlainObject(option)) {\r\n    mergeInterceptorHook(scopedInterceptors[method] || (scopedInterceptors[method] = {}), option);\r\n  } else if (isPlainObject(method)) {\r\n    mergeInterceptorHook(globalInterceptors, method);\r\n  }\r\n}\r\n\r\nfunction removeInterceptor (method, option) {\r\n  if (typeof method === 'string') {\r\n    if (isPlainObject(option)) {\r\n      removeInterceptorHook(scopedInterceptors[method], option);\r\n    } else {\r\n      delete scopedInterceptors[method];\r\n    }\r\n  } else if (isPlainObject(method)) {\r\n    removeInterceptorHook(globalInterceptors, method);\r\n  }\r\n}\r\n\r\nfunction wrapperHook (hook) {\r\n  return function (data) {\r\n    return hook(data) || data\r\n  }\r\n}\r\n\r\nfunction isPromise (obj) {\r\n  return !!obj && (typeof obj === 'object' || typeof obj === 'function') && typeof obj.then === 'function'\r\n}\r\n\r\nfunction queue (hooks, data) {\r\n  let promise = false;\r\n  for (let i = 0; i < hooks.length; i++) {\r\n    const hook = hooks[i];\r\n    if (promise) {\r\n      promise = Promise.resolve(wrapperHook(hook));\r\n    } else {\r\n      const res = hook(data);\r\n      if (isPromise(res)) {\r\n        promise = Promise.resolve(res);\r\n      }\r\n      if (res === false) {\r\n        return {\r\n          then () { }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  return promise || {\r\n    then (callback) {\r\n      return callback(data)\r\n    }\r\n  }\r\n}\r\n\r\nfunction wrapperOptions (interceptor, options = {}) {\r\n  ['success', 'fail', 'complete'].forEach(name => {\r\n    if (Array.isArray(interceptor[name])) {\r\n      const oldCallback = options[name];\r\n      options[name] = function callbackInterceptor (res) {\r\n        queue(interceptor[name], res).then((res) => {\r\n          /* eslint-disable no-mixed-operators */\r\n          return isFn(oldCallback) && oldCallback(res) || res\r\n        });\r\n      };\r\n    }\r\n  });\r\n  return options\r\n}\r\n\r\nfunction wrapperReturnValue (method, returnValue) {\r\n  const returnValueHooks = [];\r\n  if (Array.isArray(globalInterceptors.returnValue)) {\r\n    returnValueHooks.push(...globalInterceptors.returnValue);\r\n  }\r\n  const interceptor = scopedInterceptors[method];\r\n  if (interceptor && Array.isArray(interceptor.returnValue)) {\r\n    returnValueHooks.push(...interceptor.returnValue);\r\n  }\r\n  returnValueHooks.forEach(hook => {\r\n    returnValue = hook(returnValue) || returnValue;\r\n  });\r\n  return returnValue\r\n}\r\n\r\nfunction getApiInterceptorHooks (method) {\r\n  const interceptor = Object.create(null);\r\n  Object.keys(globalInterceptors).forEach(hook => {\r\n    if (hook !== 'returnValue') {\r\n      interceptor[hook] = globalInterceptors[hook].slice();\r\n    }\r\n  });\r\n  const scopedInterceptor = scopedInterceptors[method];\r\n  if (scopedInterceptor) {\r\n    Object.keys(scopedInterceptor).forEach(hook => {\r\n      if (hook !== 'returnValue') {\r\n        interceptor[hook] = (interceptor[hook] || []).concat(scopedInterceptor[hook]);\r\n      }\r\n    });\r\n  }\r\n  return interceptor\r\n}\r\n\r\nfunction invokeApi (method, api, options, ...params) {\r\n  const interceptor = getApiInterceptorHooks(method);\r\n  if (interceptor && Object.keys(interceptor).length) {\r\n    if (Array.isArray(interceptor.invoke)) {\r\n      const res = queue(interceptor.invoke, options);\r\n      return res.then((options) => {\r\n        return api(wrapperOptions(interceptor, options), ...params)\r\n      })\r\n    } else {\r\n      return api(wrapperOptions(interceptor, options), ...params)\r\n    }\r\n  }\r\n  return api(options, ...params)\r\n}\r\n\r\nconst promiseInterceptor = {\r\n  returnValue (res) {\r\n    if (!isPromise(res)) {\r\n      return res\r\n    }\r\n    return new Promise((resolve, reject) => {\r\n      res.then(res => {\r\n        if (res[0]) {\r\n          reject(res[0]);\r\n        } else {\r\n          resolve(res[1]);\r\n        }\r\n      });\r\n    })\r\n  }\r\n};\r\n\r\nconst SYNC_API_RE =\r\n  /^\\$|Window$|WindowStyle$|sendNativeEvent|restoreGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale/;\r\n\r\nconst CONTEXT_API_RE = /^create|Manager$/;\r\n\r\n// Context例外情况\r\nconst CONTEXT_API_RE_EXC = ['createBLEConnection'];\r\n\r\n// 同步例外情况\r\nconst ASYNC_API = ['createBLEConnection'];\r\n\r\nconst CALLBACK_API_RE = /^on|^off/;\r\n\r\nfunction isContextApi (name) {\r\n  return CONTEXT_API_RE.test(name) && CONTEXT_API_RE_EXC.indexOf(name) === -1\r\n}\r\nfunction isSyncApi (name) {\r\n  return SYNC_API_RE.test(name) && ASYNC_API.indexOf(name) === -1\r\n}\r\n\r\nfunction isCallbackApi (name) {\r\n  return CALLBACK_API_RE.test(name) && name !== 'onPush'\r\n}\r\n\r\nfunction handlePromise (promise) {\r\n  return promise.then(data => {\r\n    return [null, data]\r\n  })\r\n    .catch(err => [err])\r\n}\r\n\r\nfunction shouldPromise (name) {\r\n  if (\r\n    isContextApi(name) ||\r\n    isSyncApi(name) ||\r\n    isCallbackApi(name)\r\n  ) {\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n/* eslint-disable no-extend-native */\r\nif (!Promise.prototype.finally) {\r\n  Promise.prototype.finally = function (callback) {\r\n    const promise = this.constructor;\r\n    return this.then(\r\n      value => promise.resolve(callback()).then(() => value),\r\n      reason => promise.resolve(callback()).then(() => {\r\n        throw reason\r\n      })\r\n    )\r\n  };\r\n}\r\n\r\nfunction promisify (name, api) {\r\n  if (!shouldPromise(name)) {\r\n    return api\r\n  }\r\n  return function promiseApi (options = {}, ...params) {\r\n    if (isFn(options.success) || isFn(options.fail) || isFn(options.complete)) {\r\n      return wrapperReturnValue(name, invokeApi(name, api, options, ...params))\r\n    }\r\n    return wrapperReturnValue(name, handlePromise(new Promise((resolve, reject) => {\r\n      invokeApi(name, api, Object.assign({}, options, {\r\n        success: resolve,\r\n        fail: reject\r\n      }), ...params);\r\n    })))\r\n  }\r\n}\r\n\r\nconst EPS = 1e-4;\r\nconst BASE_DEVICE_WIDTH = 750;\r\nlet isIOS = false;\r\nlet deviceWidth = 0;\r\nlet deviceDPR = 0;\r\n\r\nfunction checkDeviceWidth () {\r\n  const {\r\n    platform,\r\n    pixelRatio,\r\n    windowWidth\r\n  } = wx.getSystemInfoSync(); // uni=>wx runtime 编译目标是 uni 对象，内部不允许直接使用 uni\r\n\r\n  deviceWidth = windowWidth;\r\n  deviceDPR = pixelRatio;\r\n  isIOS = platform === 'ios';\r\n}\r\n\r\nfunction upx2px (number, newDeviceWidth) {\r\n  if (deviceWidth === 0) {\r\n    checkDeviceWidth();\r\n  }\r\n\r\n  number = Number(number);\r\n  if (number === 0) {\r\n    return 0\r\n  }\r\n  let result = (number / BASE_DEVICE_WIDTH) * (newDeviceWidth || deviceWidth);\r\n  if (result < 0) {\r\n    result = -result;\r\n  }\r\n  result = Math.floor(result + EPS);\r\n  if (result === 0) {\r\n    if (deviceDPR === 1 || !isIOS) {\r\n      result = 1;\r\n    } else {\r\n      result = 0.5;\r\n    }\r\n  }\r\n  return number < 0 ? -result : result\r\n}\r\n\r\nfunction getLocale () {\r\n  // 优先使用 $locale\r\n  const app = getApp({\r\n    allowDefault: true\r\n  });\r\n  if (app && app.$vm) {\r\n    return app.$vm.$locale\r\n  }\r\n  return wx.getSystemInfoSync().language || 'zh-Hans'\r\n}\r\n\r\nfunction setLocale (locale) {\r\n  const app = getApp();\r\n  if (!app) {\r\n    return false\r\n  }\r\n  const oldLocale = app.$vm.$locale;\r\n  if (oldLocale !== locale) {\r\n    app.$vm.$locale = locale;\r\n    onLocaleChangeCallbacks.forEach((fn) => fn({\r\n      locale\r\n    }));\r\n    return true\r\n  }\r\n  return false\r\n}\r\n\r\nconst onLocaleChangeCallbacks = [];\r\nfunction onLocaleChange (fn) {\r\n  if (onLocaleChangeCallbacks.indexOf(fn) === -1) {\r\n    onLocaleChangeCallbacks.push(fn);\r\n  }\r\n}\r\n\r\nif (typeof global !== 'undefined') {\r\n  global.getLocale = getLocale;\r\n}\r\n\r\nconst interceptors = {\r\n  promiseInterceptor\r\n};\r\n\r\nvar baseApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  upx2px: upx2px,\r\n  getLocale: getLocale,\r\n  setLocale: setLocale,\r\n  onLocaleChange: onLocaleChange,\r\n  addInterceptor: addInterceptor,\r\n  removeInterceptor: removeInterceptor,\r\n  interceptors: interceptors\r\n});\r\n\r\nfunction findExistsPageIndex (url) {\r\n  const pages = getCurrentPages();\r\n  let len = pages.length;\r\n  while (len--) {\r\n    const page = pages[len];\r\n    if (page.$page && page.$page.fullPath === url) {\r\n      return len\r\n    }\r\n  }\r\n  return -1\r\n}\r\n\r\nvar redirectTo = {\r\n  name (fromArgs) {\r\n    if (fromArgs.exists === 'back' && fromArgs.delta) {\r\n      return 'navigateBack'\r\n    }\r\n    return 'redirectTo'\r\n  },\r\n  args (fromArgs) {\r\n    if (fromArgs.exists === 'back' && fromArgs.url) {\r\n      const existsPageIndex = findExistsPageIndex(fromArgs.url);\r\n      if (existsPageIndex !== -1) {\r\n        const delta = getCurrentPages().length - 1 - existsPageIndex;\r\n        if (delta > 0) {\r\n          fromArgs.delta = delta;\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nvar previewImage = {\r\n  args (fromArgs) {\r\n    let currentIndex = parseInt(fromArgs.current);\r\n    if (isNaN(currentIndex)) {\r\n      return\r\n    }\r\n    const urls = fromArgs.urls;\r\n    if (!Array.isArray(urls)) {\r\n      return\r\n    }\r\n    const len = urls.length;\r\n    if (!len) {\r\n      return\r\n    }\r\n    if (currentIndex < 0) {\r\n      currentIndex = 0;\r\n    } else if (currentIndex >= len) {\r\n      currentIndex = len - 1;\r\n    }\r\n    if (currentIndex > 0) {\r\n      fromArgs.current = urls[currentIndex];\r\n      fromArgs.urls = urls.filter(\r\n        (item, index) => index < currentIndex ? item !== urls[currentIndex] : true\r\n      );\r\n    } else {\r\n      fromArgs.current = urls[0];\r\n    }\r\n    return {\r\n      indicator: false,\r\n      loop: false\r\n    }\r\n  }\r\n};\r\n\r\nconst UUID_KEY = '__DC_STAT_UUID';\r\nlet deviceId;\r\nfunction addUuid (result) {\r\n  deviceId = deviceId || wx.getStorageSync(UUID_KEY);\r\n  if (!deviceId) {\r\n    deviceId = Date.now() + '' + Math.floor(Math.random() * 1e7);\r\n    wx.setStorage({\r\n      key: UUID_KEY,\r\n      data: deviceId\r\n    });\r\n  }\r\n  result.deviceId = deviceId;\r\n}\r\n\r\nfunction addSafeAreaInsets (result) {\r\n  if (result.safeArea) {\r\n    const safeArea = result.safeArea;\r\n    result.safeAreaInsets = {\r\n      top: safeArea.top,\r\n      left: safeArea.left,\r\n      right: result.windowWidth - safeArea.right,\r\n      bottom: result.windowHeight - safeArea.bottom\r\n    };\r\n  }\r\n}\r\n\r\nvar getSystemInfo = {\r\n  returnValue: function (result) {\r\n    addUuid(result);\r\n    addSafeAreaInsets(result);\r\n  }\r\n};\r\n\r\n// import navigateTo from 'uni-helpers/navigate-to'\r\n\r\nconst protocols = {\r\n  redirectTo,\r\n  // navigateTo,  // 由于在微信开发者工具的页面参数，会显示__id__参数，因此暂时关闭mp-weixin对于navigateTo的AOP\r\n  previewImage,\r\n  getSystemInfo,\r\n  getSystemInfoSync: getSystemInfo\r\n};\r\nconst todos = [\r\n  'vibrate',\r\n  'preloadPage',\r\n  'unPreloadPage',\r\n  'loadSubPackage'\r\n];\r\nconst canIUses = [];\r\n\r\nconst CALLBACKS = ['success', 'fail', 'cancel', 'complete'];\r\n\r\nfunction processCallback (methodName, method, returnValue) {\r\n  return function (res) {\r\n    return method(processReturnValue(methodName, res, returnValue))\r\n  }\r\n}\r\n\r\nfunction processArgs (methodName, fromArgs, argsOption = {}, returnValue = {}, keepFromArgs = false) {\r\n  if (isPlainObject(fromArgs)) { // 一般 api 的参数解析\r\n    const toArgs = keepFromArgs === true ? fromArgs : {}; // returnValue 为 false 时，说明是格式化返回值，直接在返回值对象上修改赋值\r\n    if (isFn(argsOption)) {\r\n      argsOption = argsOption(fromArgs, toArgs) || {};\r\n    }\r\n    for (const key in fromArgs) {\r\n      if (hasOwn(argsOption, key)) {\r\n        let keyOption = argsOption[key];\r\n        if (isFn(keyOption)) {\r\n          keyOption = keyOption(fromArgs[key], fromArgs, toArgs);\r\n        }\r\n        if (!keyOption) { // 不支持的参数\r\n          console.warn(`The '${methodName}' method of platform '微信小程序' does not support option '${key}'`);\r\n        } else if (isStr(keyOption)) { // 重写参数 key\r\n          toArgs[keyOption] = fromArgs[key];\r\n        } else if (isPlainObject(keyOption)) { // {name:newName,value:value}可重新指定参数 key:value\r\n          toArgs[keyOption.name ? keyOption.name : key] = keyOption.value;\r\n        }\r\n      } else if (CALLBACKS.indexOf(key) !== -1) {\r\n        if (isFn(fromArgs[key])) {\r\n          toArgs[key] = processCallback(methodName, fromArgs[key], returnValue);\r\n        }\r\n      } else {\r\n        if (!keepFromArgs) {\r\n          toArgs[key] = fromArgs[key];\r\n        }\r\n      }\r\n    }\r\n    return toArgs\r\n  } else if (isFn(fromArgs)) {\r\n    fromArgs = processCallback(methodName, fromArgs, returnValue);\r\n  }\r\n  return fromArgs\r\n}\r\n\r\nfunction processReturnValue (methodName, res, returnValue, keepReturnValue = false) {\r\n  if (isFn(protocols.returnValue)) { // 处理通用 returnValue\r\n    res = protocols.returnValue(methodName, res);\r\n  }\r\n  return processArgs(methodName, res, returnValue, {}, keepReturnValue)\r\n}\r\n\r\nfunction wrapper (methodName, method) {\r\n  if (hasOwn(protocols, methodName)) {\r\n    const protocol = protocols[methodName];\r\n    if (!protocol) { // 暂不支持的 api\r\n      return function () {\r\n        console.error(`Platform '微信小程序' does not support '${methodName}'.`);\r\n      }\r\n    }\r\n    return function (arg1, arg2) { // 目前 api 最多两个参数\r\n      let options = protocol;\r\n      if (isFn(protocol)) {\r\n        options = protocol(arg1);\r\n      }\r\n\r\n      arg1 = processArgs(methodName, arg1, options.args, options.returnValue);\r\n\r\n      const args = [arg1];\r\n      if (typeof arg2 !== 'undefined') {\r\n        args.push(arg2);\r\n      }\r\n      if (isFn(options.name)) {\r\n        methodName = options.name(arg1);\r\n      } else if (isStr(options.name)) {\r\n        methodName = options.name;\r\n      }\r\n      const returnValue = wx[methodName].apply(wx, args);\r\n      if (isSyncApi(methodName)) { // 同步 api\r\n        return processReturnValue(methodName, returnValue, options.returnValue, isContextApi(methodName))\r\n      }\r\n      return returnValue\r\n    }\r\n  }\r\n  return method\r\n}\r\n\r\nconst todoApis = Object.create(null);\r\n\r\nconst TODOS = [\r\n  'onTabBarMidButtonTap',\r\n  'subscribePush',\r\n  'unsubscribePush',\r\n  'onPush',\r\n  'offPush',\r\n  'share'\r\n];\r\n\r\nfunction createTodoApi (name) {\r\n  return function todoApi ({\r\n    fail,\r\n    complete\r\n  }) {\r\n    const res = {\r\n      errMsg: `${name}:fail method '${name}' not supported`\r\n    };\r\n    isFn(fail) && fail(res);\r\n    isFn(complete) && complete(res);\r\n  }\r\n}\r\n\r\nTODOS.forEach(function (name) {\r\n  todoApis[name] = createTodoApi(name);\r\n});\r\n\r\nvar providers = {\r\n  oauth: ['weixin'],\r\n  share: ['weixin'],\r\n  payment: ['wxpay'],\r\n  push: ['weixin']\r\n};\r\n\r\nfunction getProvider ({\r\n  service,\r\n  success,\r\n  fail,\r\n  complete\r\n}) {\r\n  let res = false;\r\n  if (providers[service]) {\r\n    res = {\r\n      errMsg: 'getProvider:ok',\r\n      service,\r\n      provider: providers[service]\r\n    };\r\n    isFn(success) && success(res);\r\n  } else {\r\n    res = {\r\n      errMsg: 'getProvider:fail service not found'\r\n    };\r\n    isFn(fail) && fail(res);\r\n  }\r\n  isFn(complete) && complete(res);\r\n}\r\n\r\nvar extraApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  getProvider: getProvider\r\n});\r\n\r\nconst getEmitter = (function () {\r\n  let Emitter;\r\n  return function getUniEmitter () {\r\n    if (!Emitter) {\r\n      Emitter = new Vue();\r\n    }\r\n    return Emitter\r\n  }\r\n})();\r\n\r\nfunction apply (ctx, method, args) {\r\n  return ctx[method].apply(ctx, args)\r\n}\r\n\r\nfunction $on () {\r\n  return apply(getEmitter(), '$on', [...arguments])\r\n}\r\nfunction $off () {\r\n  return apply(getEmitter(), '$off', [...arguments])\r\n}\r\nfunction $once () {\r\n  return apply(getEmitter(), '$once', [...arguments])\r\n}\r\nfunction $emit () {\r\n  return apply(getEmitter(), '$emit', [...arguments])\r\n}\r\n\r\nvar eventApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  $on: $on,\r\n  $off: $off,\r\n  $once: $once,\r\n  $emit: $emit\r\n});\r\n\r\nvar api = /*#__PURE__*/Object.freeze({\r\n  __proto__: null\r\n});\r\n\r\nconst MPPage = Page;\r\nconst MPComponent = Component;\r\n\r\nconst customizeRE = /:/g;\r\n\r\nconst customize = cached((str) => {\r\n  return camelize(str.replace(customizeRE, '-'))\r\n});\r\n\r\nfunction initTriggerEvent (mpInstance) {\r\n  const oldTriggerEvent = mpInstance.triggerEvent;\r\n  mpInstance.triggerEvent = function (event, ...args) {\r\n    return oldTriggerEvent.apply(mpInstance, [customize(event), ...args])\r\n  };\r\n}\r\n\r\nfunction initHook (name, options, isComponent) {\r\n  const oldHook = options[name];\r\n  if (!oldHook) {\r\n    options[name] = function () {\r\n      initTriggerEvent(this);\r\n    };\r\n  } else {\r\n    options[name] = function (...args) {\r\n      initTriggerEvent(this);\r\n      return oldHook.apply(this, args)\r\n    };\r\n  }\r\n}\r\nif (!MPPage.__$wrappered) {\r\n  MPPage.__$wrappered = true;\r\n  Page = function (options = {}) {\r\n    initHook('onLoad', options);\r\n    return MPPage(options)\r\n  };\r\n  Page.after = MPPage.after;\r\n\r\n  Component = function (options = {}) {\r\n    initHook('created', options);\r\n    return MPComponent(options)\r\n  };\r\n}\r\n\r\nconst PAGE_EVENT_HOOKS = [\r\n  'onPullDownRefresh',\r\n  'onReachBottom',\r\n  'onAddToFavorites',\r\n  'onShareTimeline',\r\n  'onShareAppMessage',\r\n  'onPageScroll',\r\n  'onResize',\r\n  'onTabItemTap'\r\n];\r\n\r\nfunction initMocks (vm, mocks) {\r\n  const mpInstance = vm.$mp[vm.mpType];\r\n  mocks.forEach(mock => {\r\n    if (hasOwn(mpInstance, mock)) {\r\n      vm[mock] = mpInstance[mock];\r\n    }\r\n  });\r\n}\r\n\r\nfunction hasHook (hook, vueOptions) {\r\n  if (!vueOptions) {\r\n    return true\r\n  }\r\n\r\n  if (Vue.options && Array.isArray(Vue.options[hook])) {\r\n    return true\r\n  }\r\n\r\n  vueOptions = vueOptions.default || vueOptions;\r\n\r\n  if (isFn(vueOptions)) {\r\n    if (isFn(vueOptions.extendOptions[hook])) {\r\n      return true\r\n    }\r\n    if (vueOptions.super &&\r\n      vueOptions.super.options &&\r\n      Array.isArray(vueOptions.super.options[hook])) {\r\n      return true\r\n    }\r\n    return false\r\n  }\r\n\r\n  if (isFn(vueOptions[hook])) {\r\n    return true\r\n  }\r\n  const mixins = vueOptions.mixins;\r\n  if (Array.isArray(mixins)) {\r\n    return !!mixins.find(mixin => hasHook(hook, mixin))\r\n  }\r\n}\r\n\r\nfunction initHooks (mpOptions, hooks, vueOptions) {\r\n  hooks.forEach(hook => {\r\n    if (hasHook(hook, vueOptions)) {\r\n      mpOptions[hook] = function (args) {\r\n        return this.$vm && this.$vm.__call_hook(hook, args)\r\n      };\r\n    }\r\n  });\r\n}\r\n\r\nfunction initVueComponent (Vue, vueOptions) {\r\n  vueOptions = vueOptions.default || vueOptions;\r\n  let VueComponent;\r\n  if (isFn(vueOptions)) {\r\n    VueComponent = vueOptions;\r\n  } else {\r\n    VueComponent = Vue.extend(vueOptions);\r\n  }\r\n  vueOptions = VueComponent.options;\r\n  return [VueComponent, vueOptions]\r\n}\r\n\r\nfunction initSlots (vm, vueSlots) {\r\n  if (Array.isArray(vueSlots) && vueSlots.length) {\r\n    const $slots = Object.create(null);\r\n    vueSlots.forEach(slotName => {\r\n      $slots[slotName] = true;\r\n    });\r\n    vm.$scopedSlots = vm.$slots = $slots;\r\n  }\r\n}\r\n\r\nfunction initVueIds (vueIds, mpInstance) {\r\n  vueIds = (vueIds || '').split(',');\r\n  const len = vueIds.length;\r\n\r\n  if (len === 1) {\r\n    mpInstance._$vueId = vueIds[0];\r\n  } else if (len === 2) {\r\n    mpInstance._$vueId = vueIds[0];\r\n    mpInstance._$vuePid = vueIds[1];\r\n  }\r\n}\r\n\r\nfunction initData (vueOptions, context) {\r\n  let data = vueOptions.data || {};\r\n  const methods = vueOptions.methods || {};\r\n\r\n  if (typeof data === 'function') {\r\n    try {\r\n      data = data.call(context); // 支持 Vue.prototype 上挂的数据\r\n    } catch (e) {\r\n      if (process.env.VUE_APP_DEBUG) {\r\n        console.warn('根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。', data);\r\n      }\r\n    }\r\n  } else {\r\n    try {\r\n      // 对 data 格式化\r\n      data = JSON.parse(JSON.stringify(data));\r\n    } catch (e) {}\r\n  }\r\n\r\n  if (!isPlainObject(data)) {\r\n    data = {};\r\n  }\r\n\r\n  Object.keys(methods).forEach(methodName => {\r\n    if (context.__lifecycle_hooks__.indexOf(methodName) === -1 && !hasOwn(data, methodName)) {\r\n      data[methodName] = methods[methodName];\r\n    }\r\n  });\r\n\r\n  return data\r\n}\r\n\r\nconst PROP_TYPES = [String, Number, Boolean, Object, Array, null];\r\n\r\nfunction createObserver (name) {\r\n  return function observer (newVal, oldVal) {\r\n    if (this.$vm) {\r\n      this.$vm[name] = newVal; // 为了触发其他非 render watcher\r\n    }\r\n  }\r\n}\r\n\r\nfunction initBehaviors (vueOptions, initBehavior) {\r\n  const vueBehaviors = vueOptions.behaviors;\r\n  const vueExtends = vueOptions.extends;\r\n  const vueMixins = vueOptions.mixins;\r\n\r\n  let vueProps = vueOptions.props;\r\n\r\n  if (!vueProps) {\r\n    vueOptions.props = vueProps = [];\r\n  }\r\n\r\n  const behaviors = [];\r\n  if (Array.isArray(vueBehaviors)) {\r\n    vueBehaviors.forEach(behavior => {\r\n      behaviors.push(behavior.replace('uni://', `${\"wx\"}://`));\r\n      if (behavior === 'uni://form-field') {\r\n        if (Array.isArray(vueProps)) {\r\n          vueProps.push('name');\r\n          vueProps.push('value');\r\n        } else {\r\n          vueProps.name = {\r\n            type: String,\r\n            default: ''\r\n          };\r\n          vueProps.value = {\r\n            type: [String, Number, Boolean, Array, Object, Date],\r\n            default: ''\r\n          };\r\n        }\r\n      }\r\n    });\r\n  }\r\n  if (isPlainObject(vueExtends) && vueExtends.props) {\r\n    behaviors.push(\r\n      initBehavior({\r\n        properties: initProperties(vueExtends.props, true)\r\n      })\r\n    );\r\n  }\r\n  if (Array.isArray(vueMixins)) {\r\n    vueMixins.forEach(vueMixin => {\r\n      if (isPlainObject(vueMixin) && vueMixin.props) {\r\n        behaviors.push(\r\n          initBehavior({\r\n            properties: initProperties(vueMixin.props, true)\r\n          })\r\n        );\r\n      }\r\n    });\r\n  }\r\n  return behaviors\r\n}\r\n\r\nfunction parsePropType (key, type, defaultValue, file) {\r\n  // [String]=>String\r\n  if (Array.isArray(type) && type.length === 1) {\r\n    return type[0]\r\n  }\r\n  return type\r\n}\r\n\r\nfunction initProperties (props, isBehavior = false, file = '') {\r\n  const properties = {};\r\n  if (!isBehavior) {\r\n    properties.vueId = {\r\n      type: String,\r\n      value: ''\r\n    };\r\n    // 用于字节跳动小程序模拟抽象节点\r\n    properties.generic = {\r\n      type: Object,\r\n      value: null\r\n    };\r\n    // scopedSlotsCompiler auto\r\n    properties.scopedSlotsCompiler = {\r\n      type: String,\r\n      value: ''\r\n    };\r\n    properties.vueSlots = { // 小程序不能直接定义 $slots 的 props，所以通过 vueSlots 转换到 $slots\r\n      type: null,\r\n      value: [],\r\n      observer: function (newVal, oldVal) {\r\n        const $slots = Object.create(null);\r\n        newVal.forEach(slotName => {\r\n          $slots[slotName] = true;\r\n        });\r\n        this.setData({\r\n          $slots\r\n        });\r\n      }\r\n    };\r\n  }\r\n  if (Array.isArray(props)) { // ['title']\r\n    props.forEach(key => {\r\n      properties[key] = {\r\n        type: null,\r\n        observer: createObserver(key)\r\n      };\r\n    });\r\n  } else if (isPlainObject(props)) { // {title:{type:String,default:''},content:String}\r\n    Object.keys(props).forEach(key => {\r\n      const opts = props[key];\r\n      if (isPlainObject(opts)) { // title:{type:String,default:''}\r\n        let value = opts.default;\r\n        if (isFn(value)) {\r\n          value = value();\r\n        }\r\n\r\n        opts.type = parsePropType(key, opts.type);\r\n\r\n        properties[key] = {\r\n          type: PROP_TYPES.indexOf(opts.type) !== -1 ? opts.type : null,\r\n          value,\r\n          observer: createObserver(key)\r\n        };\r\n      } else { // content:String\r\n        const type = parsePropType(key, opts);\r\n        properties[key] = {\r\n          type: PROP_TYPES.indexOf(type) !== -1 ? type : null,\r\n          observer: createObserver(key)\r\n        };\r\n      }\r\n    });\r\n  }\r\n  return properties\r\n}\r\n\r\nfunction wrapper$1 (event) {\r\n  // TODO 又得兼容 mpvue 的 mp 对象\r\n  try {\r\n    event.mp = JSON.parse(JSON.stringify(event));\r\n  } catch (e) {}\r\n\r\n  event.stopPropagation = noop;\r\n  event.preventDefault = noop;\r\n\r\n  event.target = event.target || {};\r\n\r\n  if (!hasOwn(event, 'detail')) {\r\n    event.detail = {};\r\n  }\r\n\r\n  if (hasOwn(event, 'markerId')) {\r\n    event.detail = typeof event.detail === 'object' ? event.detail : {};\r\n    event.detail.markerId = event.markerId;\r\n  }\r\n\r\n  if (isPlainObject(event.detail)) {\r\n    event.target = Object.assign({}, event.target, event.detail);\r\n  }\r\n\r\n  return event\r\n}\r\n\r\nfunction getExtraValue (vm, dataPathsArray) {\r\n  let context = vm;\r\n  dataPathsArray.forEach(dataPathArray => {\r\n    const dataPath = dataPathArray[0];\r\n    const value = dataPathArray[2];\r\n    if (dataPath || typeof value !== 'undefined') { // ['','',index,'disable']\r\n      const propPath = dataPathArray[1];\r\n      const valuePath = dataPathArray[3];\r\n\r\n      let vFor;\r\n      if (Number.isInteger(dataPath)) {\r\n        vFor = dataPath;\r\n      } else if (!dataPath) {\r\n        vFor = context;\r\n      } else if (typeof dataPath === 'string' && dataPath) {\r\n        if (dataPath.indexOf('#s#') === 0) {\r\n          vFor = dataPath.substr(3);\r\n        } else {\r\n          vFor = vm.__get_value(dataPath, context);\r\n        }\r\n      }\r\n\r\n      if (Number.isInteger(vFor)) {\r\n        context = value;\r\n      } else if (!propPath) {\r\n        context = vFor[value];\r\n      } else {\r\n        if (Array.isArray(vFor)) {\r\n          context = vFor.find(vForItem => {\r\n            return vm.__get_value(propPath, vForItem) === value\r\n          });\r\n        } else if (isPlainObject(vFor)) {\r\n          context = Object.keys(vFor).find(vForKey => {\r\n            return vm.__get_value(propPath, vFor[vForKey]) === value\r\n          });\r\n        } else {\r\n          console.error('v-for 暂不支持循环数据：', vFor);\r\n        }\r\n      }\r\n\r\n      if (valuePath) {\r\n        context = vm.__get_value(valuePath, context);\r\n      }\r\n    }\r\n  });\r\n  return context\r\n}\r\n\r\nfunction processEventExtra (vm, extra, event) {\r\n  const extraObj = {};\r\n\r\n  if (Array.isArray(extra) && extra.length) {\r\n    /**\r\n     *[\r\n     *    ['data.items', 'data.id', item.data.id],\r\n     *    ['metas', 'id', meta.id]\r\n     *],\r\n     *[\r\n     *    ['data.items', 'data.id', item.data.id],\r\n     *    ['metas', 'id', meta.id]\r\n     *],\r\n     *'test'\r\n     */\r\n    extra.forEach((dataPath, index) => {\r\n      if (typeof dataPath === 'string') {\r\n        if (!dataPath) { // model,prop.sync\r\n          extraObj['$' + index] = vm;\r\n        } else {\r\n          if (dataPath === '$event') { // $event\r\n            extraObj['$' + index] = event;\r\n          } else if (dataPath === 'arguments') {\r\n            if (event.detail && event.detail.__args__) {\r\n              extraObj['$' + index] = event.detail.__args__;\r\n            } else {\r\n              extraObj['$' + index] = [event];\r\n            }\r\n          } else if (dataPath.indexOf('$event.') === 0) { // $event.target.value\r\n            extraObj['$' + index] = vm.__get_value(dataPath.replace('$event.', ''), event);\r\n          } else {\r\n            extraObj['$' + index] = vm.__get_value(dataPath);\r\n          }\r\n        }\r\n      } else {\r\n        extraObj['$' + index] = getExtraValue(vm, dataPath);\r\n      }\r\n    });\r\n  }\r\n\r\n  return extraObj\r\n}\r\n\r\nfunction getObjByArray (arr) {\r\n  const obj = {};\r\n  for (let i = 1; i < arr.length; i++) {\r\n    const element = arr[i];\r\n    obj[element[0]] = element[1];\r\n  }\r\n  return obj\r\n}\r\n\r\nfunction processEventArgs (vm, event, args = [], extra = [], isCustom, methodName) {\r\n  let isCustomMPEvent = false; // wxcomponent 组件，传递原始 event 对象\r\n  if (isCustom) { // 自定义事件\r\n    isCustomMPEvent = event.currentTarget &&\r\n      event.currentTarget.dataset &&\r\n      event.currentTarget.dataset.comType === 'wx';\r\n    if (!args.length) { // 无参数，直接传入 event 或 detail 数组\r\n      if (isCustomMPEvent) {\r\n        return [event]\r\n      }\r\n      return event.detail.__args__ || event.detail\r\n    }\r\n  }\r\n\r\n  const extraObj = processEventExtra(vm, extra, event);\r\n\r\n  const ret = [];\r\n  args.forEach(arg => {\r\n    if (arg === '$event') {\r\n      if (methodName === '__set_model' && !isCustom) { // input v-model value\r\n        ret.push(event.target.value);\r\n      } else {\r\n        if (isCustom && !isCustomMPEvent) {\r\n          ret.push(event.detail.__args__[0]);\r\n        } else { // wxcomponent 组件或内置组件\r\n          ret.push(event);\r\n        }\r\n      }\r\n    } else {\r\n      if (Array.isArray(arg) && arg[0] === 'o') {\r\n        ret.push(getObjByArray(arg));\r\n      } else if (typeof arg === 'string' && hasOwn(extraObj, arg)) {\r\n        ret.push(extraObj[arg]);\r\n      } else {\r\n        ret.push(arg);\r\n      }\r\n    }\r\n  });\r\n\r\n  return ret\r\n}\r\n\r\nconst ONCE = '~';\r\nconst CUSTOM = '^';\r\n\r\nfunction isMatchEventType (eventType, optType) {\r\n  return (eventType === optType) ||\r\n    (\r\n      optType === 'regionchange' &&\r\n      (\r\n        eventType === 'begin' ||\r\n        eventType === 'end'\r\n      )\r\n    )\r\n}\r\n\r\nfunction getContextVm (vm) {\r\n  let $parent = vm.$parent;\r\n  // 父组件是 scoped slots 或者其他自定义组件时继续查找\r\n  while ($parent && $parent.$parent && ($parent.$options.generic || $parent.$parent.$options.generic || $parent.$scope._$vuePid)) {\r\n    $parent = $parent.$parent;\r\n  }\r\n  return $parent && $parent.$parent\r\n}\r\n\r\nfunction handleEvent (event) {\r\n  event = wrapper$1(event);\r\n\r\n  // [['tap',[['handle',[1,2,a]],['handle1',[1,2,a]]]]]\r\n  const dataset = (event.currentTarget || event.target).dataset;\r\n  if (!dataset) {\r\n    return console.warn('事件信息不存在')\r\n  }\r\n  const eventOpts = dataset.eventOpts || dataset['event-opts']; // 支付宝 web-view 组件 dataset 非驼峰\r\n  if (!eventOpts) {\r\n    return console.warn('事件信息不存在')\r\n  }\r\n\r\n  // [['handle',[1,2,a]],['handle1',[1,2,a]]]\r\n  const eventType = event.type;\r\n\r\n  const ret = [];\r\n\r\n  eventOpts.forEach(eventOpt => {\r\n    let type = eventOpt[0];\r\n    const eventsArray = eventOpt[1];\r\n\r\n    const isCustom = type.charAt(0) === CUSTOM;\r\n    type = isCustom ? type.slice(1) : type;\r\n    const isOnce = type.charAt(0) === ONCE;\r\n    type = isOnce ? type.slice(1) : type;\r\n\r\n    if (eventsArray && isMatchEventType(eventType, type)) {\r\n      eventsArray.forEach(eventArray => {\r\n        const methodName = eventArray[0];\r\n        if (methodName) {\r\n          let handlerCtx = this.$vm;\r\n          if (handlerCtx.$options.generic) { // mp-weixin,mp-toutiao 抽象节点模拟 scoped slots\r\n            handlerCtx = getContextVm(handlerCtx) || handlerCtx;\r\n          }\r\n          if (methodName === '$emit') {\r\n            handlerCtx.$emit.apply(handlerCtx,\r\n              processEventArgs(\r\n                this.$vm,\r\n                event,\r\n                eventArray[1],\r\n                eventArray[2],\r\n                isCustom,\r\n                methodName\r\n              ));\r\n            return\r\n          }\r\n          const handler = handlerCtx[methodName];\r\n          if (!isFn(handler)) {\r\n            throw new Error(` _vm.${methodName} is not a function`)\r\n          }\r\n          if (isOnce) {\r\n            if (handler.once) {\r\n              return\r\n            }\r\n            handler.once = true;\r\n          }\r\n          let params = processEventArgs(\r\n            this.$vm,\r\n            event,\r\n            eventArray[1],\r\n            eventArray[2],\r\n            isCustom,\r\n            methodName\r\n          );\r\n          params = Array.isArray(params) ? params : [];\r\n          // 参数尾部增加原始事件对象用于复杂表达式内获取额外数据\r\n          if (/=\\s*\\S+\\.eventParams\\s*\\|\\|\\s*\\S+\\[['\"]event-params['\"]\\]/.test(handler.toString())) {\r\n            // eslint-disable-next-line no-sparse-arrays\r\n            params = params.concat([, , , , , , , , , , event]);\r\n          }\r\n          ret.push(handler.apply(handlerCtx, params));\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  if (\r\n    eventType === 'input' &&\r\n    ret.length === 1 &&\r\n    typeof ret[0] !== 'undefined'\r\n  ) {\r\n    return ret[0]\r\n  }\r\n}\r\n\r\nconst messages = {};\r\n\r\nlet locale;\r\n\r\n{\r\n  locale = wx.getSystemInfoSync().language;\r\n}\r\n\r\nfunction initI18nMessages () {\r\n  if (!isEnableLocale()) {\r\n    return\r\n  }\r\n  const localeKeys = Object.keys(__uniConfig.locales);\r\n  if (localeKeys.length) {\r\n    localeKeys.forEach((locale) => {\r\n      const curMessages = messages[locale];\r\n      const userMessages = __uniConfig.locales[locale];\r\n      if (curMessages) {\r\n        Object.assign(curMessages, userMessages);\r\n      } else {\r\n        messages[locale] = userMessages;\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\ninitI18nMessages();\r\n\r\nconst i18n = initVueI18n(\r\n  locale,\r\n   {}\r\n);\r\nconst t = i18n.t;\r\nconst i18nMixin = (i18n.mixin = {\r\n  beforeCreate () {\r\n    const unwatch = i18n.i18n.watchLocale(() => {\r\n      this.$forceUpdate();\r\n    });\r\n    this.$once('hook:beforeDestroy', function () {\r\n      unwatch();\r\n    });\r\n  },\r\n  methods: {\r\n    $$t (key, values) {\r\n      return t(key, values)\r\n    }\r\n  }\r\n});\r\nconst setLocale$1 = i18n.setLocale;\r\nconst getLocale$1 = i18n.getLocale;\r\n\r\nfunction initAppLocale (Vue, appVm, locale) {\r\n  const state = Vue.observable({\r\n    locale: locale || i18n.getLocale()\r\n  });\r\n  const localeWatchers = [];\r\n  appVm.$watchLocale = fn => {\r\n    localeWatchers.push(fn);\r\n  };\r\n  Object.defineProperty(appVm, '$locale', {\r\n    get () {\r\n      return state.locale\r\n    },\r\n    set (v) {\r\n      state.locale = v;\r\n      localeWatchers.forEach(watch => watch(v));\r\n    }\r\n  });\r\n}\r\n\r\nfunction isEnableLocale () {\r\n  return typeof __uniConfig !== 'undefined' && __uniConfig.locales && !!Object.keys(__uniConfig.locales).length\r\n}\r\n\r\n// export function initI18n() {\r\n//   const localeKeys = Object.keys(__uniConfig.locales || {})\r\n//   if (localeKeys.length) {\r\n//     localeKeys.forEach((locale) =>\r\n//       i18n.add(locale, __uniConfig.locales[locale])\r\n//     )\r\n//   }\r\n// }\r\n\r\nconst eventChannels = {};\r\n\r\nconst eventChannelStack = [];\r\n\r\nfunction getEventChannel (id) {\r\n  if (id) {\r\n    const eventChannel = eventChannels[id];\r\n    delete eventChannels[id];\r\n    return eventChannel\r\n  }\r\n  return eventChannelStack.shift()\r\n}\r\n\r\nconst hooks = [\r\n  'onShow',\r\n  'onHide',\r\n  'onError',\r\n  'onPageNotFound',\r\n  'onThemeChange',\r\n  'onUnhandledRejection'\r\n];\r\n\r\nfunction initEventChannel () {\r\n  Vue.prototype.getOpenerEventChannel = function () {\r\n    // 微信小程序使用自身getOpenerEventChannel\r\n    {\r\n      return this.$scope.getOpenerEventChannel()\r\n    }\r\n  };\r\n  const callHook = Vue.prototype.__call_hook;\r\n  Vue.prototype.__call_hook = function (hook, args) {\r\n    if (hook === 'onLoad' && args && args.__id__) {\r\n      this.__eventChannel__ = getEventChannel(args.__id__);\r\n      delete args.__id__;\r\n    }\r\n    return callHook.call(this, hook, args)\r\n  };\r\n}\r\n\r\nfunction initScopedSlotsParams () {\r\n  const center = {};\r\n  const parents = {};\r\n\r\n  Vue.prototype.$hasScopedSlotsParams = function (vueId) {\r\n    const has = center[vueId];\r\n    if (!has) {\r\n      parents[vueId] = this;\r\n      this.$on('hook:destroyed', () => {\r\n        delete parents[vueId];\r\n      });\r\n    }\r\n    return has\r\n  };\r\n\r\n  Vue.prototype.$getScopedSlotsParams = function (vueId, name, key) {\r\n    const data = center[vueId];\r\n    if (data) {\r\n      const object = data[name] || {};\r\n      return key ? object[key] : object\r\n    } else {\r\n      parents[vueId] = this;\r\n      this.$on('hook:destroyed', () => {\r\n        delete parents[vueId];\r\n      });\r\n    }\r\n  };\r\n\r\n  Vue.prototype.$setScopedSlotsParams = function (name, value) {\r\n    const vueIds = this.$options.propsData.vueId;\r\n    if (vueIds) {\r\n      const vueId = vueIds.split(',')[0];\r\n      const object = center[vueId] = center[vueId] || {};\r\n      object[name] = value;\r\n      if (parents[vueId]) {\r\n        parents[vueId].$forceUpdate();\r\n      }\r\n    }\r\n  };\r\n\r\n  Vue.mixin({\r\n    destroyed () {\r\n      const propsData = this.$options.propsData;\r\n      const vueId = propsData && propsData.vueId;\r\n      if (vueId) {\r\n        delete center[vueId];\r\n        delete parents[vueId];\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nfunction parseBaseApp (vm, {\r\n  mocks,\r\n  initRefs\r\n}) {\r\n  initEventChannel();\r\n  {\r\n    initScopedSlotsParams();\r\n  }\r\n  if (vm.$options.store) {\r\n    Vue.prototype.$store = vm.$options.store;\r\n  }\r\n  uniIdMixin(Vue);\r\n\r\n  Vue.prototype.mpHost = \"mp-weixin\";\r\n\r\n  Vue.mixin({\r\n    beforeCreate () {\r\n      if (!this.$options.mpType) {\r\n        return\r\n      }\r\n\r\n      this.mpType = this.$options.mpType;\r\n\r\n      this.$mp = {\r\n        data: {},\r\n        [this.mpType]: this.$options.mpInstance\r\n      };\r\n\r\n      this.$scope = this.$options.mpInstance;\r\n\r\n      delete this.$options.mpType;\r\n      delete this.$options.mpInstance;\r\n      if (this.mpType === 'page' && typeof getApp === 'function') { // hack vue-i18n\r\n        const app = getApp();\r\n        if (app.$vm && app.$vm.$i18n) {\r\n          this._i18n = app.$vm.$i18n;\r\n        }\r\n      }\r\n      if (this.mpType !== 'app') {\r\n        initRefs(this);\r\n        initMocks(this, mocks);\r\n      }\r\n    }\r\n  });\r\n\r\n  const appOptions = {\r\n    onLaunch (args) {\r\n      if (this.$vm) { // 已经初始化过了，主要是为了百度，百度 onShow 在 onLaunch 之前\r\n        return\r\n      }\r\n      {\r\n        if (wx.canIUse && !wx.canIUse('nextTick')) { // 事实 上2.2.3 即可，简单使用 2.3.0 的 nextTick 判断\r\n          console.error('当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上');\r\n        }\r\n      }\r\n\r\n      this.$vm = vm;\r\n\r\n      this.$vm.$mp = {\r\n        app: this\r\n      };\r\n\r\n      this.$vm.$scope = this;\r\n      // vm 上也挂载 globalData\r\n      this.$vm.globalData = this.globalData;\r\n\r\n      this.$vm._isMounted = true;\r\n      this.$vm.__call_hook('mounted', args);\r\n\r\n      this.$vm.__call_hook('onLaunch', args);\r\n    }\r\n  };\r\n\r\n  // 兼容旧版本 globalData\r\n  appOptions.globalData = vm.$options.globalData || {};\r\n  // 将 methods 中的方法挂在 getApp() 中\r\n  const methods = vm.$options.methods;\r\n  if (methods) {\r\n    Object.keys(methods).forEach(name => {\r\n      appOptions[name] = methods[name];\r\n    });\r\n  }\r\n\r\n  initAppLocale(Vue, vm, wx.getSystemInfoSync().language || 'zh-Hans');\r\n\r\n  initHooks(appOptions, hooks);\r\n\r\n  return appOptions\r\n}\r\n\r\nconst mocks = ['__route__', '__wxExparserNodeId__', '__wxWebviewId__'];\r\n\r\nfunction findVmByVueId (vm, vuePid) {\r\n  const $children = vm.$children;\r\n  // 优先查找直属(反向查找:https://github.com/dcloudio/uni-app/issues/1200)\r\n  for (let i = $children.length - 1; i >= 0; i--) {\r\n    const childVm = $children[i];\r\n    if (childVm.$scope._$vueId === vuePid) {\r\n      return childVm\r\n    }\r\n  }\r\n  // 反向递归查找\r\n  let parentVm;\r\n  for (let i = $children.length - 1; i >= 0; i--) {\r\n    parentVm = findVmByVueId($children[i], vuePid);\r\n    if (parentVm) {\r\n      return parentVm\r\n    }\r\n  }\r\n}\r\n\r\nfunction initBehavior (options) {\r\n  return Behavior(options)\r\n}\r\n\r\nfunction isPage () {\r\n  return !!this.route\r\n}\r\n\r\nfunction initRelation (detail) {\r\n  this.triggerEvent('__l', detail);\r\n}\r\n\r\nfunction selectAllComponents (mpInstance, selector, $refs) {\r\n  const components = mpInstance.selectAllComponents(selector);\r\n  components.forEach(component => {\r\n    const ref = component.dataset.ref;\r\n    $refs[ref] = component.$vm || component;\r\n    {\r\n      if (component.dataset.vueGeneric === 'scoped') {\r\n        component.selectAllComponents('.scoped-ref').forEach(scopedComponent => {\r\n          selectAllComponents(scopedComponent, selector, $refs);\r\n        });\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nfunction initRefs (vm) {\r\n  const mpInstance = vm.$scope;\r\n  Object.defineProperty(vm, '$refs', {\r\n    get () {\r\n      const $refs = {};\r\n      selectAllComponents(mpInstance, '.vue-ref', $refs);\r\n      // TODO 暂不考虑 for 中的 scoped\r\n      const forComponents = mpInstance.selectAllComponents('.vue-ref-in-for');\r\n      forComponents.forEach(component => {\r\n        const ref = component.dataset.ref;\r\n        if (!$refs[ref]) {\r\n          $refs[ref] = [];\r\n        }\r\n        $refs[ref].push(component.$vm || component);\r\n      });\r\n      return $refs\r\n    }\r\n  });\r\n}\r\n\r\nfunction handleLink (event) {\r\n  const {\r\n    vuePid,\r\n    vueOptions\r\n  } = event.detail || event.value; // detail 是微信,value 是百度(dipatch)\r\n\r\n  let parentVm;\r\n\r\n  if (vuePid) {\r\n    parentVm = findVmByVueId(this.$vm, vuePid);\r\n  }\r\n\r\n  if (!parentVm) {\r\n    parentVm = this.$vm;\r\n  }\r\n\r\n  vueOptions.parent = parentVm;\r\n}\r\n\r\nfunction parseApp (vm) {\r\n  return parseBaseApp(vm, {\r\n    mocks,\r\n    initRefs\r\n  })\r\n}\r\n\r\nfunction createApp (vm) {\r\n  App(parseApp(vm));\r\n  return vm\r\n}\r\n\r\nconst encodeReserveRE = /[!'()*]/g;\r\nconst encodeReserveReplacer = c => '%' + c.charCodeAt(0).toString(16);\r\nconst commaRE = /%2C/g;\r\n\r\n// fixed encodeURIComponent which is more conformant to RFC3986:\r\n// - escapes [!'()*]\r\n// - preserve commas\r\nconst encode = str => encodeURIComponent(str)\r\n  .replace(encodeReserveRE, encodeReserveReplacer)\r\n  .replace(commaRE, ',');\r\n\r\nfunction stringifyQuery (obj, encodeStr = encode) {\r\n  const res = obj ? Object.keys(obj).map(key => {\r\n    const val = obj[key];\r\n\r\n    if (val === undefined) {\r\n      return ''\r\n    }\r\n\r\n    if (val === null) {\r\n      return encodeStr(key)\r\n    }\r\n\r\n    if (Array.isArray(val)) {\r\n      const result = [];\r\n      val.forEach(val2 => {\r\n        if (val2 === undefined) {\r\n          return\r\n        }\r\n        if (val2 === null) {\r\n          result.push(encodeStr(key));\r\n        } else {\r\n          result.push(encodeStr(key) + '=' + encodeStr(val2));\r\n        }\r\n      });\r\n      return result.join('&')\r\n    }\r\n\r\n    return encodeStr(key) + '=' + encodeStr(val)\r\n  }).filter(x => x.length > 0).join('&') : null;\r\n  return res ? `?${res}` : ''\r\n}\r\n\r\nfunction parseBaseComponent (vueComponentOptions, {\r\n  isPage,\r\n  initRelation\r\n} = {}) {\r\n  const [VueComponent, vueOptions] = initVueComponent(Vue, vueComponentOptions);\r\n\r\n  const options = {\r\n    multipleSlots: true,\r\n    addGlobalClass: true,\r\n    ...(vueOptions.options || {})\r\n  };\r\n\r\n  {\r\n    // 微信 multipleSlots 部分情况有 bug，导致内容顺序错乱 如 u-list，提供覆盖选项\r\n    if (vueOptions['mp-weixin'] && vueOptions['mp-weixin'].options) {\r\n      Object.assign(options, vueOptions['mp-weixin'].options);\r\n    }\r\n  }\r\n\r\n  const componentOptions = {\r\n    options,\r\n    data: initData(vueOptions, Vue.prototype),\r\n    behaviors: initBehaviors(vueOptions, initBehavior),\r\n    properties: initProperties(vueOptions.props, false, vueOptions.__file),\r\n    lifetimes: {\r\n      attached () {\r\n        const properties = this.properties;\r\n\r\n        const options = {\r\n          mpType: isPage.call(this) ? 'page' : 'component',\r\n          mpInstance: this,\r\n          propsData: properties\r\n        };\r\n\r\n        initVueIds(properties.vueId, this);\r\n\r\n        // 处理父子关系\r\n        initRelation.call(this, {\r\n          vuePid: this._$vuePid,\r\n          vueOptions: options\r\n        });\r\n\r\n        // 初始化 vue 实例\r\n        this.$vm = new VueComponent(options);\r\n\r\n        // 处理$slots,$scopedSlots（暂不支持动态变化$slots）\r\n        initSlots(this.$vm, properties.vueSlots);\r\n\r\n        // 触发首次 setData\r\n        this.$vm.$mount();\r\n      },\r\n      ready () {\r\n        // 当组件 props 默认值为 true，初始化时传入 false 会导致 created,ready 触发, 但 attached 不触发\r\n        // https://developers.weixin.qq.com/community/develop/doc/00066ae2844cc0f8eb883e2a557800\r\n        if (this.$vm) {\r\n          this.$vm._isMounted = true;\r\n          this.$vm.__call_hook('mounted');\r\n          this.$vm.__call_hook('onReady');\r\n        }\r\n      },\r\n      detached () {\r\n        this.$vm && this.$vm.$destroy();\r\n      }\r\n    },\r\n    pageLifetimes: {\r\n      show (args) {\r\n        this.$vm && this.$vm.__call_hook('onPageShow', args);\r\n      },\r\n      hide () {\r\n        this.$vm && this.$vm.__call_hook('onPageHide');\r\n      },\r\n      resize (size) {\r\n        this.$vm && this.$vm.__call_hook('onPageResize', size);\r\n      }\r\n    },\r\n    methods: {\r\n      __l: handleLink,\r\n      __e: handleEvent\r\n    }\r\n  };\r\n  // externalClasses\r\n  if (vueOptions.externalClasses) {\r\n    componentOptions.externalClasses = vueOptions.externalClasses;\r\n  }\r\n\r\n  if (Array.isArray(vueOptions.wxsCallMethods)) {\r\n    vueOptions.wxsCallMethods.forEach(callMethod => {\r\n      componentOptions.methods[callMethod] = function (args) {\r\n        return this.$vm[callMethod](args)\r\n      };\r\n    });\r\n  }\r\n\r\n  if (isPage) {\r\n    return componentOptions\r\n  }\r\n  return [componentOptions, VueComponent]\r\n}\r\n\r\nfunction parseComponent (vueComponentOptions) {\r\n  return parseBaseComponent(vueComponentOptions, {\r\n    isPage,\r\n    initRelation\r\n  })\r\n}\r\n\r\nconst hooks$1 = [\r\n  'onShow',\r\n  'onHide',\r\n  'onUnload'\r\n];\r\n\r\nhooks$1.push(...PAGE_EVENT_HOOKS);\r\n\r\nfunction parseBasePage (vuePageOptions, {\r\n  isPage,\r\n  initRelation\r\n}) {\r\n  const pageOptions = parseComponent(vuePageOptions);\r\n\r\n  initHooks(pageOptions.methods, hooks$1, vuePageOptions);\r\n\r\n  pageOptions.methods.onLoad = function (query) {\r\n    this.options = query;\r\n    const copyQuery = Object.assign({}, query);\r\n    delete copyQuery.__id__;\r\n    this.$page = {\r\n      fullPath: '/' + (this.route || this.is) + stringifyQuery(copyQuery)\r\n    };\r\n    this.$vm.$mp.query = query; // 兼容 mpvue\r\n    this.$vm.__call_hook('onLoad', query);\r\n  };\r\n\r\n  return pageOptions\r\n}\r\n\r\nfunction parsePage (vuePageOptions) {\r\n  return parseBasePage(vuePageOptions, {\r\n    isPage,\r\n    initRelation\r\n  })\r\n}\r\n\r\nfunction createPage (vuePageOptions) {\r\n  {\r\n    return Component(parsePage(vuePageOptions))\r\n  }\r\n}\r\n\r\nfunction createComponent (vueOptions) {\r\n  {\r\n    return Component(parseComponent(vueOptions))\r\n  }\r\n}\r\n\r\nfunction createSubpackageApp (vm) {\r\n  const appOptions = parseApp(vm);\r\n  const app = getApp({\r\n    allowDefault: true\r\n  });\r\n  vm.$scope = app;\r\n  const globalData = app.globalData;\r\n  if (globalData) {\r\n    Object.keys(appOptions.globalData).forEach(name => {\r\n      if (!hasOwn(globalData, name)) {\r\n        globalData[name] = appOptions.globalData[name];\r\n      }\r\n    });\r\n  }\r\n  Object.keys(appOptions).forEach(name => {\r\n    if (!hasOwn(app, name)) {\r\n      app[name] = appOptions[name];\r\n    }\r\n  });\r\n  if (isFn(appOptions.onShow) && wx.onAppShow) {\r\n    wx.onAppShow((...args) => {\r\n      vm.__call_hook('onShow', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onHide) && wx.onAppHide) {\r\n    wx.onAppHide((...args) => {\r\n      vm.__call_hook('onHide', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onLaunch)) {\r\n    const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\r\n    vm.__call_hook('onLaunch', args);\r\n  }\r\n  return vm\r\n}\r\n\r\nfunction createPlugin (vm) {\r\n  const appOptions = parseApp(vm);\r\n  if (isFn(appOptions.onShow) && wx.onAppShow) {\r\n    wx.onAppShow((...args) => {\r\n      appOptions.onShow.apply(vm, args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onHide) && wx.onAppHide) {\r\n    wx.onAppHide((...args) => {\r\n      appOptions.onHide.apply(vm, args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onLaunch)) {\r\n    const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\r\n    appOptions.onLaunch.call(vm, args);\r\n  }\r\n  return vm\r\n}\r\n\r\ntodos.forEach(todoApi => {\r\n  protocols[todoApi] = false;\r\n});\r\n\r\ncanIUses.forEach(canIUseApi => {\r\n  const apiName = protocols[canIUseApi] && protocols[canIUseApi].name ? protocols[canIUseApi].name\r\n    : canIUseApi;\r\n  if (!wx.canIUse(apiName)) {\r\n    protocols[canIUseApi] = false;\r\n  }\r\n});\r\n\r\nlet uni = {};\r\n\r\nif (typeof Proxy !== 'undefined' && \"mp-weixin\" !== 'app-plus') {\r\n  uni = new Proxy({}, {\r\n    get (target, name) {\r\n      if (hasOwn(target, name)) {\r\n        return target[name]\r\n      }\r\n      if (baseApi[name]) {\r\n        return baseApi[name]\r\n      }\r\n      if (api[name]) {\r\n        return promisify(name, api[name])\r\n      }\r\n      {\r\n        if (extraApi[name]) {\r\n          return promisify(name, extraApi[name])\r\n        }\r\n        if (todoApis[name]) {\r\n          return promisify(name, todoApis[name])\r\n        }\r\n      }\r\n      if (eventApi[name]) {\r\n        return eventApi[name]\r\n      }\r\n      if (!hasOwn(wx, name) && !hasOwn(protocols, name)) {\r\n        return\r\n      }\r\n      return promisify(name, wrapper(name, wx[name]))\r\n    },\r\n    set (target, name, value) {\r\n      target[name] = value;\r\n      return true\r\n    }\r\n  });\r\n} else {\r\n  Object.keys(baseApi).forEach(name => {\r\n    uni[name] = baseApi[name];\r\n  });\r\n\r\n  {\r\n    Object.keys(todoApis).forEach(name => {\r\n      uni[name] = promisify(name, todoApis[name]);\r\n    });\r\n    Object.keys(extraApi).forEach(name => {\r\n      uni[name] = promisify(name, todoApis[name]);\r\n    });\r\n  }\r\n\r\n  Object.keys(eventApi).forEach(name => {\r\n    uni[name] = eventApi[name];\r\n  });\r\n\r\n  Object.keys(api).forEach(name => {\r\n    uni[name] = promisify(name, api[name]);\r\n  });\r\n\r\n  Object.keys(wx).forEach(name => {\r\n    if (hasOwn(wx, name) || hasOwn(protocols, name)) {\r\n      uni[name] = promisify(name, wrapper(name, wx[name]));\r\n    }\r\n  });\r\n}\r\n\r\nwx.createApp = createApp;\r\nwx.createPage = createPage;\r\nwx.createComponent = createComponent;\r\nwx.createSubpackageApp = createSubpackageApp;\r\nwx.createPlugin = createPlugin;\r\n\r\nvar uni$1 = uni;\r\n\r\nexport default uni$1;\r\nexport { createApp, createComponent, createPage, createPlugin, createSubpackageApp };\r\n", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode, /* vue-cli only */\n  components, // fixed by xxxxxx auto components\n  renderjs // fixed by xxxxxx renderjs\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // fixed by xxxxxx auto components\n  if (components) {\n    if (!options.components) {\n      options.components = {}\n    }\n    var hasOwn = Object.prototype.hasOwnProperty\n    for (var name in components) {\n      if (hasOwn.call(components, name) && !hasOwn.call(options.components, name)) {\n        options.components[name] = components[name]\n      }\n    }\n  }\n  // fixed by xxxxxx renderjs\n  if (renderjs) {\n    (renderjs.beforeCreate || (renderjs.beforeCreate = [])).unshift(function() {\n      this[renderjs.__module] = this\n    });\n    (options.mixins || (options.mixins = [])).push(renderjs)\n  }\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n", "/**\n * 地图API配置文件\n * 使用前请申请相应的API密钥\n */\n\nexport const MAP_CONFIG = {\n  // 腾讯地图API配置\n  // 申请地址：https://lbs.qq.com/\n  TENCENT: {\n    KEY: 'NHWBZ-W5MRZ-M6VX4-7ZHPF-UEVS6-PSFC3', // 请替换为你的腾讯地图API密钥\n    GEOCODER_URL: 'https://apis.map.qq.com/ws/geocoder/v1/'\n  },\n\n  // 高德地图API配置\n  // 申请地址：https://console.amap.com/\n  AMAP: {\n    KEY: 'YOUR_AMAP_KEY', // 请替换为你的高德地图API密钥\n    GEOCODER_URL: 'https://restapi.amap.com/v3/geocode/regeo'\n  },\n\n  // 百度地图API配置（备用）\n  // 申请地址：https://lbsyun.baidu.com/\n  BAIDU: {\n    KEY: 'YOUR_BAIDU_MAP_KEY', // 请替换为你的百度地图API密钥\n    GEOCODER_URL: 'https://api.map.baidu.com/reverse_geocoding/v3/'\n  }\n};\n\n/**\n * 检查API密钥是否已配置\n * @param {string} provider - 地图服务提供商 ('TENCENT', 'AMAP', 'BAIDU')\n * @returns {boolean}\n */\nexport function isApiKeyConfigured(provider) {\n  const config = MAP_CONFIG[provider];\n  return config && config.KEY && !config.KEY.startsWith('YOUR_');\n}\n\n/**\n * 获取可用的地图服务提供商\n * @returns {string[]} 已配置密钥的提供商列表\n */\nexport function getAvailableProviders() {\n  return Object.keys(MAP_CONFIG).filter(provider => isApiKeyConfigured(provider));\n}\n", "\r\nexport const fontData = [\n  {\n    \"font_class\": \"arrow-down\",\n    \"unicode\": \"\\ue6be\"\n  },\n  {\n    \"font_class\": \"arrow-left\",\n    \"unicode\": \"\\ue6bc\"\n  },\n  {\n    \"font_class\": \"arrow-right\",\n    \"unicode\": \"\\ue6bb\"\n  },\n  {\n    \"font_class\": \"arrow-up\",\n    \"unicode\": \"\\ue6bd\"\n  },\n  {\n    \"font_class\": \"auth\",\n    \"unicode\": \"\\ue6ab\"\n  },\n  {\n    \"font_class\": \"auth-filled\",\n    \"unicode\": \"\\ue6cc\"\n  },\n  {\n    \"font_class\": \"back\",\n    \"unicode\": \"\\ue6b9\"\n  },\n  {\n    \"font_class\": \"bars\",\n    \"unicode\": \"\\ue627\"\n  },\n  {\n    \"font_class\": \"calendar\",\n    \"unicode\": \"\\ue6a0\"\n  },\n  {\n    \"font_class\": \"calendar-filled\",\n    \"unicode\": \"\\ue6c0\"\n  },\n  {\n    \"font_class\": \"camera\",\n    \"unicode\": \"\\ue65a\"\n  },\n  {\n    \"font_class\": \"camera-filled\",\n    \"unicode\": \"\\ue658\"\n  },\n  {\n    \"font_class\": \"cart\",\n    \"unicode\": \"\\ue631\"\n  },\n  {\n    \"font_class\": \"cart-filled\",\n    \"unicode\": \"\\ue6d0\"\n  },\n  {\n    \"font_class\": \"chat\",\n    \"unicode\": \"\\ue65d\"\n  },\n  {\n    \"font_class\": \"chat-filled\",\n    \"unicode\": \"\\ue659\"\n  },\n  {\n    \"font_class\": \"chatboxes\",\n    \"unicode\": \"\\ue696\"\n  },\n  {\n    \"font_class\": \"chatboxes-filled\",\n    \"unicode\": \"\\ue692\"\n  },\n  {\n    \"font_class\": \"chatbubble\",\n    \"unicode\": \"\\ue697\"\n  },\n  {\n    \"font_class\": \"chatbubble-filled\",\n    \"unicode\": \"\\ue694\"\n  },\n  {\n    \"font_class\": \"checkbox\",\n    \"unicode\": \"\\ue62b\"\n  },\n  {\n    \"font_class\": \"checkbox-filled\",\n    \"unicode\": \"\\ue62c\"\n  },\n  {\n    \"font_class\": \"checkmarkempty\",\n    \"unicode\": \"\\ue65c\"\n  },\n  {\n    \"font_class\": \"circle\",\n    \"unicode\": \"\\ue65b\"\n  },\n  {\n    \"font_class\": \"circle-filled\",\n    \"unicode\": \"\\ue65e\"\n  },\n  {\n    \"font_class\": \"clear\",\n    \"unicode\": \"\\ue66d\"\n  },\n  {\n    \"font_class\": \"close\",\n    \"unicode\": \"\\ue673\"\n  },\n  {\n    \"font_class\": \"closeempty\",\n    \"unicode\": \"\\ue66c\"\n  },\n  {\n    \"font_class\": \"cloud-download\",\n    \"unicode\": \"\\ue647\"\n  },\n  {\n    \"font_class\": \"cloud-download-filled\",\n    \"unicode\": \"\\ue646\"\n  },\n  {\n    \"font_class\": \"cloud-upload\",\n    \"unicode\": \"\\ue645\"\n  },\n  {\n    \"font_class\": \"cloud-upload-filled\",\n    \"unicode\": \"\\ue648\"\n  },\n  {\n    \"font_class\": \"color\",\n    \"unicode\": \"\\ue6cf\"\n  },\n  {\n    \"font_class\": \"color-filled\",\n    \"unicode\": \"\\ue6c9\"\n  },\n  {\n    \"font_class\": \"compose\",\n    \"unicode\": \"\\ue67f\"\n  },\n  {\n    \"font_class\": \"contact\",\n    \"unicode\": \"\\ue693\"\n  },\n  {\n    \"font_class\": \"contact-filled\",\n    \"unicode\": \"\\ue695\"\n  },\n  {\n    \"font_class\": \"down\",\n    \"unicode\": \"\\ue6b8\"\n  },\n\t{\n\t  \"font_class\": \"bottom\",\n\t  \"unicode\": \"\\ue6b8\"\n\t},\n  {\n    \"font_class\": \"download\",\n    \"unicode\": \"\\ue68d\"\n  },\n  {\n    \"font_class\": \"download-filled\",\n    \"unicode\": \"\\ue681\"\n  },\n  {\n    \"font_class\": \"email\",\n    \"unicode\": \"\\ue69e\"\n  },\n  {\n    \"font_class\": \"email-filled\",\n    \"unicode\": \"\\ue69a\"\n  },\n  {\n    \"font_class\": \"eye\",\n    \"unicode\": \"\\ue651\"\n  },\n  {\n    \"font_class\": \"eye-filled\",\n    \"unicode\": \"\\ue66a\"\n  },\n  {\n    \"font_class\": \"eye-slash\",\n    \"unicode\": \"\\ue6b3\"\n  },\n  {\n    \"font_class\": \"eye-slash-filled\",\n    \"unicode\": \"\\ue6b4\"\n  },\n  {\n    \"font_class\": \"fire\",\n    \"unicode\": \"\\ue6a1\"\n  },\n  {\n    \"font_class\": \"fire-filled\",\n    \"unicode\": \"\\ue6c5\"\n  },\n  {\n    \"font_class\": \"flag\",\n    \"unicode\": \"\\ue65f\"\n  },\n  {\n    \"font_class\": \"flag-filled\",\n    \"unicode\": \"\\ue660\"\n  },\n  {\n    \"font_class\": \"folder-add\",\n    \"unicode\": \"\\ue6a9\"\n  },\n  {\n    \"font_class\": \"folder-add-filled\",\n    \"unicode\": \"\\ue6c8\"\n  },\n  {\n    \"font_class\": \"font\",\n    \"unicode\": \"\\ue6a3\"\n  },\n  {\n    \"font_class\": \"forward\",\n    \"unicode\": \"\\ue6ba\"\n  },\n  {\n    \"font_class\": \"gear\",\n    \"unicode\": \"\\ue664\"\n  },\n  {\n    \"font_class\": \"gear-filled\",\n    \"unicode\": \"\\ue661\"\n  },\n  {\n    \"font_class\": \"gift\",\n    \"unicode\": \"\\ue6a4\"\n  },\n  {\n    \"font_class\": \"gift-filled\",\n    \"unicode\": \"\\ue6c4\"\n  },\n  {\n    \"font_class\": \"hand-down\",\n    \"unicode\": \"\\ue63d\"\n  },\n  {\n    \"font_class\": \"hand-down-filled\",\n    \"unicode\": \"\\ue63c\"\n  },\n  {\n    \"font_class\": \"hand-up\",\n    \"unicode\": \"\\ue63f\"\n  },\n  {\n    \"font_class\": \"hand-up-filled\",\n    \"unicode\": \"\\ue63e\"\n  },\n  {\n    \"font_class\": \"headphones\",\n    \"unicode\": \"\\ue630\"\n  },\n  {\n    \"font_class\": \"heart\",\n    \"unicode\": \"\\ue639\"\n  },\n  {\n    \"font_class\": \"heart-filled\",\n    \"unicode\": \"\\ue641\"\n  },\n  {\n    \"font_class\": \"help\",\n    \"unicode\": \"\\ue679\"\n  },\n  {\n    \"font_class\": \"help-filled\",\n    \"unicode\": \"\\ue674\"\n  },\n  {\n    \"font_class\": \"home\",\n    \"unicode\": \"\\ue662\"\n  },\n  {\n    \"font_class\": \"home-filled\",\n    \"unicode\": \"\\ue663\"\n  },\n  {\n    \"font_class\": \"image\",\n    \"unicode\": \"\\ue670\"\n  },\n  {\n    \"font_class\": \"image-filled\",\n    \"unicode\": \"\\ue678\"\n  },\n  {\n    \"font_class\": \"images\",\n    \"unicode\": \"\\ue650\"\n  },\n  {\n    \"font_class\": \"images-filled\",\n    \"unicode\": \"\\ue64b\"\n  },\n  {\n    \"font_class\": \"info\",\n    \"unicode\": \"\\ue669\"\n  },\n  {\n    \"font_class\": \"info-filled\",\n    \"unicode\": \"\\ue649\"\n  },\n  {\n    \"font_class\": \"left\",\n    \"unicode\": \"\\ue6b7\"\n  },\n  {\n    \"font_class\": \"link\",\n    \"unicode\": \"\\ue6a5\"\n  },\n  {\n    \"font_class\": \"list\",\n    \"unicode\": \"\\ue644\"\n  },\n  {\n    \"font_class\": \"location\",\n    \"unicode\": \"\\ue6ae\"\n  },\n  {\n    \"font_class\": \"location-filled\",\n    \"unicode\": \"\\ue6af\"\n  },\n  {\n    \"font_class\": \"locked\",\n    \"unicode\": \"\\ue66b\"\n  },\n  {\n    \"font_class\": \"locked-filled\",\n    \"unicode\": \"\\ue668\"\n  },\n  {\n    \"font_class\": \"loop\",\n    \"unicode\": \"\\ue633\"\n  },\n  {\n    \"font_class\": \"mail-open\",\n    \"unicode\": \"\\ue643\"\n  },\n  {\n    \"font_class\": \"mail-open-filled\",\n    \"unicode\": \"\\ue63a\"\n  },\n  {\n    \"font_class\": \"map\",\n    \"unicode\": \"\\ue667\"\n  },\n  {\n    \"font_class\": \"map-filled\",\n    \"unicode\": \"\\ue666\"\n  },\n  {\n    \"font_class\": \"map-pin\",\n    \"unicode\": \"\\ue6ad\"\n  },\n  {\n    \"font_class\": \"map-pin-ellipse\",\n    \"unicode\": \"\\ue6ac\"\n  },\n  {\n    \"font_class\": \"medal\",\n    \"unicode\": \"\\ue6a2\"\n  },\n  {\n    \"font_class\": \"medal-filled\",\n    \"unicode\": \"\\ue6c3\"\n  },\n  {\n    \"font_class\": \"mic\",\n    \"unicode\": \"\\ue671\"\n  },\n  {\n    \"font_class\": \"mic-filled\",\n    \"unicode\": \"\\ue677\"\n  },\n  {\n    \"font_class\": \"micoff\",\n    \"unicode\": \"\\ue67e\"\n  },\n  {\n    \"font_class\": \"micoff-filled\",\n    \"unicode\": \"\\ue6b0\"\n  },\n  {\n    \"font_class\": \"minus\",\n    \"unicode\": \"\\ue66f\"\n  },\n  {\n    \"font_class\": \"minus-filled\",\n    \"unicode\": \"\\ue67d\"\n  },\n  {\n    \"font_class\": \"more\",\n    \"unicode\": \"\\ue64d\"\n  },\n  {\n    \"font_class\": \"more-filled\",\n    \"unicode\": \"\\ue64e\"\n  },\n  {\n    \"font_class\": \"navigate\",\n    \"unicode\": \"\\ue66e\"\n  },\n  {\n    \"font_class\": \"navigate-filled\",\n    \"unicode\": \"\\ue67a\"\n  },\n  {\n    \"font_class\": \"notification\",\n    \"unicode\": \"\\ue6a6\"\n  },\n  {\n    \"font_class\": \"notification-filled\",\n    \"unicode\": \"\\ue6c1\"\n  },\n  {\n    \"font_class\": \"paperclip\",\n    \"unicode\": \"\\ue652\"\n  },\n  {\n    \"font_class\": \"paperplane\",\n    \"unicode\": \"\\ue672\"\n  },\n  {\n    \"font_class\": \"paperplane-filled\",\n    \"unicode\": \"\\ue675\"\n  },\n  {\n    \"font_class\": \"person\",\n    \"unicode\": \"\\ue699\"\n  },\n  {\n    \"font_class\": \"person-filled\",\n    \"unicode\": \"\\ue69d\"\n  },\n  {\n    \"font_class\": \"personadd\",\n    \"unicode\": \"\\ue69f\"\n  },\n  {\n    \"font_class\": \"personadd-filled\",\n    \"unicode\": \"\\ue698\"\n  },\n  {\n    \"font_class\": \"personadd-filled-copy\",\n    \"unicode\": \"\\ue6d1\"\n  },\n  {\n    \"font_class\": \"phone\",\n    \"unicode\": \"\\ue69c\"\n  },\n  {\n    \"font_class\": \"phone-filled\",\n    \"unicode\": \"\\ue69b\"\n  },\n  {\n    \"font_class\": \"plus\",\n    \"unicode\": \"\\ue676\"\n  },\n  {\n    \"font_class\": \"plus-filled\",\n    \"unicode\": \"\\ue6c7\"\n  },\n  {\n    \"font_class\": \"plusempty\",\n    \"unicode\": \"\\ue67b\"\n  },\n  {\n    \"font_class\": \"pulldown\",\n    \"unicode\": \"\\ue632\"\n  },\n  {\n    \"font_class\": \"pyq\",\n    \"unicode\": \"\\ue682\"\n  },\n  {\n    \"font_class\": \"qq\",\n    \"unicode\": \"\\ue680\"\n  },\n  {\n    \"font_class\": \"redo\",\n    \"unicode\": \"\\ue64a\"\n  },\n  {\n    \"font_class\": \"redo-filled\",\n    \"unicode\": \"\\ue655\"\n  },\n  {\n    \"font_class\": \"refresh\",\n    \"unicode\": \"\\ue657\"\n  },\n  {\n    \"font_class\": \"refresh-filled\",\n    \"unicode\": \"\\ue656\"\n  },\n  {\n    \"font_class\": \"refreshempty\",\n    \"unicode\": \"\\ue6bf\"\n  },\n  {\n    \"font_class\": \"reload\",\n    \"unicode\": \"\\ue6b2\"\n  },\n  {\n    \"font_class\": \"right\",\n    \"unicode\": \"\\ue6b5\"\n  },\n  {\n    \"font_class\": \"scan\",\n    \"unicode\": \"\\ue62a\"\n  },\n  {\n    \"font_class\": \"search\",\n    \"unicode\": \"\\ue654\"\n  },\n  {\n    \"font_class\": \"settings\",\n    \"unicode\": \"\\ue653\"\n  },\n  {\n    \"font_class\": \"settings-filled\",\n    \"unicode\": \"\\ue6ce\"\n  },\n  {\n    \"font_class\": \"shop\",\n    \"unicode\": \"\\ue62f\"\n  },\n  {\n    \"font_class\": \"shop-filled\",\n    \"unicode\": \"\\ue6cd\"\n  },\n  {\n    \"font_class\": \"smallcircle\",\n    \"unicode\": \"\\ue67c\"\n  },\n  {\n    \"font_class\": \"smallcircle-filled\",\n    \"unicode\": \"\\ue665\"\n  },\n  {\n    \"font_class\": \"sound\",\n    \"unicode\": \"\\ue684\"\n  },\n  {\n    \"font_class\": \"sound-filled\",\n    \"unicode\": \"\\ue686\"\n  },\n  {\n    \"font_class\": \"spinner-cycle\",\n    \"unicode\": \"\\ue68a\"\n  },\n  {\n    \"font_class\": \"staff\",\n    \"unicode\": \"\\ue6a7\"\n  },\n  {\n    \"font_class\": \"staff-filled\",\n    \"unicode\": \"\\ue6cb\"\n  },\n  {\n    \"font_class\": \"star\",\n    \"unicode\": \"\\ue688\"\n  },\n  {\n    \"font_class\": \"star-filled\",\n    \"unicode\": \"\\ue68f\"\n  },\n  {\n    \"font_class\": \"starhalf\",\n    \"unicode\": \"\\ue683\"\n  },\n  {\n    \"font_class\": \"trash\",\n    \"unicode\": \"\\ue687\"\n  },\n  {\n    \"font_class\": \"trash-filled\",\n    \"unicode\": \"\\ue685\"\n  },\n  {\n    \"font_class\": \"tune\",\n    \"unicode\": \"\\ue6aa\"\n  },\n  {\n    \"font_class\": \"tune-filled\",\n    \"unicode\": \"\\ue6ca\"\n  },\n  {\n    \"font_class\": \"undo\",\n    \"unicode\": \"\\ue64f\"\n  },\n  {\n    \"font_class\": \"undo-filled\",\n    \"unicode\": \"\\ue64c\"\n  },\n  {\n    \"font_class\": \"up\",\n    \"unicode\": \"\\ue6b6\"\n  },\n\t{\n\t  \"font_class\": \"top\",\n\t  \"unicode\": \"\\ue6b6\"\n\t},\n  {\n    \"font_class\": \"upload\",\n    \"unicode\": \"\\ue690\"\n  },\n  {\n    \"font_class\": \"upload-filled\",\n    \"unicode\": \"\\ue68e\"\n  },\n  {\n    \"font_class\": \"videocam\",\n    \"unicode\": \"\\ue68c\"\n  },\n  {\n    \"font_class\": \"videocam-filled\",\n    \"unicode\": \"\\ue689\"\n  },\n  {\n    \"font_class\": \"vip\",\n    \"unicode\": \"\\ue6a8\"\n  },\n  {\n    \"font_class\": \"vip-filled\",\n    \"unicode\": \"\\ue6c6\"\n  },\n  {\n    \"font_class\": \"wallet\",\n    \"unicode\": \"\\ue6b1\"\n  },\n  {\n    \"font_class\": \"wallet-filled\",\n    \"unicode\": \"\\ue6c2\"\n  },\n  {\n    \"font_class\": \"weibo\",\n    \"unicode\": \"\\ue68b\"\n  },\n  {\n    \"font_class\": \"weixin\",\n    \"unicode\": \"\\ue691\"\n  }\n]\r\n\r\n// export const fontData = JSON.parse<IconsDataItem>(fontDataJson)\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "/*!\n * Vue.js v2.6.11\n * (c) 2014-2021 Evan You\n * Released under the MIT License.\n */\n/*  */\n\nvar emptyObject = Object.freeze({});\n\n// These helpers produce better VM code in JS engines due to their\n// explicitness and function inlining.\nfunction isUndef (v) {\n  return v === undefined || v === null\n}\n\nfunction isDef (v) {\n  return v !== undefined && v !== null\n}\n\nfunction isTrue (v) {\n  return v === true\n}\n\nfunction isFalse (v) {\n  return v === false\n}\n\n/**\n * Check if value is primitive.\n */\nfunction isPrimitive (value) {\n  return (\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    // $flow-disable-line\n    typeof value === 'symbol' ||\n    typeof value === 'boolean'\n  )\n}\n\n/**\n * Quick object check - this is primarily used to tell\n * Objects from primitive values when we know the value\n * is a JSON-compliant type.\n */\nfunction isObject (obj) {\n  return obj !== null && typeof obj === 'object'\n}\n\n/**\n * Get the raw type string of a value, e.g., [object Object].\n */\nvar _toString = Object.prototype.toString;\n\nfunction toRawType (value) {\n  return _toString.call(value).slice(8, -1)\n}\n\n/**\n * Strict object type check. Only returns true\n * for plain JavaScript objects.\n */\nfunction isPlainObject (obj) {\n  return _toString.call(obj) === '[object Object]'\n}\n\nfunction isRegExp (v) {\n  return _toString.call(v) === '[object RegExp]'\n}\n\n/**\n * Check if val is a valid array index.\n */\nfunction isValidArrayIndex (val) {\n  var n = parseFloat(String(val));\n  return n >= 0 && Math.floor(n) === n && isFinite(val)\n}\n\nfunction isPromise (val) {\n  return (\n    isDef(val) &&\n    typeof val.then === 'function' &&\n    typeof val.catch === 'function'\n  )\n}\n\n/**\n * Convert a value to a string that is actually rendered.\n */\nfunction toString (val) {\n  return val == null\n    ? ''\n    : Array.isArray(val) || (isPlainObject(val) && val.toString === _toString)\n      ? JSON.stringify(val, null, 2)\n      : String(val)\n}\n\n/**\n * Convert an input value to a number for persistence.\n * If the conversion fails, return original string.\n */\nfunction toNumber (val) {\n  var n = parseFloat(val);\n  return isNaN(n) ? val : n\n}\n\n/**\n * Make a map and return a function for checking if a key\n * is in that map.\n */\nfunction makeMap (\n  str,\n  expectsLowerCase\n) {\n  var map = Object.create(null);\n  var list = str.split(',');\n  for (var i = 0; i < list.length; i++) {\n    map[list[i]] = true;\n  }\n  return expectsLowerCase\n    ? function (val) { return map[val.toLowerCase()]; }\n    : function (val) { return map[val]; }\n}\n\n/**\n * Check if a tag is a built-in tag.\n */\nvar isBuiltInTag = makeMap('slot,component', true);\n\n/**\n * Check if an attribute is a reserved attribute.\n */\nvar isReservedAttribute = makeMap('key,ref,slot,slot-scope,is');\n\n/**\n * Remove an item from an array.\n */\nfunction remove (arr, item) {\n  if (arr.length) {\n    var index = arr.indexOf(item);\n    if (index > -1) {\n      return arr.splice(index, 1)\n    }\n  }\n}\n\n/**\n * Check whether an object has the property.\n */\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwn (obj, key) {\n  return hasOwnProperty.call(obj, key)\n}\n\n/**\n * Create a cached version of a pure function.\n */\nfunction cached (fn) {\n  var cache = Object.create(null);\n  return (function cachedFn (str) {\n    var hit = cache[str];\n    return hit || (cache[str] = fn(str))\n  })\n}\n\n/**\n * Camelize a hyphen-delimited string.\n */\nvar camelizeRE = /-(\\w)/g;\nvar camelize = cached(function (str) {\n  return str.replace(camelizeRE, function (_, c) { return c ? c.toUpperCase() : ''; })\n});\n\n/**\n * Capitalize a string.\n */\nvar capitalize = cached(function (str) {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n});\n\n/**\n * Hyphenate a camelCase string.\n */\nvar hyphenateRE = /\\B([A-Z])/g;\nvar hyphenate = cached(function (str) {\n  return str.replace(hyphenateRE, '-$1').toLowerCase()\n});\n\n/**\n * Simple bind polyfill for environments that do not support it,\n * e.g., PhantomJS 1.x. Technically, we don't need this anymore\n * since native bind is now performant enough in most browsers.\n * But removing it would mean breaking code that was able to run in\n * PhantomJS 1.x, so this must be kept for backward compatibility.\n */\n\n/* istanbul ignore next */\nfunction polyfillBind (fn, ctx) {\n  function boundFn (a) {\n    var l = arguments.length;\n    return l\n      ? l > 1\n        ? fn.apply(ctx, arguments)\n        : fn.call(ctx, a)\n      : fn.call(ctx)\n  }\n\n  boundFn._length = fn.length;\n  return boundFn\n}\n\nfunction nativeBind (fn, ctx) {\n  return fn.bind(ctx)\n}\n\nvar bind = Function.prototype.bind\n  ? nativeBind\n  : polyfillBind;\n\n/**\n * Convert an Array-like object to a real Array.\n */\nfunction toArray (list, start) {\n  start = start || 0;\n  var i = list.length - start;\n  var ret = new Array(i);\n  while (i--) {\n    ret[i] = list[i + start];\n  }\n  return ret\n}\n\n/**\n * Mix properties into target object.\n */\nfunction extend (to, _from) {\n  for (var key in _from) {\n    to[key] = _from[key];\n  }\n  return to\n}\n\n/**\n * Merge an Array of Objects into a single Object.\n */\nfunction toObject (arr) {\n  var res = {};\n  for (var i = 0; i < arr.length; i++) {\n    if (arr[i]) {\n      extend(res, arr[i]);\n    }\n  }\n  return res\n}\n\n/* eslint-disable no-unused-vars */\n\n/**\n * Perform no operation.\n * Stubbing args to make Flow happy without leaving useless transpiled code\n * with ...rest (https://flow.org/blog/2017/05/07/Strict-Function-Call-Arity/).\n */\nfunction noop (a, b, c) {}\n\n/**\n * Always return false.\n */\nvar no = function (a, b, c) { return false; };\n\n/* eslint-enable no-unused-vars */\n\n/**\n * Return the same value.\n */\nvar identity = function (_) { return _; };\n\n/**\n * Check if two values are loosely equal - that is,\n * if they are plain objects, do they have the same shape?\n */\nfunction looseEqual (a, b) {\n  if (a === b) { return true }\n  var isObjectA = isObject(a);\n  var isObjectB = isObject(b);\n  if (isObjectA && isObjectB) {\n    try {\n      var isArrayA = Array.isArray(a);\n      var isArrayB = Array.isArray(b);\n      if (isArrayA && isArrayB) {\n        return a.length === b.length && a.every(function (e, i) {\n          return looseEqual(e, b[i])\n        })\n      } else if (a instanceof Date && b instanceof Date) {\n        return a.getTime() === b.getTime()\n      } else if (!isArrayA && !isArrayB) {\n        var keysA = Object.keys(a);\n        var keysB = Object.keys(b);\n        return keysA.length === keysB.length && keysA.every(function (key) {\n          return looseEqual(a[key], b[key])\n        })\n      } else {\n        /* istanbul ignore next */\n        return false\n      }\n    } catch (e) {\n      /* istanbul ignore next */\n      return false\n    }\n  } else if (!isObjectA && !isObjectB) {\n    return String(a) === String(b)\n  } else {\n    return false\n  }\n}\n\n/**\n * Return the first index at which a loosely equal value can be\n * found in the array (if value is a plain object, the array must\n * contain an object of the same shape), or -1 if it is not present.\n */\nfunction looseIndexOf (arr, val) {\n  for (var i = 0; i < arr.length; i++) {\n    if (looseEqual(arr[i], val)) { return i }\n  }\n  return -1\n}\n\n/**\n * Ensure a function is called only once.\n */\nfunction once (fn) {\n  var called = false;\n  return function () {\n    if (!called) {\n      called = true;\n      fn.apply(this, arguments);\n    }\n  }\n}\n\nvar ASSET_TYPES = [\n  'component',\n  'directive',\n  'filter'\n];\n\nvar LIFECYCLE_HOOKS = [\n  'beforeCreate',\n  'created',\n  'beforeMount',\n  'mounted',\n  'beforeUpdate',\n  'updated',\n  'beforeDestroy',\n  'destroyed',\n  'activated',\n  'deactivated',\n  'errorCaptured',\n  'serverPrefetch'\n];\n\n/*  */\n\n\n\nvar config = ({\n  /**\n   * Option merge strategies (used in core/util/options)\n   */\n  // $flow-disable-line\n  optionMergeStrategies: Object.create(null),\n\n  /**\n   * Whether to suppress warnings.\n   */\n  silent: false,\n\n  /**\n   * Show production mode tip message on boot?\n   */\n  productionTip: process.env.NODE_ENV !== 'production',\n\n  /**\n   * Whether to enable devtools\n   */\n  devtools: process.env.NODE_ENV !== 'production',\n\n  /**\n   * Whether to record perf\n   */\n  performance: false,\n\n  /**\n   * Error handler for watcher errors\n   */\n  errorHandler: null,\n\n  /**\n   * Warn handler for watcher warns\n   */\n  warnHandler: null,\n\n  /**\n   * Ignore certain custom elements\n   */\n  ignoredElements: [],\n\n  /**\n   * Custom user key aliases for v-on\n   */\n  // $flow-disable-line\n  keyCodes: Object.create(null),\n\n  /**\n   * Check if a tag is reserved so that it cannot be registered as a\n   * component. This is platform-dependent and may be overwritten.\n   */\n  isReservedTag: no,\n\n  /**\n   * Check if an attribute is reserved so that it cannot be used as a component\n   * prop. This is platform-dependent and may be overwritten.\n   */\n  isReservedAttr: no,\n\n  /**\n   * Check if a tag is an unknown element.\n   * Platform-dependent.\n   */\n  isUnknownElement: no,\n\n  /**\n   * Get the namespace of an element\n   */\n  getTagNamespace: noop,\n\n  /**\n   * Parse the real tag name for the specific platform.\n   */\n  parsePlatformTagName: identity,\n\n  /**\n   * Check if an attribute must be bound using property, e.g. value\n   * Platform-dependent.\n   */\n  mustUseProp: no,\n\n  /**\n   * Perform updates asynchronously. Intended to be used by Vue Test Utils\n   * This will significantly reduce performance if set to false.\n   */\n  async: true,\n\n  /**\n   * Exposed for legacy reasons\n   */\n  _lifecycleHooks: LIFECYCLE_HOOKS\n});\n\n/*  */\n\n/**\n * unicode letters used for parsing html tags, component names and property paths.\n * using https://www.w3.org/TR/html53/semantics-scripting.html#potentialcustomelementname\n * skipping \\u10000-\\uEFFFF due to it freezing up PhantomJS\n */\nvar unicodeRegExp = /a-zA-Z\\u00B7\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u203F-\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD/;\n\n/**\n * Check if a string starts with $ or _\n */\nfunction isReserved (str) {\n  var c = (str + '').charCodeAt(0);\n  return c === 0x24 || c === 0x5F\n}\n\n/**\n * Define a property.\n */\nfunction def (obj, key, val, enumerable) {\n  Object.defineProperty(obj, key, {\n    value: val,\n    enumerable: !!enumerable,\n    writable: true,\n    configurable: true\n  });\n}\n\n/**\n * Parse simple path.\n */\nvar bailRE = new RegExp((\"[^\" + (unicodeRegExp.source) + \".$_\\\\d]\"));\nfunction parsePath (path) {\n  if (bailRE.test(path)) {\n    return\n  }\n  var segments = path.split('.');\n  return function (obj) {\n    for (var i = 0; i < segments.length; i++) {\n      if (!obj) { return }\n      obj = obj[segments[i]];\n    }\n    return obj\n  }\n}\n\n/*  */\n\n// can we use __proto__?\nvar hasProto = '__proto__' in {};\n\n// Browser environment sniffing\nvar inBrowser = typeof window !== 'undefined';\nvar inWeex = typeof WXEnvironment !== 'undefined' && !!WXEnvironment.platform;\nvar weexPlatform = inWeex && WXEnvironment.platform.toLowerCase();\nvar UA = inBrowser && window.navigator.userAgent.toLowerCase();\nvar isIE = UA && /msie|trident/.test(UA);\nvar isIE9 = UA && UA.indexOf('msie 9.0') > 0;\nvar isEdge = UA && UA.indexOf('edge/') > 0;\nvar isAndroid = (UA && UA.indexOf('android') > 0) || (weexPlatform === 'android');\nvar isIOS = (UA && /iphone|ipad|ipod|ios/.test(UA)) || (weexPlatform === 'ios');\nvar isChrome = UA && /chrome\\/\\d+/.test(UA) && !isEdge;\nvar isPhantomJS = UA && /phantomjs/.test(UA);\nvar isFF = UA && UA.match(/firefox\\/(\\d+)/);\n\n// Firefox has a \"watch\" function on Object.prototype...\nvar nativeWatch = ({}).watch;\nif (inBrowser) {\n  try {\n    var opts = {};\n    Object.defineProperty(opts, 'passive', ({\n      get: function get () {\n      }\n    })); // https://github.com/facebook/flow/issues/285\n    window.addEventListener('test-passive', null, opts);\n  } catch (e) {}\n}\n\n// this needs to be lazy-evaled because vue may be required before\n// vue-server-renderer can set VUE_ENV\nvar _isServer;\nvar isServerRendering = function () {\n  if (_isServer === undefined) {\n    /* istanbul ignore if */\n    if (!inBrowser && !inWeex && typeof global !== 'undefined') {\n      // detect presence of vue-server-renderer and avoid\n      // Webpack shimming the process\n      _isServer = global['process'] && global['process'].env.VUE_ENV === 'server';\n    } else {\n      _isServer = false;\n    }\n  }\n  return _isServer\n};\n\n// detect devtools\nvar devtools = inBrowser && window.__VUE_DEVTOOLS_GLOBAL_HOOK__;\n\n/* istanbul ignore next */\nfunction isNative (Ctor) {\n  return typeof Ctor === 'function' && /native code/.test(Ctor.toString())\n}\n\nvar hasSymbol =\n  typeof Symbol !== 'undefined' && isNative(Symbol) &&\n  typeof Reflect !== 'undefined' && isNative(Reflect.ownKeys);\n\nvar _Set;\n/* istanbul ignore if */ // $flow-disable-line\nif (typeof Set !== 'undefined' && isNative(Set)) {\n  // use native Set when available.\n  _Set = Set;\n} else {\n  // a non-standard Set polyfill that only works with primitive keys.\n  _Set = /*@__PURE__*/(function () {\n    function Set () {\n      this.set = Object.create(null);\n    }\n    Set.prototype.has = function has (key) {\n      return this.set[key] === true\n    };\n    Set.prototype.add = function add (key) {\n      this.set[key] = true;\n    };\n    Set.prototype.clear = function clear () {\n      this.set = Object.create(null);\n    };\n\n    return Set;\n  }());\n}\n\n/*  */\n\nvar warn = noop;\nvar tip = noop;\nvar generateComponentTrace = (noop); // work around flow check\nvar formatComponentName = (noop);\n\nif (process.env.NODE_ENV !== 'production') {\n  var hasConsole = typeof console !== 'undefined';\n  var classifyRE = /(?:^|[-_])(\\w)/g;\n  var classify = function (str) { return str\n    .replace(classifyRE, function (c) { return c.toUpperCase(); })\n    .replace(/[-_]/g, ''); };\n\n  warn = function (msg, vm) {\n    var trace = vm ? generateComponentTrace(vm) : '';\n\n    if (config.warnHandler) {\n      config.warnHandler.call(null, msg, vm, trace);\n    } else if (hasConsole && (!config.silent)) {\n      console.error((\"[Vue warn]: \" + msg + trace));\n    }\n  };\n\n  tip = function (msg, vm) {\n    if (hasConsole && (!config.silent)) {\n      console.warn(\"[Vue tip]: \" + msg + (\n        vm ? generateComponentTrace(vm) : ''\n      ));\n    }\n  };\n\n  formatComponentName = function (vm, includeFile) {\n    if (vm.$root === vm) {\n      if (vm.$options && vm.$options.__file) { // fixed by xxxxxx\n        return ('') + vm.$options.__file\n      }\n      return '<Root>'\n    }\n    var options = typeof vm === 'function' && vm.cid != null\n      ? vm.options\n      : vm._isVue\n        ? vm.$options || vm.constructor.options\n        : vm;\n    var name = options.name || options._componentTag;\n    var file = options.__file;\n    if (!name && file) {\n      var match = file.match(/([^/\\\\]+)\\.vue$/);\n      name = match && match[1];\n    }\n\n    return (\n      (name ? (\"<\" + (classify(name)) + \">\") : \"<Anonymous>\") +\n      (file && includeFile !== false ? (\" at \" + file) : '')\n    )\n  };\n\n  var repeat = function (str, n) {\n    var res = '';\n    while (n) {\n      if (n % 2 === 1) { res += str; }\n      if (n > 1) { str += str; }\n      n >>= 1;\n    }\n    return res\n  };\n\n  generateComponentTrace = function (vm) {\n    if (vm._isVue && vm.$parent) {\n      var tree = [];\n      var currentRecursiveSequence = 0;\n      while (vm && vm.$options.name !== 'PageBody') {\n        if (tree.length > 0) {\n          var last = tree[tree.length - 1];\n          if (last.constructor === vm.constructor) {\n            currentRecursiveSequence++;\n            vm = vm.$parent;\n            continue\n          } else if (currentRecursiveSequence > 0) {\n            tree[tree.length - 1] = [last, currentRecursiveSequence];\n            currentRecursiveSequence = 0;\n          }\n        }\n        !vm.$options.isReserved && tree.push(vm);\n        vm = vm.$parent;\n      }\n      return '\\n\\nfound in\\n\\n' + tree\n        .map(function (vm, i) { return (\"\" + (i === 0 ? '---> ' : repeat(' ', 5 + i * 2)) + (Array.isArray(vm)\n            ? ((formatComponentName(vm[0])) + \"... (\" + (vm[1]) + \" recursive calls)\")\n            : formatComponentName(vm))); })\n        .join('\\n')\n    } else {\n      return (\"\\n\\n(found in \" + (formatComponentName(vm)) + \")\")\n    }\n  };\n}\n\n/*  */\n\nvar uid = 0;\n\n/**\n * A dep is an observable that can have multiple\n * directives subscribing to it.\n */\nvar Dep = function Dep () {\n  this.id = uid++;\n  this.subs = [];\n};\n\nDep.prototype.addSub = function addSub (sub) {\n  this.subs.push(sub);\n};\n\nDep.prototype.removeSub = function removeSub (sub) {\n  remove(this.subs, sub);\n};\n\nDep.prototype.depend = function depend () {\n  if (Dep.SharedObject.target) {\n    Dep.SharedObject.target.addDep(this);\n  }\n};\n\nDep.prototype.notify = function notify () {\n  // stabilize the subscriber list first\n  var subs = this.subs.slice();\n  if (process.env.NODE_ENV !== 'production' && !config.async) {\n    // subs aren't sorted in scheduler if not running async\n    // we need to sort them now to make sure they fire in correct\n    // order\n    subs.sort(function (a, b) { return a.id - b.id; });\n  }\n  for (var i = 0, l = subs.length; i < l; i++) {\n    subs[i].update();\n  }\n};\n\n// The current target watcher being evaluated.\n// This is globally unique because only one watcher\n// can be evaluated at a time.\n// fixed by xxxxxx (nvue shared vuex)\n/* eslint-disable no-undef */\nDep.SharedObject = {};\nDep.SharedObject.target = null;\nDep.SharedObject.targetStack = [];\n\nfunction pushTarget (target) {\n  Dep.SharedObject.targetStack.push(target);\n  Dep.SharedObject.target = target;\n  Dep.target = target;\n}\n\nfunction popTarget () {\n  Dep.SharedObject.targetStack.pop();\n  Dep.SharedObject.target = Dep.SharedObject.targetStack[Dep.SharedObject.targetStack.length - 1];\n  Dep.target = Dep.SharedObject.target;\n}\n\n/*  */\n\nvar VNode = function VNode (\n  tag,\n  data,\n  children,\n  text,\n  elm,\n  context,\n  componentOptions,\n  asyncFactory\n) {\n  this.tag = tag;\n  this.data = data;\n  this.children = children;\n  this.text = text;\n  this.elm = elm;\n  this.ns = undefined;\n  this.context = context;\n  this.fnContext = undefined;\n  this.fnOptions = undefined;\n  this.fnScopeId = undefined;\n  this.key = data && data.key;\n  this.componentOptions = componentOptions;\n  this.componentInstance = undefined;\n  this.parent = undefined;\n  this.raw = false;\n  this.isStatic = false;\n  this.isRootInsert = true;\n  this.isComment = false;\n  this.isCloned = false;\n  this.isOnce = false;\n  this.asyncFactory = asyncFactory;\n  this.asyncMeta = undefined;\n  this.isAsyncPlaceholder = false;\n};\n\nvar prototypeAccessors = { child: { configurable: true } };\n\n// DEPRECATED: alias for componentInstance for backwards compat.\n/* istanbul ignore next */\nprototypeAccessors.child.get = function () {\n  return this.componentInstance\n};\n\nObject.defineProperties( VNode.prototype, prototypeAccessors );\n\nvar createEmptyVNode = function (text) {\n  if ( text === void 0 ) text = '';\n\n  var node = new VNode();\n  node.text = text;\n  node.isComment = true;\n  return node\n};\n\nfunction createTextVNode (val) {\n  return new VNode(undefined, undefined, undefined, String(val))\n}\n\n// optimized shallow clone\n// used for static nodes and slot nodes because they may be reused across\n// multiple renders, cloning them avoids errors when DOM manipulations rely\n// on their elm reference.\nfunction cloneVNode (vnode) {\n  var cloned = new VNode(\n    vnode.tag,\n    vnode.data,\n    // #7975\n    // clone children array to avoid mutating original in case of cloning\n    // a child.\n    vnode.children && vnode.children.slice(),\n    vnode.text,\n    vnode.elm,\n    vnode.context,\n    vnode.componentOptions,\n    vnode.asyncFactory\n  );\n  cloned.ns = vnode.ns;\n  cloned.isStatic = vnode.isStatic;\n  cloned.key = vnode.key;\n  cloned.isComment = vnode.isComment;\n  cloned.fnContext = vnode.fnContext;\n  cloned.fnOptions = vnode.fnOptions;\n  cloned.fnScopeId = vnode.fnScopeId;\n  cloned.asyncMeta = vnode.asyncMeta;\n  cloned.isCloned = true;\n  return cloned\n}\n\n/*\n * not type checking this file because flow doesn't play well with\n * dynamically accessing methods on Array prototype\n */\n\nvar arrayProto = Array.prototype;\nvar arrayMethods = Object.create(arrayProto);\n\nvar methodsToPatch = [\n  'push',\n  'pop',\n  'shift',\n  'unshift',\n  'splice',\n  'sort',\n  'reverse'\n];\n\n/**\n * Intercept mutating methods and emit events\n */\nmethodsToPatch.forEach(function (method) {\n  // cache original method\n  var original = arrayProto[method];\n  def(arrayMethods, method, function mutator () {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var result = original.apply(this, args);\n    var ob = this.__ob__;\n    var inserted;\n    switch (method) {\n      case 'push':\n      case 'unshift':\n        inserted = args;\n        break\n      case 'splice':\n        inserted = args.slice(2);\n        break\n    }\n    if (inserted) { ob.observeArray(inserted); }\n    // notify change\n    ob.dep.notify();\n    return result\n  });\n});\n\n/*  */\n\nvar arrayKeys = Object.getOwnPropertyNames(arrayMethods);\n\n/**\n * In some cases we may want to disable observation inside a component's\n * update computation.\n */\nvar shouldObserve = true;\n\nfunction toggleObserving (value) {\n  shouldObserve = value;\n}\n\n/**\n * Observer class that is attached to each observed\n * object. Once attached, the observer converts the target\n * object's property keys into getter/setters that\n * collect dependencies and dispatch updates.\n */\nvar Observer = function Observer (value) {\n  this.value = value;\n  this.dep = new Dep();\n  this.vmCount = 0;\n  def(value, '__ob__', this);\n  if (Array.isArray(value)) {\n    if (hasProto) {\n      {// fixed by xxxxxx 微信小程序使用 plugins 之后，数组方法被直接挂载到了数组对象上，需要执行 copyAugment 逻辑\n        if(value.push !== value.__proto__.push){\n          copyAugment(value, arrayMethods, arrayKeys);\n        } else {\n          protoAugment(value, arrayMethods);\n        }\n      }\n    } else {\n      copyAugment(value, arrayMethods, arrayKeys);\n    }\n    this.observeArray(value);\n  } else {\n    this.walk(value);\n  }\n};\n\n/**\n * Walk through all properties and convert them into\n * getter/setters. This method should only be called when\n * value type is Object.\n */\nObserver.prototype.walk = function walk (obj) {\n  var keys = Object.keys(obj);\n  for (var i = 0; i < keys.length; i++) {\n    defineReactive$$1(obj, keys[i]);\n  }\n};\n\n/**\n * Observe a list of Array items.\n */\nObserver.prototype.observeArray = function observeArray (items) {\n  for (var i = 0, l = items.length; i < l; i++) {\n    observe(items[i]);\n  }\n};\n\n// helpers\n\n/**\n * Augment a target Object or Array by intercepting\n * the prototype chain using __proto__\n */\nfunction protoAugment (target, src) {\n  /* eslint-disable no-proto */\n  target.__proto__ = src;\n  /* eslint-enable no-proto */\n}\n\n/**\n * Augment a target Object or Array by defining\n * hidden properties.\n */\n/* istanbul ignore next */\nfunction copyAugment (target, src, keys) {\n  for (var i = 0, l = keys.length; i < l; i++) {\n    var key = keys[i];\n    def(target, key, src[key]);\n  }\n}\n\n/**\n * Attempt to create an observer instance for a value,\n * returns the new observer if successfully observed,\n * or the existing observer if the value already has one.\n */\nfunction observe (value, asRootData) {\n  if (!isObject(value) || value instanceof VNode) {\n    return\n  }\n  var ob;\n  if (hasOwn(value, '__ob__') && value.__ob__ instanceof Observer) {\n    ob = value.__ob__;\n  } else if (\n    shouldObserve &&\n    !isServerRendering() &&\n    (Array.isArray(value) || isPlainObject(value)) &&\n    Object.isExtensible(value) &&\n    !value._isVue\n  ) {\n    ob = new Observer(value);\n  }\n  if (asRootData && ob) {\n    ob.vmCount++;\n  }\n  return ob\n}\n\n/**\n * Define a reactive property on an Object.\n */\nfunction defineReactive$$1 (\n  obj,\n  key,\n  val,\n  customSetter,\n  shallow\n) {\n  var dep = new Dep();\n\n  var property = Object.getOwnPropertyDescriptor(obj, key);\n  if (property && property.configurable === false) {\n    return\n  }\n\n  // cater for pre-defined getter/setters\n  var getter = property && property.get;\n  var setter = property && property.set;\n  if ((!getter || setter) && arguments.length === 2) {\n    val = obj[key];\n  }\n\n  var childOb = !shallow && observe(val);\n  Object.defineProperty(obj, key, {\n    enumerable: true,\n    configurable: true,\n    get: function reactiveGetter () {\n      var value = getter ? getter.call(obj) : val;\n      if (Dep.SharedObject.target) { // fixed by xxxxxx\n        dep.depend();\n        if (childOb) {\n          childOb.dep.depend();\n          if (Array.isArray(value)) {\n            dependArray(value);\n          }\n        }\n      }\n      return value\n    },\n    set: function reactiveSetter (newVal) {\n      var value = getter ? getter.call(obj) : val;\n      /* eslint-disable no-self-compare */\n      if (newVal === value || (newVal !== newVal && value !== value)) {\n        return\n      }\n      /* eslint-enable no-self-compare */\n      if (process.env.NODE_ENV !== 'production' && customSetter) {\n        customSetter();\n      }\n      // #7981: for accessor properties without setter\n      if (getter && !setter) { return }\n      if (setter) {\n        setter.call(obj, newVal);\n      } else {\n        val = newVal;\n      }\n      childOb = !shallow && observe(newVal);\n      dep.notify();\n    }\n  });\n}\n\n/**\n * Set a property on an object. Adds the new property and\n * triggers change notification if the property doesn't\n * already exist.\n */\nfunction set (target, key, val) {\n  if (process.env.NODE_ENV !== 'production' &&\n    (isUndef(target) || isPrimitive(target))\n  ) {\n    warn((\"Cannot set reactive property on undefined, null, or primitive value: \" + ((target))));\n  }\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.length = Math.max(target.length, key);\n    target.splice(key, 1, val);\n    return val\n  }\n  if (key in target && !(key in Object.prototype)) {\n    target[key] = val;\n    return val\n  }\n  var ob = (target).__ob__;\n  if (target._isVue || (ob && ob.vmCount)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      'Avoid adding reactive properties to a Vue instance or its root $data ' +\n      'at runtime - declare it upfront in the data option.'\n    );\n    return val\n  }\n  if (!ob) {\n    target[key] = val;\n    return val\n  }\n  defineReactive$$1(ob.value, key, val);\n  ob.dep.notify();\n  return val\n}\n\n/**\n * Delete a property and trigger change if necessary.\n */\nfunction del (target, key) {\n  if (process.env.NODE_ENV !== 'production' &&\n    (isUndef(target) || isPrimitive(target))\n  ) {\n    warn((\"Cannot delete reactive property on undefined, null, or primitive value: \" + ((target))));\n  }\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.splice(key, 1);\n    return\n  }\n  var ob = (target).__ob__;\n  if (target._isVue || (ob && ob.vmCount)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      'Avoid deleting properties on a Vue instance or its root $data ' +\n      '- just set it to null.'\n    );\n    return\n  }\n  if (!hasOwn(target, key)) {\n    return\n  }\n  delete target[key];\n  if (!ob) {\n    return\n  }\n  ob.dep.notify();\n}\n\n/**\n * Collect dependencies on array elements when the array is touched, since\n * we cannot intercept array element access like property getters.\n */\nfunction dependArray (value) {\n  for (var e = (void 0), i = 0, l = value.length; i < l; i++) {\n    e = value[i];\n    e && e.__ob__ && e.__ob__.dep.depend();\n    if (Array.isArray(e)) {\n      dependArray(e);\n    }\n  }\n}\n\n/*  */\n\n/**\n * Option overwriting strategies are functions that handle\n * how to merge a parent option value and a child option\n * value into the final value.\n */\nvar strats = config.optionMergeStrategies;\n\n/**\n * Options with restrictions\n */\nif (process.env.NODE_ENV !== 'production') {\n  strats.el = strats.propsData = function (parent, child, vm, key) {\n    if (!vm) {\n      warn(\n        \"option \\\"\" + key + \"\\\" can only be used during instance \" +\n        'creation with the `new` keyword.'\n      );\n    }\n    return defaultStrat(parent, child)\n  };\n}\n\n/**\n * Helper that recursively merges two data objects together.\n */\nfunction mergeData (to, from) {\n  if (!from) { return to }\n  var key, toVal, fromVal;\n\n  var keys = hasSymbol\n    ? Reflect.ownKeys(from)\n    : Object.keys(from);\n\n  for (var i = 0; i < keys.length; i++) {\n    key = keys[i];\n    // in case the object is already observed...\n    if (key === '__ob__') { continue }\n    toVal = to[key];\n    fromVal = from[key];\n    if (!hasOwn(to, key)) {\n      set(to, key, fromVal);\n    } else if (\n      toVal !== fromVal &&\n      isPlainObject(toVal) &&\n      isPlainObject(fromVal)\n    ) {\n      mergeData(toVal, fromVal);\n    }\n  }\n  return to\n}\n\n/**\n * Data\n */\nfunction mergeDataOrFn (\n  parentVal,\n  childVal,\n  vm\n) {\n  if (!vm) {\n    // in a Vue.extend merge, both should be functions\n    if (!childVal) {\n      return parentVal\n    }\n    if (!parentVal) {\n      return childVal\n    }\n    // when parentVal & childVal are both present,\n    // we need to return a function that returns the\n    // merged result of both functions... no need to\n    // check if parentVal is a function here because\n    // it has to be a function to pass previous merges.\n    return function mergedDataFn () {\n      return mergeData(\n        typeof childVal === 'function' ? childVal.call(this, this) : childVal,\n        typeof parentVal === 'function' ? parentVal.call(this, this) : parentVal\n      )\n    }\n  } else {\n    return function mergedInstanceDataFn () {\n      // instance merge\n      var instanceData = typeof childVal === 'function'\n        ? childVal.call(vm, vm)\n        : childVal;\n      var defaultData = typeof parentVal === 'function'\n        ? parentVal.call(vm, vm)\n        : parentVal;\n      if (instanceData) {\n        return mergeData(instanceData, defaultData)\n      } else {\n        return defaultData\n      }\n    }\n  }\n}\n\nstrats.data = function (\n  parentVal,\n  childVal,\n  vm\n) {\n  if (!vm) {\n    if (childVal && typeof childVal !== 'function') {\n      process.env.NODE_ENV !== 'production' && warn(\n        'The \"data\" option should be a function ' +\n        'that returns a per-instance value in component ' +\n        'definitions.',\n        vm\n      );\n\n      return parentVal\n    }\n    return mergeDataOrFn(parentVal, childVal)\n  }\n\n  return mergeDataOrFn(parentVal, childVal, vm)\n};\n\n/**\n * Hooks and props are merged as arrays.\n */\nfunction mergeHook (\n  parentVal,\n  childVal\n) {\n  var res = childVal\n    ? parentVal\n      ? parentVal.concat(childVal)\n      : Array.isArray(childVal)\n        ? childVal\n        : [childVal]\n    : parentVal;\n  return res\n    ? dedupeHooks(res)\n    : res\n}\n\nfunction dedupeHooks (hooks) {\n  var res = [];\n  for (var i = 0; i < hooks.length; i++) {\n    if (res.indexOf(hooks[i]) === -1) {\n      res.push(hooks[i]);\n    }\n  }\n  return res\n}\n\nLIFECYCLE_HOOKS.forEach(function (hook) {\n  strats[hook] = mergeHook;\n});\n\n/**\n * Assets\n *\n * When a vm is present (instance creation), we need to do\n * a three-way merge between constructor options, instance\n * options and parent options.\n */\nfunction mergeAssets (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  var res = Object.create(parentVal || null);\n  if (childVal) {\n    process.env.NODE_ENV !== 'production' && assertObjectType(key, childVal, vm);\n    return extend(res, childVal)\n  } else {\n    return res\n  }\n}\n\nASSET_TYPES.forEach(function (type) {\n  strats[type + 's'] = mergeAssets;\n});\n\n/**\n * Watchers.\n *\n * Watchers hashes should not overwrite one\n * another, so we merge them as arrays.\n */\nstrats.watch = function (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  // work around Firefox's Object.prototype.watch...\n  if (parentVal === nativeWatch) { parentVal = undefined; }\n  if (childVal === nativeWatch) { childVal = undefined; }\n  /* istanbul ignore if */\n  if (!childVal) { return Object.create(parentVal || null) }\n  if (process.env.NODE_ENV !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n  if (!parentVal) { return childVal }\n  var ret = {};\n  extend(ret, parentVal);\n  for (var key$1 in childVal) {\n    var parent = ret[key$1];\n    var child = childVal[key$1];\n    if (parent && !Array.isArray(parent)) {\n      parent = [parent];\n    }\n    ret[key$1] = parent\n      ? parent.concat(child)\n      : Array.isArray(child) ? child : [child];\n  }\n  return ret\n};\n\n/**\n * Other object hashes.\n */\nstrats.props =\nstrats.methods =\nstrats.inject =\nstrats.computed = function (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  if (childVal && process.env.NODE_ENV !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n  if (!parentVal) { return childVal }\n  var ret = Object.create(null);\n  extend(ret, parentVal);\n  if (childVal) { extend(ret, childVal); }\n  return ret\n};\nstrats.provide = mergeDataOrFn;\n\n/**\n * Default strategy.\n */\nvar defaultStrat = function (parentVal, childVal) {\n  return childVal === undefined\n    ? parentVal\n    : childVal\n};\n\n/**\n * Validate component names\n */\nfunction checkComponents (options) {\n  for (var key in options.components) {\n    validateComponentName(key);\n  }\n}\n\nfunction validateComponentName (name) {\n  if (!new RegExp((\"^[a-zA-Z][\\\\-\\\\.0-9_\" + (unicodeRegExp.source) + \"]*$\")).test(name)) {\n    warn(\n      'Invalid component name: \"' + name + '\". Component names ' +\n      'should conform to valid custom element name in html5 specification.'\n    );\n  }\n  if (isBuiltInTag(name) || config.isReservedTag(name)) {\n    warn(\n      'Do not use built-in or reserved HTML elements as component ' +\n      'id: ' + name\n    );\n  }\n}\n\n/**\n * Ensure all props option syntax are normalized into the\n * Object-based format.\n */\nfunction normalizeProps (options, vm) {\n  var props = options.props;\n  if (!props) { return }\n  var res = {};\n  var i, val, name;\n  if (Array.isArray(props)) {\n    i = props.length;\n    while (i--) {\n      val = props[i];\n      if (typeof val === 'string') {\n        name = camelize(val);\n        res[name] = { type: null };\n      } else if (process.env.NODE_ENV !== 'production') {\n        warn('props must be strings when using array syntax.');\n      }\n    }\n  } else if (isPlainObject(props)) {\n    for (var key in props) {\n      val = props[key];\n      name = camelize(key);\n      res[name] = isPlainObject(val)\n        ? val\n        : { type: val };\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\n      \"Invalid value for option \\\"props\\\": expected an Array or an Object, \" +\n      \"but got \" + (toRawType(props)) + \".\",\n      vm\n    );\n  }\n  options.props = res;\n}\n\n/**\n * Normalize all injections into Object-based format\n */\nfunction normalizeInject (options, vm) {\n  var inject = options.inject;\n  if (!inject) { return }\n  var normalized = options.inject = {};\n  if (Array.isArray(inject)) {\n    for (var i = 0; i < inject.length; i++) {\n      normalized[inject[i]] = { from: inject[i] };\n    }\n  } else if (isPlainObject(inject)) {\n    for (var key in inject) {\n      var val = inject[key];\n      normalized[key] = isPlainObject(val)\n        ? extend({ from: key }, val)\n        : { from: val };\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\n      \"Invalid value for option \\\"inject\\\": expected an Array or an Object, \" +\n      \"but got \" + (toRawType(inject)) + \".\",\n      vm\n    );\n  }\n}\n\n/**\n * Normalize raw function directives into object format.\n */\nfunction normalizeDirectives (options) {\n  var dirs = options.directives;\n  if (dirs) {\n    for (var key in dirs) {\n      var def$$1 = dirs[key];\n      if (typeof def$$1 === 'function') {\n        dirs[key] = { bind: def$$1, update: def$$1 };\n      }\n    }\n  }\n}\n\nfunction assertObjectType (name, value, vm) {\n  if (!isPlainObject(value)) {\n    warn(\n      \"Invalid value for option \\\"\" + name + \"\\\": expected an Object, \" +\n      \"but got \" + (toRawType(value)) + \".\",\n      vm\n    );\n  }\n}\n\n/**\n * Merge two option objects into a new one.\n * Core utility used in both instantiation and inheritance.\n */\nfunction mergeOptions (\n  parent,\n  child,\n  vm\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    checkComponents(child);\n  }\n\n  if (typeof child === 'function') {\n    child = child.options;\n  }\n\n  normalizeProps(child, vm);\n  normalizeInject(child, vm);\n  normalizeDirectives(child);\n\n  // Apply extends and mixins on the child options,\n  // but only if it is a raw options object that isn't\n  // the result of another mergeOptions call.\n  // Only merged options has the _base property.\n  if (!child._base) {\n    if (child.extends) {\n      parent = mergeOptions(parent, child.extends, vm);\n    }\n    if (child.mixins) {\n      for (var i = 0, l = child.mixins.length; i < l; i++) {\n        parent = mergeOptions(parent, child.mixins[i], vm);\n      }\n    }\n  }\n\n  var options = {};\n  var key;\n  for (key in parent) {\n    mergeField(key);\n  }\n  for (key in child) {\n    if (!hasOwn(parent, key)) {\n      mergeField(key);\n    }\n  }\n  function mergeField (key) {\n    var strat = strats[key] || defaultStrat;\n    options[key] = strat(parent[key], child[key], vm, key);\n  }\n  return options\n}\n\n/**\n * Resolve an asset.\n * This function is used because child instances need access\n * to assets defined in its ancestor chain.\n */\nfunction resolveAsset (\n  options,\n  type,\n  id,\n  warnMissing\n) {\n  /* istanbul ignore if */\n  if (typeof id !== 'string') {\n    return\n  }\n  var assets = options[type];\n  // check local registration variations first\n  if (hasOwn(assets, id)) { return assets[id] }\n  var camelizedId = camelize(id);\n  if (hasOwn(assets, camelizedId)) { return assets[camelizedId] }\n  var PascalCaseId = capitalize(camelizedId);\n  if (hasOwn(assets, PascalCaseId)) { return assets[PascalCaseId] }\n  // fallback to prototype chain\n  var res = assets[id] || assets[camelizedId] || assets[PascalCaseId];\n  if (process.env.NODE_ENV !== 'production' && warnMissing && !res) {\n    warn(\n      'Failed to resolve ' + type.slice(0, -1) + ': ' + id,\n      options\n    );\n  }\n  return res\n}\n\n/*  */\n\n\n\nfunction validateProp (\n  key,\n  propOptions,\n  propsData,\n  vm\n) {\n  var prop = propOptions[key];\n  var absent = !hasOwn(propsData, key);\n  var value = propsData[key];\n  // boolean casting\n  var booleanIndex = getTypeIndex(Boolean, prop.type);\n  if (booleanIndex > -1) {\n    if (absent && !hasOwn(prop, 'default')) {\n      value = false;\n    } else if (value === '' || value === hyphenate(key)) {\n      // only cast empty string / same name to boolean if\n      // boolean has higher priority\n      var stringIndex = getTypeIndex(String, prop.type);\n      if (stringIndex < 0 || booleanIndex < stringIndex) {\n        value = true;\n      }\n    }\n  }\n  // check default value\n  if (value === undefined) {\n    value = getPropDefaultValue(vm, prop, key);\n    // since the default value is a fresh copy,\n    // make sure to observe it.\n    var prevShouldObserve = shouldObserve;\n    toggleObserving(true);\n    observe(value);\n    toggleObserving(prevShouldObserve);\n  }\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    // skip validation for weex recycle-list child component props\n    !(false)\n  ) {\n    assertProp(prop, key, value, vm, absent);\n  }\n  return value\n}\n\n/**\n * Get the default value of a prop.\n */\nfunction getPropDefaultValue (vm, prop, key) {\n  // no default, return undefined\n  if (!hasOwn(prop, 'default')) {\n    return undefined\n  }\n  var def = prop.default;\n  // warn against non-factory defaults for Object & Array\n  if (process.env.NODE_ENV !== 'production' && isObject(def)) {\n    warn(\n      'Invalid default value for prop \"' + key + '\": ' +\n      'Props with type Object/Array must use a factory function ' +\n      'to return the default value.',\n      vm\n    );\n  }\n  // the raw prop value was also undefined from previous render,\n  // return previous default value to avoid unnecessary watcher trigger\n  if (vm && vm.$options.propsData &&\n    vm.$options.propsData[key] === undefined &&\n    vm._props[key] !== undefined\n  ) {\n    return vm._props[key]\n  }\n  // call factory function for non-Function types\n  // a value is Function if its prototype is function even across different execution context\n  return typeof def === 'function' && getType(prop.type) !== 'Function'\n    ? def.call(vm)\n    : def\n}\n\n/**\n * Assert whether a prop is valid.\n */\nfunction assertProp (\n  prop,\n  name,\n  value,\n  vm,\n  absent\n) {\n  if (prop.required && absent) {\n    warn(\n      'Missing required prop: \"' + name + '\"',\n      vm\n    );\n    return\n  }\n  if (value == null && !prop.required) {\n    return\n  }\n  var type = prop.type;\n  var valid = !type || type === true;\n  var expectedTypes = [];\n  if (type) {\n    if (!Array.isArray(type)) {\n      type = [type];\n    }\n    for (var i = 0; i < type.length && !valid; i++) {\n      var assertedType = assertType(value, type[i]);\n      expectedTypes.push(assertedType.expectedType || '');\n      valid = assertedType.valid;\n    }\n  }\n\n  if (!valid) {\n    warn(\n      getInvalidTypeMessage(name, value, expectedTypes),\n      vm\n    );\n    return\n  }\n  var validator = prop.validator;\n  if (validator) {\n    if (!validator(value)) {\n      warn(\n        'Invalid prop: custom validator check failed for prop \"' + name + '\".',\n        vm\n      );\n    }\n  }\n}\n\nvar simpleCheckRE = /^(String|Number|Boolean|Function|Symbol)$/;\n\nfunction assertType (value, type) {\n  var valid;\n  var expectedType = getType(type);\n  if (simpleCheckRE.test(expectedType)) {\n    var t = typeof value;\n    valid = t === expectedType.toLowerCase();\n    // for primitive wrapper objects\n    if (!valid && t === 'object') {\n      valid = value instanceof type;\n    }\n  } else if (expectedType === 'Object') {\n    valid = isPlainObject(value);\n  } else if (expectedType === 'Array') {\n    valid = Array.isArray(value);\n  } else {\n    valid = value instanceof type;\n  }\n  return {\n    valid: valid,\n    expectedType: expectedType\n  }\n}\n\n/**\n * Use function string name to check built-in types,\n * because a simple equality check will fail when running\n * across different vms / iframes.\n */\nfunction getType (fn) {\n  var match = fn && fn.toString().match(/^\\s*function (\\w+)/);\n  return match ? match[1] : ''\n}\n\nfunction isSameType (a, b) {\n  return getType(a) === getType(b)\n}\n\nfunction getTypeIndex (type, expectedTypes) {\n  if (!Array.isArray(expectedTypes)) {\n    return isSameType(expectedTypes, type) ? 0 : -1\n  }\n  for (var i = 0, len = expectedTypes.length; i < len; i++) {\n    if (isSameType(expectedTypes[i], type)) {\n      return i\n    }\n  }\n  return -1\n}\n\nfunction getInvalidTypeMessage (name, value, expectedTypes) {\n  var message = \"Invalid prop: type check failed for prop \\\"\" + name + \"\\\".\" +\n    \" Expected \" + (expectedTypes.map(capitalize).join(', '));\n  var expectedType = expectedTypes[0];\n  var receivedType = toRawType(value);\n  var expectedValue = styleValue(value, expectedType);\n  var receivedValue = styleValue(value, receivedType);\n  // check if we need to specify expected value\n  if (expectedTypes.length === 1 &&\n      isExplicable(expectedType) &&\n      !isBoolean(expectedType, receivedType)) {\n    message += \" with value \" + expectedValue;\n  }\n  message += \", got \" + receivedType + \" \";\n  // check if we need to specify received value\n  if (isExplicable(receivedType)) {\n    message += \"with value \" + receivedValue + \".\";\n  }\n  return message\n}\n\nfunction styleValue (value, type) {\n  if (type === 'String') {\n    return (\"\\\"\" + value + \"\\\"\")\n  } else if (type === 'Number') {\n    return (\"\" + (Number(value)))\n  } else {\n    return (\"\" + value)\n  }\n}\n\nfunction isExplicable (value) {\n  var explicitTypes = ['string', 'number', 'boolean'];\n  return explicitTypes.some(function (elem) { return value.toLowerCase() === elem; })\n}\n\nfunction isBoolean () {\n  var args = [], len = arguments.length;\n  while ( len-- ) args[ len ] = arguments[ len ];\n\n  return args.some(function (elem) { return elem.toLowerCase() === 'boolean'; })\n}\n\n/*  */\n\nfunction handleError (err, vm, info) {\n  // Deactivate deps tracking while processing error handler to avoid possible infinite rendering.\n  // See: https://github.com/vuejs/vuex/issues/1505\n  pushTarget();\n  try {\n    if (vm) {\n      var cur = vm;\n      while ((cur = cur.$parent)) {\n        var hooks = cur.$options.errorCaptured;\n        if (hooks) {\n          for (var i = 0; i < hooks.length; i++) {\n            try {\n              var capture = hooks[i].call(cur, err, vm, info) === false;\n              if (capture) { return }\n            } catch (e) {\n              globalHandleError(e, cur, 'errorCaptured hook');\n            }\n          }\n        }\n      }\n    }\n    globalHandleError(err, vm, info);\n  } finally {\n    popTarget();\n  }\n}\n\nfunction invokeWithErrorHandling (\n  handler,\n  context,\n  args,\n  vm,\n  info\n) {\n  var res;\n  try {\n    res = args ? handler.apply(context, args) : handler.call(context);\n    if (res && !res._isVue && isPromise(res) && !res._handled) {\n      res.catch(function (e) { return handleError(e, vm, info + \" (Promise/async)\"); });\n      // issue #9511\n      // avoid catch triggering multiple times when nested calls\n      res._handled = true;\n    }\n  } catch (e) {\n    handleError(e, vm, info);\n  }\n  return res\n}\n\nfunction globalHandleError (err, vm, info) {\n  if (config.errorHandler) {\n    try {\n      return config.errorHandler.call(null, err, vm, info)\n    } catch (e) {\n      // if the user intentionally throws the original error in the handler,\n      // do not log it twice\n      if (e !== err) {\n        logError(e, null, 'config.errorHandler');\n      }\n    }\n  }\n  logError(err, vm, info);\n}\n\nfunction logError (err, vm, info) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn((\"Error in \" + info + \": \\\"\" + (err.toString()) + \"\\\"\"), vm);\n  }\n  /* istanbul ignore else */\n  if ((inBrowser || inWeex) && typeof console !== 'undefined') {\n    console.error(err);\n  } else {\n    throw err\n  }\n}\n\n/*  */\n\nvar callbacks = [];\nvar pending = false;\n\nfunction flushCallbacks () {\n  pending = false;\n  var copies = callbacks.slice(0);\n  callbacks.length = 0;\n  for (var i = 0; i < copies.length; i++) {\n    copies[i]();\n  }\n}\n\n// Here we have async deferring wrappers using microtasks.\n// In 2.5 we used (macro) tasks (in combination with microtasks).\n// However, it has subtle problems when state is changed right before repaint\n// (e.g. #6813, out-in transitions).\n// Also, using (macro) tasks in event handler would cause some weird behaviors\n// that cannot be circumvented (e.g. #7109, #7153, #7546, #7834, #8109).\n// So we now use microtasks everywhere, again.\n// A major drawback of this tradeoff is that there are some scenarios\n// where microtasks have too high a priority and fire in between supposedly\n// sequential events (e.g. #4521, #6690, which have workarounds)\n// or even between bubbling of the same event (#6566).\nvar timerFunc;\n\n// The nextTick behavior leverages the microtask queue, which can be accessed\n// via either native Promise.then or MutationObserver.\n// MutationObserver has wider support, however it is seriously bugged in\n// UIWebView in iOS >= 9.3.3 when triggered in touch event handlers. It\n// completely stops working after triggering a few times... so, if native\n// Promise is available, we will use it:\n/* istanbul ignore next, $flow-disable-line */\nif (typeof Promise !== 'undefined' && isNative(Promise)) {\n  var p = Promise.resolve();\n  timerFunc = function () {\n    p.then(flushCallbacks);\n    // In problematic UIWebViews, Promise.then doesn't completely break, but\n    // it can get stuck in a weird state where callbacks are pushed into the\n    // microtask queue but the queue isn't being flushed, until the browser\n    // needs to do some other work, e.g. handle a timer. Therefore we can\n    // \"force\" the microtask queue to be flushed by adding an empty timer.\n    if (isIOS) { setTimeout(noop); }\n  };\n} else if (!isIE && typeof MutationObserver !== 'undefined' && (\n  isNative(MutationObserver) ||\n  // PhantomJS and iOS 7.x\n  MutationObserver.toString() === '[object MutationObserverConstructor]'\n)) {\n  // Use MutationObserver where native Promise is not available,\n  // e.g. PhantomJS, iOS7, Android 4.4\n  // (#6466 MutationObserver is unreliable in IE11)\n  var counter = 1;\n  var observer = new MutationObserver(flushCallbacks);\n  var textNode = document.createTextNode(String(counter));\n  observer.observe(textNode, {\n    characterData: true\n  });\n  timerFunc = function () {\n    counter = (counter + 1) % 2;\n    textNode.data = String(counter);\n  };\n} else if (typeof setImmediate !== 'undefined' && isNative(setImmediate)) {\n  // Fallback to setImmediate.\n  // Technically it leverages the (macro) task queue,\n  // but it is still a better choice than setTimeout.\n  timerFunc = function () {\n    setImmediate(flushCallbacks);\n  };\n} else {\n  // Fallback to setTimeout.\n  timerFunc = function () {\n    setTimeout(flushCallbacks, 0);\n  };\n}\n\nfunction nextTick (cb, ctx) {\n  var _resolve;\n  callbacks.push(function () {\n    if (cb) {\n      try {\n        cb.call(ctx);\n      } catch (e) {\n        handleError(e, ctx, 'nextTick');\n      }\n    } else if (_resolve) {\n      _resolve(ctx);\n    }\n  });\n  if (!pending) {\n    pending = true;\n    timerFunc();\n  }\n  // $flow-disable-line\n  if (!cb && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve) {\n      _resolve = resolve;\n    })\n  }\n}\n\n/*  */\n\n/* not type checking this file because flow doesn't play well with Proxy */\n\nvar initProxy;\n\nif (process.env.NODE_ENV !== 'production') {\n  var allowedGlobals = makeMap(\n    'Infinity,undefined,NaN,isFinite,isNaN,' +\n    'parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,' +\n    'Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,' +\n    'require' // for Webpack/Browserify\n  );\n\n  var warnNonPresent = function (target, key) {\n    warn(\n      \"Property or method \\\"\" + key + \"\\\" is not defined on the instance but \" +\n      'referenced during render. Make sure that this property is reactive, ' +\n      'either in the data option, or for class-based components, by ' +\n      'initializing the property. ' +\n      'See: https://vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.',\n      target\n    );\n  };\n\n  var warnReservedPrefix = function (target, key) {\n    warn(\n      \"Property \\\"\" + key + \"\\\" must be accessed with \\\"$data.\" + key + \"\\\" because \" +\n      'properties starting with \"$\" or \"_\" are not proxied in the Vue instance to ' +\n      'prevent conflicts with Vue internals. ' +\n      'See: https://vuejs.org/v2/api/#data',\n      target\n    );\n  };\n\n  var hasProxy =\n    typeof Proxy !== 'undefined' && isNative(Proxy);\n\n  if (hasProxy) {\n    var isBuiltInModifier = makeMap('stop,prevent,self,ctrl,shift,alt,meta,exact');\n    config.keyCodes = new Proxy(config.keyCodes, {\n      set: function set (target, key, value) {\n        if (isBuiltInModifier(key)) {\n          warn((\"Avoid overwriting built-in modifier in config.keyCodes: .\" + key));\n          return false\n        } else {\n          target[key] = value;\n          return true\n        }\n      }\n    });\n  }\n\n  var hasHandler = {\n    has: function has (target, key) {\n      var has = key in target;\n      var isAllowed = allowedGlobals(key) ||\n        (typeof key === 'string' && key.charAt(0) === '_' && !(key in target.$data));\n      if (!has && !isAllowed) {\n        if (key in target.$data) { warnReservedPrefix(target, key); }\n        else { warnNonPresent(target, key); }\n      }\n      return has || !isAllowed\n    }\n  };\n\n  var getHandler = {\n    get: function get (target, key) {\n      if (typeof key === 'string' && !(key in target)) {\n        if (key in target.$data) { warnReservedPrefix(target, key); }\n        else { warnNonPresent(target, key); }\n      }\n      return target[key]\n    }\n  };\n\n  initProxy = function initProxy (vm) {\n    if (hasProxy) {\n      // determine which proxy handler to use\n      var options = vm.$options;\n      var handlers = options.render && options.render._withStripped\n        ? getHandler\n        : hasHandler;\n      vm._renderProxy = new Proxy(vm, handlers);\n    } else {\n      vm._renderProxy = vm;\n    }\n  };\n}\n\n/*  */\n\nvar seenObjects = new _Set();\n\n/**\n * Recursively traverse an object to evoke all converted\n * getters, so that every nested property inside the object\n * is collected as a \"deep\" dependency.\n */\nfunction traverse (val) {\n  _traverse(val, seenObjects);\n  seenObjects.clear();\n}\n\nfunction _traverse (val, seen) {\n  var i, keys;\n  var isA = Array.isArray(val);\n  if ((!isA && !isObject(val)) || Object.isFrozen(val) || val instanceof VNode) {\n    return\n  }\n  if (val.__ob__) {\n    var depId = val.__ob__.dep.id;\n    if (seen.has(depId)) {\n      return\n    }\n    seen.add(depId);\n  }\n  if (isA) {\n    i = val.length;\n    while (i--) { _traverse(val[i], seen); }\n  } else {\n    keys = Object.keys(val);\n    i = keys.length;\n    while (i--) { _traverse(val[keys[i]], seen); }\n  }\n}\n\nvar mark;\nvar measure;\n\nif (process.env.NODE_ENV !== 'production') {\n  var perf = inBrowser && window.performance;\n  /* istanbul ignore if */\n  if (\n    perf &&\n    perf.mark &&\n    perf.measure &&\n    perf.clearMarks &&\n    perf.clearMeasures\n  ) {\n    mark = function (tag) { return perf.mark(tag); };\n    measure = function (name, startTag, endTag) {\n      perf.measure(name, startTag, endTag);\n      perf.clearMarks(startTag);\n      perf.clearMarks(endTag);\n      // perf.clearMeasures(name)\n    };\n  }\n}\n\n/*  */\n\nvar normalizeEvent = cached(function (name) {\n  var passive = name.charAt(0) === '&';\n  name = passive ? name.slice(1) : name;\n  var once$$1 = name.charAt(0) === '~'; // Prefixed last, checked first\n  name = once$$1 ? name.slice(1) : name;\n  var capture = name.charAt(0) === '!';\n  name = capture ? name.slice(1) : name;\n  return {\n    name: name,\n    once: once$$1,\n    capture: capture,\n    passive: passive\n  }\n});\n\nfunction createFnInvoker (fns, vm) {\n  function invoker () {\n    var arguments$1 = arguments;\n\n    var fns = invoker.fns;\n    if (Array.isArray(fns)) {\n      var cloned = fns.slice();\n      for (var i = 0; i < cloned.length; i++) {\n        invokeWithErrorHandling(cloned[i], null, arguments$1, vm, \"v-on handler\");\n      }\n    } else {\n      // return handler return value for single handlers\n      return invokeWithErrorHandling(fns, null, arguments, vm, \"v-on handler\")\n    }\n  }\n  invoker.fns = fns;\n  return invoker\n}\n\nfunction updateListeners (\n  on,\n  oldOn,\n  add,\n  remove$$1,\n  createOnceHandler,\n  vm\n) {\n  var name, def$$1, cur, old, event;\n  for (name in on) {\n    def$$1 = cur = on[name];\n    old = oldOn[name];\n    event = normalizeEvent(name);\n    if (isUndef(cur)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Invalid handler for event \\\"\" + (event.name) + \"\\\": got \" + String(cur),\n        vm\n      );\n    } else if (isUndef(old)) {\n      if (isUndef(cur.fns)) {\n        cur = on[name] = createFnInvoker(cur, vm);\n      }\n      if (isTrue(event.once)) {\n        cur = on[name] = createOnceHandler(event.name, cur, event.capture);\n      }\n      add(event.name, cur, event.capture, event.passive, event.params);\n    } else if (cur !== old) {\n      old.fns = cur;\n      on[name] = old;\n    }\n  }\n  for (name in oldOn) {\n    if (isUndef(on[name])) {\n      event = normalizeEvent(name);\n      remove$$1(event.name, oldOn[name], event.capture);\n    }\n  }\n}\n\n/*  */\n\n/*  */\n\n// fixed by xxxxxx (mp properties)\nfunction extractPropertiesFromVNodeData(data, Ctor, res, context) {\n  var propOptions = Ctor.options.mpOptions && Ctor.options.mpOptions.properties;\n  if (isUndef(propOptions)) {\n    return res\n  }\n  var externalClasses = Ctor.options.mpOptions.externalClasses || [];\n  var attrs = data.attrs;\n  var props = data.props;\n  if (isDef(attrs) || isDef(props)) {\n    for (var key in propOptions) {\n      var altKey = hyphenate(key);\n      var result = checkProp(res, props, key, altKey, true) ||\n          checkProp(res, attrs, key, altKey, false);\n      // externalClass\n      if (\n        result &&\n        res[key] &&\n        externalClasses.indexOf(altKey) !== -1 &&\n        context[camelize(res[key])]\n      ) {\n        // 赋值 externalClass 真正的值(模板里 externalClass 的值可能是字符串)\n        res[key] = context[camelize(res[key])];\n      }\n    }\n  }\n  return res\n}\n\nfunction extractPropsFromVNodeData (\n  data,\n  Ctor,\n  tag,\n  context// fixed by xxxxxx\n) {\n  // we are only extracting raw values here.\n  // validation and default values are handled in the child\n  // component itself.\n  var propOptions = Ctor.options.props;\n  if (isUndef(propOptions)) {\n    // fixed by xxxxxx\n    return extractPropertiesFromVNodeData(data, Ctor, {}, context)\n  }\n  var res = {};\n  var attrs = data.attrs;\n  var props = data.props;\n  if (isDef(attrs) || isDef(props)) {\n    for (var key in propOptions) {\n      var altKey = hyphenate(key);\n      if (process.env.NODE_ENV !== 'production') {\n        var keyInLowerCase = key.toLowerCase();\n        if (\n          key !== keyInLowerCase &&\n          attrs && hasOwn(attrs, keyInLowerCase)\n        ) {\n          tip(\n            \"Prop \\\"\" + keyInLowerCase + \"\\\" is passed to component \" +\n            (formatComponentName(tag || Ctor)) + \", but the declared prop name is\" +\n            \" \\\"\" + key + \"\\\". \" +\n            \"Note that HTML attributes are case-insensitive and camelCased \" +\n            \"props need to use their kebab-case equivalents when using in-DOM \" +\n            \"templates. You should probably use \\\"\" + altKey + \"\\\" instead of \\\"\" + key + \"\\\".\"\n          );\n        }\n      }\n      checkProp(res, props, key, altKey, true) ||\n      checkProp(res, attrs, key, altKey, false);\n    }\n  }\n  // fixed by xxxxxx\n  return extractPropertiesFromVNodeData(data, Ctor, res, context)\n}\n\nfunction checkProp (\n  res,\n  hash,\n  key,\n  altKey,\n  preserve\n) {\n  if (isDef(hash)) {\n    if (hasOwn(hash, key)) {\n      res[key] = hash[key];\n      if (!preserve) {\n        delete hash[key];\n      }\n      return true\n    } else if (hasOwn(hash, altKey)) {\n      res[key] = hash[altKey];\n      if (!preserve) {\n        delete hash[altKey];\n      }\n      return true\n    }\n  }\n  return false\n}\n\n/*  */\n\n// The template compiler attempts to minimize the need for normalization by\n// statically analyzing the template at compile time.\n//\n// For plain HTML markup, normalization can be completely skipped because the\n// generated render function is guaranteed to return Array<VNode>. There are\n// two cases where extra normalization is needed:\n\n// 1. When the children contains components - because a functional component\n// may return an Array instead of a single root. In this case, just a simple\n// normalization is needed - if any child is an Array, we flatten the whole\n// thing with Array.prototype.concat. It is guaranteed to be only 1-level deep\n// because functional components already normalize their own children.\nfunction simpleNormalizeChildren (children) {\n  for (var i = 0; i < children.length; i++) {\n    if (Array.isArray(children[i])) {\n      return Array.prototype.concat.apply([], children)\n    }\n  }\n  return children\n}\n\n// 2. When the children contains constructs that always generated nested Arrays,\n// e.g. <template>, <slot>, v-for, or when the children is provided by user\n// with hand-written render functions / JSX. In such cases a full normalization\n// is needed to cater to all possible types of children values.\nfunction normalizeChildren (children) {\n  return isPrimitive(children)\n    ? [createTextVNode(children)]\n    : Array.isArray(children)\n      ? normalizeArrayChildren(children)\n      : undefined\n}\n\nfunction isTextNode (node) {\n  return isDef(node) && isDef(node.text) && isFalse(node.isComment)\n}\n\nfunction normalizeArrayChildren (children, nestedIndex) {\n  var res = [];\n  var i, c, lastIndex, last;\n  for (i = 0; i < children.length; i++) {\n    c = children[i];\n    if (isUndef(c) || typeof c === 'boolean') { continue }\n    lastIndex = res.length - 1;\n    last = res[lastIndex];\n    //  nested\n    if (Array.isArray(c)) {\n      if (c.length > 0) {\n        c = normalizeArrayChildren(c, ((nestedIndex || '') + \"_\" + i));\n        // merge adjacent text nodes\n        if (isTextNode(c[0]) && isTextNode(last)) {\n          res[lastIndex] = createTextVNode(last.text + (c[0]).text);\n          c.shift();\n        }\n        res.push.apply(res, c);\n      }\n    } else if (isPrimitive(c)) {\n      if (isTextNode(last)) {\n        // merge adjacent text nodes\n        // this is necessary for SSR hydration because text nodes are\n        // essentially merged when rendered to HTML strings\n        res[lastIndex] = createTextVNode(last.text + c);\n      } else if (c !== '') {\n        // convert primitive to vnode\n        res.push(createTextVNode(c));\n      }\n    } else {\n      if (isTextNode(c) && isTextNode(last)) {\n        // merge adjacent text nodes\n        res[lastIndex] = createTextVNode(last.text + c.text);\n      } else {\n        // default key for nested array children (likely generated by v-for)\n        if (isTrue(children._isVList) &&\n          isDef(c.tag) &&\n          isUndef(c.key) &&\n          isDef(nestedIndex)) {\n          c.key = \"__vlist\" + nestedIndex + \"_\" + i + \"__\";\n        }\n        res.push(c);\n      }\n    }\n  }\n  return res\n}\n\n/*  */\n\nfunction initProvide (vm) {\n  var provide = vm.$options.provide;\n  if (provide) {\n    vm._provided = typeof provide === 'function'\n      ? provide.call(vm)\n      : provide;\n  }\n}\n\nfunction initInjections (vm) {\n  var result = resolveInject(vm.$options.inject, vm);\n  if (result) {\n    toggleObserving(false);\n    Object.keys(result).forEach(function (key) {\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        defineReactive$$1(vm, key, result[key], function () {\n          warn(\n            \"Avoid mutating an injected value directly since the changes will be \" +\n            \"overwritten whenever the provided component re-renders. \" +\n            \"injection being mutated: \\\"\" + key + \"\\\"\",\n            vm\n          );\n        });\n      } else {\n        defineReactive$$1(vm, key, result[key]);\n      }\n    });\n    toggleObserving(true);\n  }\n}\n\nfunction resolveInject (inject, vm) {\n  if (inject) {\n    // inject is :any because flow is not smart enough to figure out cached\n    var result = Object.create(null);\n    var keys = hasSymbol\n      ? Reflect.ownKeys(inject)\n      : Object.keys(inject);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n      // #6574 in case the inject object is observed...\n      if (key === '__ob__') { continue }\n      var provideKey = inject[key].from;\n      var source = vm;\n      while (source) {\n        if (source._provided && hasOwn(source._provided, provideKey)) {\n          result[key] = source._provided[provideKey];\n          break\n        }\n        source = source.$parent;\n      }\n      if (!source) {\n        if ('default' in inject[key]) {\n          var provideDefault = inject[key].default;\n          result[key] = typeof provideDefault === 'function'\n            ? provideDefault.call(vm)\n            : provideDefault;\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn((\"Injection \\\"\" + key + \"\\\" not found\"), vm);\n        }\n      }\n    }\n    return result\n  }\n}\n\n/*  */\n\n\n\n/**\n * Runtime helper for resolving raw children VNodes into a slot object.\n */\nfunction resolveSlots (\n  children,\n  context\n) {\n  if (!children || !children.length) {\n    return {}\n  }\n  var slots = {};\n  for (var i = 0, l = children.length; i < l; i++) {\n    var child = children[i];\n    var data = child.data;\n    // remove slot attribute if the node is resolved as a Vue slot node\n    if (data && data.attrs && data.attrs.slot) {\n      delete data.attrs.slot;\n    }\n    // named slots should only be respected if the vnode was rendered in the\n    // same context.\n    if ((child.context === context || child.fnContext === context) &&\n      data && data.slot != null\n    ) {\n      var name = data.slot;\n      var slot = (slots[name] || (slots[name] = []));\n      if (child.tag === 'template') {\n        slot.push.apply(slot, child.children || []);\n      } else {\n        slot.push(child);\n      }\n    } else {\n      // fixed by xxxxxx 临时 hack 掉 uni-app 中的异步 name slot page\n      if(child.asyncMeta && child.asyncMeta.data && child.asyncMeta.data.slot === 'page'){\n        (slots['page'] || (slots['page'] = [])).push(child);\n      }else{\n        (slots.default || (slots.default = [])).push(child);\n      }\n    }\n  }\n  // ignore slots that contains only whitespace\n  for (var name$1 in slots) {\n    if (slots[name$1].every(isWhitespace)) {\n      delete slots[name$1];\n    }\n  }\n  return slots\n}\n\nfunction isWhitespace (node) {\n  return (node.isComment && !node.asyncFactory) || node.text === ' '\n}\n\n/*  */\n\nfunction normalizeScopedSlots (\n  slots,\n  normalSlots,\n  prevSlots\n) {\n  var res;\n  var hasNormalSlots = Object.keys(normalSlots).length > 0;\n  var isStable = slots ? !!slots.$stable : !hasNormalSlots;\n  var key = slots && slots.$key;\n  if (!slots) {\n    res = {};\n  } else if (slots._normalized) {\n    // fast path 1: child component re-render only, parent did not change\n    return slots._normalized\n  } else if (\n    isStable &&\n    prevSlots &&\n    prevSlots !== emptyObject &&\n    key === prevSlots.$key &&\n    !hasNormalSlots &&\n    !prevSlots.$hasNormal\n  ) {\n    // fast path 2: stable scoped slots w/ no normal slots to proxy,\n    // only need to normalize once\n    return prevSlots\n  } else {\n    res = {};\n    for (var key$1 in slots) {\n      if (slots[key$1] && key$1[0] !== '$') {\n        res[key$1] = normalizeScopedSlot(normalSlots, key$1, slots[key$1]);\n      }\n    }\n  }\n  // expose normal slots on scopedSlots\n  for (var key$2 in normalSlots) {\n    if (!(key$2 in res)) {\n      res[key$2] = proxyNormalSlot(normalSlots, key$2);\n    }\n  }\n  // avoriaz seems to mock a non-extensible $scopedSlots object\n  // and when that is passed down this would cause an error\n  if (slots && Object.isExtensible(slots)) {\n    (slots)._normalized = res;\n  }\n  def(res, '$stable', isStable);\n  def(res, '$key', key);\n  def(res, '$hasNormal', hasNormalSlots);\n  return res\n}\n\nfunction normalizeScopedSlot(normalSlots, key, fn) {\n  var normalized = function () {\n    var res = arguments.length ? fn.apply(null, arguments) : fn({});\n    res = res && typeof res === 'object' && !Array.isArray(res)\n      ? [res] // single vnode\n      : normalizeChildren(res);\n    return res && (\n      res.length === 0 ||\n      (res.length === 1 && res[0].isComment) // #9658\n    ) ? undefined\n      : res\n  };\n  // this is a slot using the new v-slot syntax without scope. although it is\n  // compiled as a scoped slot, render fn users would expect it to be present\n  // on this.$slots because the usage is semantically a normal slot.\n  if (fn.proxy) {\n    Object.defineProperty(normalSlots, key, {\n      get: normalized,\n      enumerable: true,\n      configurable: true\n    });\n  }\n  return normalized\n}\n\nfunction proxyNormalSlot(slots, key) {\n  return function () { return slots[key]; }\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering v-for lists.\n */\nfunction renderList (\n  val,\n  render\n) {\n  var ret, i, l, keys, key;\n  if (Array.isArray(val) || typeof val === 'string') {\n    ret = new Array(val.length);\n    for (i = 0, l = val.length; i < l; i++) {\n      ret[i] = render(val[i], i, i, i); // fixed by xxxxxx\n    }\n  } else if (typeof val === 'number') {\n    ret = new Array(val);\n    for (i = 0; i < val; i++) {\n      ret[i] = render(i + 1, i, i, i); // fixed by xxxxxx\n    }\n  } else if (isObject(val)) {\n    if (hasSymbol && val[Symbol.iterator]) {\n      ret = [];\n      var iterator = val[Symbol.iterator]();\n      var result = iterator.next();\n      while (!result.done) {\n        ret.push(render(result.value, ret.length, i, i++)); // fixed by xxxxxx\n        result = iterator.next();\n      }\n    } else {\n      keys = Object.keys(val);\n      ret = new Array(keys.length);\n      for (i = 0, l = keys.length; i < l; i++) {\n        key = keys[i];\n        ret[i] = render(val[key], key, i, i); // fixed by xxxxxx\n      }\n    }\n  }\n  if (!isDef(ret)) {\n    ret = [];\n  }\n  (ret)._isVList = true;\n  return ret\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering <slot>\n */\nfunction renderSlot (\n  name,\n  fallback,\n  props,\n  bindObject\n) {\n  var scopedSlotFn = this.$scopedSlots[name];\n  var nodes;\n  if (scopedSlotFn) { // scoped slot\n    props = props || {};\n    if (bindObject) {\n      if (process.env.NODE_ENV !== 'production' && !isObject(bindObject)) {\n        warn(\n          'slot v-bind without argument expects an Object',\n          this\n        );\n      }\n      props = extend(extend({}, bindObject), props);\n    }\n    // fixed by xxxxxx app-plus scopedSlot\n    nodes = scopedSlotFn(props, this, props._i) || fallback;\n  } else {\n    nodes = this.$slots[name] || fallback;\n  }\n\n  var target = props && props.slot;\n  if (target) {\n    return this.$createElement('template', { slot: target }, nodes)\n  } else {\n    return nodes\n  }\n}\n\n/*  */\n\n/**\n * Runtime helper for resolving filters\n */\nfunction resolveFilter (id) {\n  return resolveAsset(this.$options, 'filters', id, true) || identity\n}\n\n/*  */\n\nfunction isKeyNotMatch (expect, actual) {\n  if (Array.isArray(expect)) {\n    return expect.indexOf(actual) === -1\n  } else {\n    return expect !== actual\n  }\n}\n\n/**\n * Runtime helper for checking keyCodes from config.\n * exposed as Vue.prototype._k\n * passing in eventKeyName as last argument separately for backwards compat\n */\nfunction checkKeyCodes (\n  eventKeyCode,\n  key,\n  builtInKeyCode,\n  eventKeyName,\n  builtInKeyName\n) {\n  var mappedKeyCode = config.keyCodes[key] || builtInKeyCode;\n  if (builtInKeyName && eventKeyName && !config.keyCodes[key]) {\n    return isKeyNotMatch(builtInKeyName, eventKeyName)\n  } else if (mappedKeyCode) {\n    return isKeyNotMatch(mappedKeyCode, eventKeyCode)\n  } else if (eventKeyName) {\n    return hyphenate(eventKeyName) !== key\n  }\n}\n\n/*  */\n\n/**\n * Runtime helper for merging v-bind=\"object\" into a VNode's data.\n */\nfunction bindObjectProps (\n  data,\n  tag,\n  value,\n  asProp,\n  isSync\n) {\n  if (value) {\n    if (!isObject(value)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        'v-bind without argument expects an Object or Array value',\n        this\n      );\n    } else {\n      if (Array.isArray(value)) {\n        value = toObject(value);\n      }\n      var hash;\n      var loop = function ( key ) {\n        if (\n          key === 'class' ||\n          key === 'style' ||\n          isReservedAttribute(key)\n        ) {\n          hash = data;\n        } else {\n          var type = data.attrs && data.attrs.type;\n          hash = asProp || config.mustUseProp(tag, type, key)\n            ? data.domProps || (data.domProps = {})\n            : data.attrs || (data.attrs = {});\n        }\n        var camelizedKey = camelize(key);\n        var hyphenatedKey = hyphenate(key);\n        if (!(camelizedKey in hash) && !(hyphenatedKey in hash)) {\n          hash[key] = value[key];\n\n          if (isSync) {\n            var on = data.on || (data.on = {});\n            on[(\"update:\" + key)] = function ($event) {\n              value[key] = $event;\n            };\n          }\n        }\n      };\n\n      for (var key in value) loop( key );\n    }\n  }\n  return data\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering static trees.\n */\nfunction renderStatic (\n  index,\n  isInFor\n) {\n  var cached = this._staticTrees || (this._staticTrees = []);\n  var tree = cached[index];\n  // if has already-rendered static tree and not inside v-for,\n  // we can reuse the same tree.\n  if (tree && !isInFor) {\n    return tree\n  }\n  // otherwise, render a fresh tree.\n  tree = cached[index] = this.$options.staticRenderFns[index].call(\n    this._renderProxy,\n    null,\n    this // for render fns generated for functional component templates\n  );\n  markStatic(tree, (\"__static__\" + index), false);\n  return tree\n}\n\n/**\n * Runtime helper for v-once.\n * Effectively it means marking the node as static with a unique key.\n */\nfunction markOnce (\n  tree,\n  index,\n  key\n) {\n  markStatic(tree, (\"__once__\" + index + (key ? (\"_\" + key) : \"\")), true);\n  return tree\n}\n\nfunction markStatic (\n  tree,\n  key,\n  isOnce\n) {\n  if (Array.isArray(tree)) {\n    for (var i = 0; i < tree.length; i++) {\n      if (tree[i] && typeof tree[i] !== 'string') {\n        markStaticNode(tree[i], (key + \"_\" + i), isOnce);\n      }\n    }\n  } else {\n    markStaticNode(tree, key, isOnce);\n  }\n}\n\nfunction markStaticNode (node, key, isOnce) {\n  node.isStatic = true;\n  node.key = key;\n  node.isOnce = isOnce;\n}\n\n/*  */\n\nfunction bindObjectListeners (data, value) {\n  if (value) {\n    if (!isPlainObject(value)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        'v-on without argument expects an Object value',\n        this\n      );\n    } else {\n      var on = data.on = data.on ? extend({}, data.on) : {};\n      for (var key in value) {\n        var existing = on[key];\n        var ours = value[key];\n        on[key] = existing ? [].concat(existing, ours) : ours;\n      }\n    }\n  }\n  return data\n}\n\n/*  */\n\nfunction resolveScopedSlots (\n  fns, // see flow/vnode\n  res,\n  // the following are added in 2.6\n  hasDynamicKeys,\n  contentHashKey\n) {\n  res = res || { $stable: !hasDynamicKeys };\n  for (var i = 0; i < fns.length; i++) {\n    var slot = fns[i];\n    if (Array.isArray(slot)) {\n      resolveScopedSlots(slot, res, hasDynamicKeys);\n    } else if (slot) {\n      // marker for reverse proxying v-slot without scope on this.$slots\n      if (slot.proxy) {\n        slot.fn.proxy = true;\n      }\n      res[slot.key] = slot.fn;\n    }\n  }\n  if (contentHashKey) {\n    (res).$key = contentHashKey;\n  }\n  return res\n}\n\n/*  */\n\nfunction bindDynamicKeys (baseObj, values) {\n  for (var i = 0; i < values.length; i += 2) {\n    var key = values[i];\n    if (typeof key === 'string' && key) {\n      baseObj[values[i]] = values[i + 1];\n    } else if (process.env.NODE_ENV !== 'production' && key !== '' && key !== null) {\n      // null is a special value for explicitly removing a binding\n      warn(\n        (\"Invalid value for dynamic directive argument (expected string or null): \" + key),\n        this\n      );\n    }\n  }\n  return baseObj\n}\n\n// helper to dynamically append modifier runtime markers to event names.\n// ensure only append when value is already string, otherwise it will be cast\n// to string and cause the type check to miss.\nfunction prependModifier (value, symbol) {\n  return typeof value === 'string' ? symbol + value : value\n}\n\n/*  */\n\nfunction installRenderHelpers (target) {\n  target._o = markOnce;\n  target._n = toNumber;\n  target._s = toString;\n  target._l = renderList;\n  target._t = renderSlot;\n  target._q = looseEqual;\n  target._i = looseIndexOf;\n  target._m = renderStatic;\n  target._f = resolveFilter;\n  target._k = checkKeyCodes;\n  target._b = bindObjectProps;\n  target._v = createTextVNode;\n  target._e = createEmptyVNode;\n  target._u = resolveScopedSlots;\n  target._g = bindObjectListeners;\n  target._d = bindDynamicKeys;\n  target._p = prependModifier;\n}\n\n/*  */\n\nfunction FunctionalRenderContext (\n  data,\n  props,\n  children,\n  parent,\n  Ctor\n) {\n  var this$1 = this;\n\n  var options = Ctor.options;\n  // ensure the createElement function in functional components\n  // gets a unique context - this is necessary for correct named slot check\n  var contextVm;\n  if (hasOwn(parent, '_uid')) {\n    contextVm = Object.create(parent);\n    // $flow-disable-line\n    contextVm._original = parent;\n  } else {\n    // the context vm passed in is a functional context as well.\n    // in this case we want to make sure we are able to get a hold to the\n    // real context instance.\n    contextVm = parent;\n    // $flow-disable-line\n    parent = parent._original;\n  }\n  var isCompiled = isTrue(options._compiled);\n  var needNormalization = !isCompiled;\n\n  this.data = data;\n  this.props = props;\n  this.children = children;\n  this.parent = parent;\n  this.listeners = data.on || emptyObject;\n  this.injections = resolveInject(options.inject, parent);\n  this.slots = function () {\n    if (!this$1.$slots) {\n      normalizeScopedSlots(\n        data.scopedSlots,\n        this$1.$slots = resolveSlots(children, parent)\n      );\n    }\n    return this$1.$slots\n  };\n\n  Object.defineProperty(this, 'scopedSlots', ({\n    enumerable: true,\n    get: function get () {\n      return normalizeScopedSlots(data.scopedSlots, this.slots())\n    }\n  }));\n\n  // support for compiled functional template\n  if (isCompiled) {\n    // exposing $options for renderStatic()\n    this.$options = options;\n    // pre-resolve slots for renderSlot()\n    this.$slots = this.slots();\n    this.$scopedSlots = normalizeScopedSlots(data.scopedSlots, this.$slots);\n  }\n\n  if (options._scopeId) {\n    this._c = function (a, b, c, d) {\n      var vnode = createElement(contextVm, a, b, c, d, needNormalization);\n      if (vnode && !Array.isArray(vnode)) {\n        vnode.fnScopeId = options._scopeId;\n        vnode.fnContext = parent;\n      }\n      return vnode\n    };\n  } else {\n    this._c = function (a, b, c, d) { return createElement(contextVm, a, b, c, d, needNormalization); };\n  }\n}\n\ninstallRenderHelpers(FunctionalRenderContext.prototype);\n\nfunction createFunctionalComponent (\n  Ctor,\n  propsData,\n  data,\n  contextVm,\n  children\n) {\n  var options = Ctor.options;\n  var props = {};\n  var propOptions = options.props;\n  if (isDef(propOptions)) {\n    for (var key in propOptions) {\n      props[key] = validateProp(key, propOptions, propsData || emptyObject);\n    }\n  } else {\n    if (isDef(data.attrs)) { mergeProps(props, data.attrs); }\n    if (isDef(data.props)) { mergeProps(props, data.props); }\n  }\n\n  var renderContext = new FunctionalRenderContext(\n    data,\n    props,\n    children,\n    contextVm,\n    Ctor\n  );\n\n  var vnode = options.render.call(null, renderContext._c, renderContext);\n\n  if (vnode instanceof VNode) {\n    return cloneAndMarkFunctionalResult(vnode, data, renderContext.parent, options, renderContext)\n  } else if (Array.isArray(vnode)) {\n    var vnodes = normalizeChildren(vnode) || [];\n    var res = new Array(vnodes.length);\n    for (var i = 0; i < vnodes.length; i++) {\n      res[i] = cloneAndMarkFunctionalResult(vnodes[i], data, renderContext.parent, options, renderContext);\n    }\n    return res\n  }\n}\n\nfunction cloneAndMarkFunctionalResult (vnode, data, contextVm, options, renderContext) {\n  // #7817 clone node before setting fnContext, otherwise if the node is reused\n  // (e.g. it was from a cached normal slot) the fnContext causes named slots\n  // that should not be matched to match.\n  var clone = cloneVNode(vnode);\n  clone.fnContext = contextVm;\n  clone.fnOptions = options;\n  if (process.env.NODE_ENV !== 'production') {\n    (clone.devtoolsMeta = clone.devtoolsMeta || {}).renderContext = renderContext;\n  }\n  if (data.slot) {\n    (clone.data || (clone.data = {})).slot = data.slot;\n  }\n  return clone\n}\n\nfunction mergeProps (to, from) {\n  for (var key in from) {\n    to[camelize(key)] = from[key];\n  }\n}\n\n/*  */\n\n/*  */\n\n/*  */\n\n/*  */\n\n// inline hooks to be invoked on component VNodes during patch\nvar componentVNodeHooks = {\n  init: function init (vnode, hydrating) {\n    if (\n      vnode.componentInstance &&\n      !vnode.componentInstance._isDestroyed &&\n      vnode.data.keepAlive\n    ) {\n      // kept-alive components, treat as a patch\n      var mountedNode = vnode; // work around flow\n      componentVNodeHooks.prepatch(mountedNode, mountedNode);\n    } else {\n      var child = vnode.componentInstance = createComponentInstanceForVnode(\n        vnode,\n        activeInstance\n      );\n      child.$mount(hydrating ? vnode.elm : undefined, hydrating);\n    }\n  },\n\n  prepatch: function prepatch (oldVnode, vnode) {\n    var options = vnode.componentOptions;\n    var child = vnode.componentInstance = oldVnode.componentInstance;\n    updateChildComponent(\n      child,\n      options.propsData, // updated props\n      options.listeners, // updated listeners\n      vnode, // new parent vnode\n      options.children // new children\n    );\n  },\n\n  insert: function insert (vnode) {\n    var context = vnode.context;\n    var componentInstance = vnode.componentInstance;\n    if (!componentInstance._isMounted) {\n      callHook(componentInstance, 'onServiceCreated');\n      callHook(componentInstance, 'onServiceAttached');\n      componentInstance._isMounted = true;\n      callHook(componentInstance, 'mounted');\n    }\n    if (vnode.data.keepAlive) {\n      if (context._isMounted) {\n        // vue-router#1212\n        // During updates, a kept-alive component's child components may\n        // change, so directly walking the tree here may call activated hooks\n        // on incorrect children. Instead we push them into a queue which will\n        // be processed after the whole patch process ended.\n        queueActivatedComponent(componentInstance);\n      } else {\n        activateChildComponent(componentInstance, true /* direct */);\n      }\n    }\n  },\n\n  destroy: function destroy (vnode) {\n    var componentInstance = vnode.componentInstance;\n    if (!componentInstance._isDestroyed) {\n      if (!vnode.data.keepAlive) {\n        componentInstance.$destroy();\n      } else {\n        deactivateChildComponent(componentInstance, true /* direct */);\n      }\n    }\n  }\n};\n\nvar hooksToMerge = Object.keys(componentVNodeHooks);\n\nfunction createComponent (\n  Ctor,\n  data,\n  context,\n  children,\n  tag\n) {\n  if (isUndef(Ctor)) {\n    return\n  }\n\n  var baseCtor = context.$options._base;\n\n  // plain options object: turn it into a constructor\n  if (isObject(Ctor)) {\n    Ctor = baseCtor.extend(Ctor);\n  }\n\n  // if at this stage it's not a constructor or an async component factory,\n  // reject.\n  if (typeof Ctor !== 'function') {\n    if (process.env.NODE_ENV !== 'production') {\n      warn((\"Invalid Component definition: \" + (String(Ctor))), context);\n    }\n    return\n  }\n\n  // async component\n  var asyncFactory;\n  if (isUndef(Ctor.cid)) {\n    asyncFactory = Ctor;\n    Ctor = resolveAsyncComponent(asyncFactory, baseCtor);\n    if (Ctor === undefined) {\n      // return a placeholder node for async component, which is rendered\n      // as a comment node but preserves all the raw information for the node.\n      // the information will be used for async server-rendering and hydration.\n      return createAsyncPlaceholder(\n        asyncFactory,\n        data,\n        context,\n        children,\n        tag\n      )\n    }\n  }\n\n  data = data || {};\n\n  // resolve constructor options in case global mixins are applied after\n  // component constructor creation\n  resolveConstructorOptions(Ctor);\n\n  // transform component v-model data into props & events\n  if (isDef(data.model)) {\n    transformModel(Ctor.options, data);\n  }\n\n  // extract props\n  var propsData = extractPropsFromVNodeData(data, Ctor, tag, context); // fixed by xxxxxx\n\n  // functional component\n  if (isTrue(Ctor.options.functional)) {\n    return createFunctionalComponent(Ctor, propsData, data, context, children)\n  }\n\n  // extract listeners, since these needs to be treated as\n  // child component listeners instead of DOM listeners\n  var listeners = data.on;\n  // replace with listeners with .native modifier\n  // so it gets processed during parent component patch.\n  data.on = data.nativeOn;\n\n  if (isTrue(Ctor.options.abstract)) {\n    // abstract components do not keep anything\n    // other than props & listeners & slot\n\n    // work around flow\n    var slot = data.slot;\n    data = {};\n    if (slot) {\n      data.slot = slot;\n    }\n  }\n\n  // install component management hooks onto the placeholder node\n  installComponentHooks(data);\n\n  // return a placeholder vnode\n  var name = Ctor.options.name || tag;\n  var vnode = new VNode(\n    (\"vue-component-\" + (Ctor.cid) + (name ? (\"-\" + name) : '')),\n    data, undefined, undefined, undefined, context,\n    { Ctor: Ctor, propsData: propsData, listeners: listeners, tag: tag, children: children },\n    asyncFactory\n  );\n\n  return vnode\n}\n\nfunction createComponentInstanceForVnode (\n  vnode, // we know it's MountedComponentVNode but flow doesn't\n  parent // activeInstance in lifecycle state\n) {\n  var options = {\n    _isComponent: true,\n    _parentVnode: vnode,\n    parent: parent\n  };\n  // check inline-template render functions\n  var inlineTemplate = vnode.data.inlineTemplate;\n  if (isDef(inlineTemplate)) {\n    options.render = inlineTemplate.render;\n    options.staticRenderFns = inlineTemplate.staticRenderFns;\n  }\n  return new vnode.componentOptions.Ctor(options)\n}\n\nfunction installComponentHooks (data) {\n  var hooks = data.hook || (data.hook = {});\n  for (var i = 0; i < hooksToMerge.length; i++) {\n    var key = hooksToMerge[i];\n    var existing = hooks[key];\n    var toMerge = componentVNodeHooks[key];\n    if (existing !== toMerge && !(existing && existing._merged)) {\n      hooks[key] = existing ? mergeHook$1(toMerge, existing) : toMerge;\n    }\n  }\n}\n\nfunction mergeHook$1 (f1, f2) {\n  var merged = function (a, b) {\n    // flow complains about extra args which is why we use any\n    f1(a, b);\n    f2(a, b);\n  };\n  merged._merged = true;\n  return merged\n}\n\n// transform component v-model info (value and callback) into\n// prop and event handler respectively.\nfunction transformModel (options, data) {\n  var prop = (options.model && options.model.prop) || 'value';\n  var event = (options.model && options.model.event) || 'input'\n  ;(data.attrs || (data.attrs = {}))[prop] = data.model.value;\n  var on = data.on || (data.on = {});\n  var existing = on[event];\n  var callback = data.model.callback;\n  if (isDef(existing)) {\n    if (\n      Array.isArray(existing)\n        ? existing.indexOf(callback) === -1\n        : existing !== callback\n    ) {\n      on[event] = [callback].concat(existing);\n    }\n  } else {\n    on[event] = callback;\n  }\n}\n\n/*  */\n\nvar SIMPLE_NORMALIZE = 1;\nvar ALWAYS_NORMALIZE = 2;\n\n// wrapper function for providing a more flexible interface\n// without getting yelled at by flow\nfunction createElement (\n  context,\n  tag,\n  data,\n  children,\n  normalizationType,\n  alwaysNormalize\n) {\n  if (Array.isArray(data) || isPrimitive(data)) {\n    normalizationType = children;\n    children = data;\n    data = undefined;\n  }\n  if (isTrue(alwaysNormalize)) {\n    normalizationType = ALWAYS_NORMALIZE;\n  }\n  return _createElement(context, tag, data, children, normalizationType)\n}\n\nfunction _createElement (\n  context,\n  tag,\n  data,\n  children,\n  normalizationType\n) {\n  if (isDef(data) && isDef((data).__ob__)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      \"Avoid using observed data object as vnode data: \" + (JSON.stringify(data)) + \"\\n\" +\n      'Always create fresh vnode data objects in each render!',\n      context\n    );\n    return createEmptyVNode()\n  }\n  // object syntax in v-bind\n  if (isDef(data) && isDef(data.is)) {\n    tag = data.is;\n  }\n  if (!tag) {\n    // in case of component :is set to falsy value\n    return createEmptyVNode()\n  }\n  // warn against non-primitive key\n  if (process.env.NODE_ENV !== 'production' &&\n    isDef(data) && isDef(data.key) && !isPrimitive(data.key)\n  ) {\n    {\n      warn(\n        'Avoid using non-primitive value as key, ' +\n        'use string/number value instead.',\n        context\n      );\n    }\n  }\n  // support single function children as default scoped slot\n  if (Array.isArray(children) &&\n    typeof children[0] === 'function'\n  ) {\n    data = data || {};\n    data.scopedSlots = { default: children[0] };\n    children.length = 0;\n  }\n  if (normalizationType === ALWAYS_NORMALIZE) {\n    children = normalizeChildren(children);\n  } else if (normalizationType === SIMPLE_NORMALIZE) {\n    children = simpleNormalizeChildren(children);\n  }\n  var vnode, ns;\n  if (typeof tag === 'string') {\n    var Ctor;\n    ns = (context.$vnode && context.$vnode.ns) || config.getTagNamespace(tag);\n    if (config.isReservedTag(tag)) {\n      // platform built-in elements\n      if (process.env.NODE_ENV !== 'production' && isDef(data) && isDef(data.nativeOn)) {\n        warn(\n          (\"The .native modifier for v-on is only valid on components but it was used on <\" + tag + \">.\"),\n          context\n        );\n      }\n      vnode = new VNode(\n        config.parsePlatformTagName(tag), data, children,\n        undefined, undefined, context\n      );\n    } else if ((!data || !data.pre) && isDef(Ctor = resolveAsset(context.$options, 'components', tag))) {\n      // component\n      vnode = createComponent(Ctor, data, context, children, tag);\n    } else {\n      // unknown or unlisted namespaced elements\n      // check at runtime because it may get assigned a namespace when its\n      // parent normalizes children\n      vnode = new VNode(\n        tag, data, children,\n        undefined, undefined, context\n      );\n    }\n  } else {\n    // direct component options / constructor\n    vnode = createComponent(tag, data, context, children);\n  }\n  if (Array.isArray(vnode)) {\n    return vnode\n  } else if (isDef(vnode)) {\n    if (isDef(ns)) { applyNS(vnode, ns); }\n    if (isDef(data)) { registerDeepBindings(data); }\n    return vnode\n  } else {\n    return createEmptyVNode()\n  }\n}\n\nfunction applyNS (vnode, ns, force) {\n  vnode.ns = ns;\n  if (vnode.tag === 'foreignObject') {\n    // use default namespace inside foreignObject\n    ns = undefined;\n    force = true;\n  }\n  if (isDef(vnode.children)) {\n    for (var i = 0, l = vnode.children.length; i < l; i++) {\n      var child = vnode.children[i];\n      if (isDef(child.tag) && (\n        isUndef(child.ns) || (isTrue(force) && child.tag !== 'svg'))) {\n        applyNS(child, ns, force);\n      }\n    }\n  }\n}\n\n// ref #5318\n// necessary to ensure parent re-render when deep bindings like :style and\n// :class are used on slot nodes\nfunction registerDeepBindings (data) {\n  if (isObject(data.style)) {\n    traverse(data.style);\n  }\n  if (isObject(data.class)) {\n    traverse(data.class);\n  }\n}\n\n/*  */\n\nfunction initRender (vm) {\n  vm._vnode = null; // the root of the child tree\n  vm._staticTrees = null; // v-once cached trees\n  var options = vm.$options;\n  var parentVnode = vm.$vnode = options._parentVnode; // the placeholder node in parent tree\n  var renderContext = parentVnode && parentVnode.context;\n  vm.$slots = resolveSlots(options._renderChildren, renderContext);\n  vm.$scopedSlots = emptyObject;\n  // bind the createElement fn to this instance\n  // so that we get proper render context inside it.\n  // args order: tag, data, children, normalizationType, alwaysNormalize\n  // internal version is used by render functions compiled from templates\n  vm._c = function (a, b, c, d) { return createElement(vm, a, b, c, d, false); };\n  // normalization is always applied for the public version, used in\n  // user-written render functions.\n  vm.$createElement = function (a, b, c, d) { return createElement(vm, a, b, c, d, true); };\n\n  // $attrs & $listeners are exposed for easier HOC creation.\n  // they need to be reactive so that HOCs using them are always updated\n  var parentData = parentVnode && parentVnode.data;\n\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production') {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$attrs is readonly.\", vm);\n    }, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$listeners is readonly.\", vm);\n    }, true);\n  } else {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, null, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, null, true);\n  }\n}\n\nvar currentRenderingInstance = null;\n\nfunction renderMixin (Vue) {\n  // install runtime convenience helpers\n  installRenderHelpers(Vue.prototype);\n\n  Vue.prototype.$nextTick = function (fn) {\n    return nextTick(fn, this)\n  };\n\n  Vue.prototype._render = function () {\n    var vm = this;\n    var ref = vm.$options;\n    var render = ref.render;\n    var _parentVnode = ref._parentVnode;\n\n    if (_parentVnode) {\n      vm.$scopedSlots = normalizeScopedSlots(\n        _parentVnode.data.scopedSlots,\n        vm.$slots,\n        vm.$scopedSlots\n      );\n    }\n\n    // set parent vnode. this allows render functions to have access\n    // to the data on the placeholder node.\n    vm.$vnode = _parentVnode;\n    // render self\n    var vnode;\n    try {\n      // There's no need to maintain a stack because all render fns are called\n      // separately from one another. Nested component's render fns are called\n      // when parent component is patched.\n      currentRenderingInstance = vm;\n      vnode = render.call(vm._renderProxy, vm.$createElement);\n    } catch (e) {\n      handleError(e, vm, \"render\");\n      // return error render result,\n      // or previous vnode to prevent render error causing blank component\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production' && vm.$options.renderError) {\n        try {\n          vnode = vm.$options.renderError.call(vm._renderProxy, vm.$createElement, e);\n        } catch (e) {\n          handleError(e, vm, \"renderError\");\n          vnode = vm._vnode;\n        }\n      } else {\n        vnode = vm._vnode;\n      }\n    } finally {\n      currentRenderingInstance = null;\n    }\n    // if the returned array contains only a single node, allow it\n    if (Array.isArray(vnode) && vnode.length === 1) {\n      vnode = vnode[0];\n    }\n    // return empty vnode in case the render function errored out\n    if (!(vnode instanceof VNode)) {\n      if (process.env.NODE_ENV !== 'production' && Array.isArray(vnode)) {\n        warn(\n          'Multiple root nodes returned from render function. Render function ' +\n          'should return a single root node.',\n          vm\n        );\n      }\n      vnode = createEmptyVNode();\n    }\n    // set parent\n    vnode.parent = _parentVnode;\n    return vnode\n  };\n}\n\n/*  */\n\nfunction ensureCtor (comp, base) {\n  if (\n    comp.__esModule ||\n    (hasSymbol && comp[Symbol.toStringTag] === 'Module')\n  ) {\n    comp = comp.default;\n  }\n  return isObject(comp)\n    ? base.extend(comp)\n    : comp\n}\n\nfunction createAsyncPlaceholder (\n  factory,\n  data,\n  context,\n  children,\n  tag\n) {\n  var node = createEmptyVNode();\n  node.asyncFactory = factory;\n  node.asyncMeta = { data: data, context: context, children: children, tag: tag };\n  return node\n}\n\nfunction resolveAsyncComponent (\n  factory,\n  baseCtor\n) {\n  if (isTrue(factory.error) && isDef(factory.errorComp)) {\n    return factory.errorComp\n  }\n\n  if (isDef(factory.resolved)) {\n    return factory.resolved\n  }\n\n  var owner = currentRenderingInstance;\n  if (owner && isDef(factory.owners) && factory.owners.indexOf(owner) === -1) {\n    // already pending\n    factory.owners.push(owner);\n  }\n\n  if (isTrue(factory.loading) && isDef(factory.loadingComp)) {\n    return factory.loadingComp\n  }\n\n  if (owner && !isDef(factory.owners)) {\n    var owners = factory.owners = [owner];\n    var sync = true;\n    var timerLoading = null;\n    var timerTimeout = null\n\n    ;(owner).$on('hook:destroyed', function () { return remove(owners, owner); });\n\n    var forceRender = function (renderCompleted) {\n      for (var i = 0, l = owners.length; i < l; i++) {\n        (owners[i]).$forceUpdate();\n      }\n\n      if (renderCompleted) {\n        owners.length = 0;\n        if (timerLoading !== null) {\n          clearTimeout(timerLoading);\n          timerLoading = null;\n        }\n        if (timerTimeout !== null) {\n          clearTimeout(timerTimeout);\n          timerTimeout = null;\n        }\n      }\n    };\n\n    var resolve = once(function (res) {\n      // cache resolved\n      factory.resolved = ensureCtor(res, baseCtor);\n      // invoke callbacks only if this is not a synchronous resolve\n      // (async resolves are shimmed as synchronous during SSR)\n      if (!sync) {\n        forceRender(true);\n      } else {\n        owners.length = 0;\n      }\n    });\n\n    var reject = once(function (reason) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Failed to resolve async component: \" + (String(factory)) +\n        (reason ? (\"\\nReason: \" + reason) : '')\n      );\n      if (isDef(factory.errorComp)) {\n        factory.error = true;\n        forceRender(true);\n      }\n    });\n\n    var res = factory(resolve, reject);\n\n    if (isObject(res)) {\n      if (isPromise(res)) {\n        // () => Promise\n        if (isUndef(factory.resolved)) {\n          res.then(resolve, reject);\n        }\n      } else if (isPromise(res.component)) {\n        res.component.then(resolve, reject);\n\n        if (isDef(res.error)) {\n          factory.errorComp = ensureCtor(res.error, baseCtor);\n        }\n\n        if (isDef(res.loading)) {\n          factory.loadingComp = ensureCtor(res.loading, baseCtor);\n          if (res.delay === 0) {\n            factory.loading = true;\n          } else {\n            timerLoading = setTimeout(function () {\n              timerLoading = null;\n              if (isUndef(factory.resolved) && isUndef(factory.error)) {\n                factory.loading = true;\n                forceRender(false);\n              }\n            }, res.delay || 200);\n          }\n        }\n\n        if (isDef(res.timeout)) {\n          timerTimeout = setTimeout(function () {\n            timerTimeout = null;\n            if (isUndef(factory.resolved)) {\n              reject(\n                process.env.NODE_ENV !== 'production'\n                  ? (\"timeout (\" + (res.timeout) + \"ms)\")\n                  : null\n              );\n            }\n          }, res.timeout);\n        }\n      }\n    }\n\n    sync = false;\n    // return in case resolved synchronously\n    return factory.loading\n      ? factory.loadingComp\n      : factory.resolved\n  }\n}\n\n/*  */\n\nfunction isAsyncPlaceholder (node) {\n  return node.isComment && node.asyncFactory\n}\n\n/*  */\n\nfunction getFirstComponentChild (children) {\n  if (Array.isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      var c = children[i];\n      if (isDef(c) && (isDef(c.componentOptions) || isAsyncPlaceholder(c))) {\n        return c\n      }\n    }\n  }\n}\n\n/*  */\n\n/*  */\n\nfunction initEvents (vm) {\n  vm._events = Object.create(null);\n  vm._hasHookEvent = false;\n  // init parent attached events\n  var listeners = vm.$options._parentListeners;\n  if (listeners) {\n    updateComponentListeners(vm, listeners);\n  }\n}\n\nvar target;\n\nfunction add (event, fn) {\n  target.$on(event, fn);\n}\n\nfunction remove$1 (event, fn) {\n  target.$off(event, fn);\n}\n\nfunction createOnceHandler (event, fn) {\n  var _target = target;\n  return function onceHandler () {\n    var res = fn.apply(null, arguments);\n    if (res !== null) {\n      _target.$off(event, onceHandler);\n    }\n  }\n}\n\nfunction updateComponentListeners (\n  vm,\n  listeners,\n  oldListeners\n) {\n  target = vm;\n  updateListeners(listeners, oldListeners || {}, add, remove$1, createOnceHandler, vm);\n  target = undefined;\n}\n\nfunction eventsMixin (Vue) {\n  var hookRE = /^hook:/;\n  Vue.prototype.$on = function (event, fn) {\n    var vm = this;\n    if (Array.isArray(event)) {\n      for (var i = 0, l = event.length; i < l; i++) {\n        vm.$on(event[i], fn);\n      }\n    } else {\n      (vm._events[event] || (vm._events[event] = [])).push(fn);\n      // optimize hook:event cost by using a boolean flag marked at registration\n      // instead of a hash lookup\n      if (hookRE.test(event)) {\n        vm._hasHookEvent = true;\n      }\n    }\n    return vm\n  };\n\n  Vue.prototype.$once = function (event, fn) {\n    var vm = this;\n    function on () {\n      vm.$off(event, on);\n      fn.apply(vm, arguments);\n    }\n    on.fn = fn;\n    vm.$on(event, on);\n    return vm\n  };\n\n  Vue.prototype.$off = function (event, fn) {\n    var vm = this;\n    // all\n    if (!arguments.length) {\n      vm._events = Object.create(null);\n      return vm\n    }\n    // array of events\n    if (Array.isArray(event)) {\n      for (var i$1 = 0, l = event.length; i$1 < l; i$1++) {\n        vm.$off(event[i$1], fn);\n      }\n      return vm\n    }\n    // specific event\n    var cbs = vm._events[event];\n    if (!cbs) {\n      return vm\n    }\n    if (!fn) {\n      vm._events[event] = null;\n      return vm\n    }\n    // specific handler\n    var cb;\n    var i = cbs.length;\n    while (i--) {\n      cb = cbs[i];\n      if (cb === fn || cb.fn === fn) {\n        cbs.splice(i, 1);\n        break\n      }\n    }\n    return vm\n  };\n\n  Vue.prototype.$emit = function (event) {\n    var vm = this;\n    if (process.env.NODE_ENV !== 'production') {\n      var lowerCaseEvent = event.toLowerCase();\n      if (lowerCaseEvent !== event && vm._events[lowerCaseEvent]) {\n        tip(\n          \"Event \\\"\" + lowerCaseEvent + \"\\\" is emitted in component \" +\n          (formatComponentName(vm)) + \" but the handler is registered for \\\"\" + event + \"\\\". \" +\n          \"Note that HTML attributes are case-insensitive and you cannot use \" +\n          \"v-on to listen to camelCase events when using in-DOM templates. \" +\n          \"You should probably use \\\"\" + (hyphenate(event)) + \"\\\" instead of \\\"\" + event + \"\\\".\"\n        );\n      }\n    }\n    var cbs = vm._events[event];\n    if (cbs) {\n      cbs = cbs.length > 1 ? toArray(cbs) : cbs;\n      var args = toArray(arguments, 1);\n      var info = \"event handler for \\\"\" + event + \"\\\"\";\n      for (var i = 0, l = cbs.length; i < l; i++) {\n        invokeWithErrorHandling(cbs[i], vm, args, vm, info);\n      }\n    }\n    return vm\n  };\n}\n\n/*  */\n\nvar activeInstance = null;\nvar isUpdatingChildComponent = false;\n\nfunction setActiveInstance(vm) {\n  var prevActiveInstance = activeInstance;\n  activeInstance = vm;\n  return function () {\n    activeInstance = prevActiveInstance;\n  }\n}\n\nfunction initLifecycle (vm) {\n  var options = vm.$options;\n\n  // locate first non-abstract parent\n  var parent = options.parent;\n  if (parent && !options.abstract) {\n    while (parent.$options.abstract && parent.$parent) {\n      parent = parent.$parent;\n    }\n    parent.$children.push(vm);\n  }\n\n  vm.$parent = parent;\n  vm.$root = parent ? parent.$root : vm;\n\n  vm.$children = [];\n  vm.$refs = {};\n\n  vm._watcher = null;\n  vm._inactive = null;\n  vm._directInactive = false;\n  vm._isMounted = false;\n  vm._isDestroyed = false;\n  vm._isBeingDestroyed = false;\n}\n\nfunction lifecycleMixin (Vue) {\n  Vue.prototype._update = function (vnode, hydrating) {\n    var vm = this;\n    var prevEl = vm.$el;\n    var prevVnode = vm._vnode;\n    var restoreActiveInstance = setActiveInstance(vm);\n    vm._vnode = vnode;\n    // Vue.prototype.__patch__ is injected in entry points\n    // based on the rendering backend used.\n    if (!prevVnode) {\n      // initial render\n      vm.$el = vm.__patch__(vm.$el, vnode, hydrating, false /* removeOnly */);\n    } else {\n      // updates\n      vm.$el = vm.__patch__(prevVnode, vnode);\n    }\n    restoreActiveInstance();\n    // update __vue__ reference\n    if (prevEl) {\n      prevEl.__vue__ = null;\n    }\n    if (vm.$el) {\n      vm.$el.__vue__ = vm;\n    }\n    // if parent is an HOC, update its $el as well\n    if (vm.$vnode && vm.$parent && vm.$vnode === vm.$parent._vnode) {\n      vm.$parent.$el = vm.$el;\n    }\n    // updated hook is called by the scheduler to ensure that children are\n    // updated in a parent's updated hook.\n  };\n\n  Vue.prototype.$forceUpdate = function () {\n    var vm = this;\n    if (vm._watcher) {\n      vm._watcher.update();\n    }\n  };\n\n  Vue.prototype.$destroy = function () {\n    var vm = this;\n    if (vm._isBeingDestroyed) {\n      return\n    }\n    callHook(vm, 'beforeDestroy');\n    vm._isBeingDestroyed = true;\n    // remove self from parent\n    var parent = vm.$parent;\n    if (parent && !parent._isBeingDestroyed && !vm.$options.abstract) {\n      remove(parent.$children, vm);\n    }\n    // teardown watchers\n    if (vm._watcher) {\n      vm._watcher.teardown();\n    }\n    var i = vm._watchers.length;\n    while (i--) {\n      vm._watchers[i].teardown();\n    }\n    // remove reference from data ob\n    // frozen object may not have observer.\n    if (vm._data.__ob__) {\n      vm._data.__ob__.vmCount--;\n    }\n    // call the last hook...\n    vm._isDestroyed = true;\n    // invoke destroy hooks on current rendered tree\n    vm.__patch__(vm._vnode, null);\n    // fire destroyed hook\n    callHook(vm, 'destroyed');\n    // turn off all instance listeners.\n    vm.$off();\n    // remove __vue__ reference\n    if (vm.$el) {\n      vm.$el.__vue__ = null;\n    }\n    // release circular reference (#6759)\n    if (vm.$vnode) {\n      vm.$vnode.parent = null;\n    }\n  };\n}\n\nfunction updateChildComponent (\n  vm,\n  propsData,\n  listeners,\n  parentVnode,\n  renderChildren\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    isUpdatingChildComponent = true;\n  }\n\n  // determine whether component has slot children\n  // we need to do this before overwriting $options._renderChildren.\n\n  // check if there are dynamic scopedSlots (hand-written or compiled but with\n  // dynamic slot names). Static scoped slots compiled from template has the\n  // \"$stable\" marker.\n  var newScopedSlots = parentVnode.data.scopedSlots;\n  var oldScopedSlots = vm.$scopedSlots;\n  var hasDynamicScopedSlot = !!(\n    (newScopedSlots && !newScopedSlots.$stable) ||\n    (oldScopedSlots !== emptyObject && !oldScopedSlots.$stable) ||\n    (newScopedSlots && vm.$scopedSlots.$key !== newScopedSlots.$key)\n  );\n\n  // Any static slot children from the parent may have changed during parent's\n  // update. Dynamic scoped slots may also have changed. In such cases, a forced\n  // update is necessary to ensure correctness.\n  var needsForceUpdate = !!(\n    renderChildren ||               // has new static slots\n    vm.$options._renderChildren ||  // has old static slots\n    hasDynamicScopedSlot\n  );\n\n  vm.$options._parentVnode = parentVnode;\n  vm.$vnode = parentVnode; // update vm's placeholder node without re-render\n\n  if (vm._vnode) { // update child tree's parent\n    vm._vnode.parent = parentVnode;\n  }\n  vm.$options._renderChildren = renderChildren;\n\n  // update $attrs and $listeners hash\n  // these are also reactive so they may trigger child update if the child\n  // used them during render\n  vm.$attrs = parentVnode.data.attrs || emptyObject;\n  vm.$listeners = listeners || emptyObject;\n\n  // update props\n  if (propsData && vm.$options.props) {\n    toggleObserving(false);\n    var props = vm._props;\n    var propKeys = vm.$options._propKeys || [];\n    for (var i = 0; i < propKeys.length; i++) {\n      var key = propKeys[i];\n      var propOptions = vm.$options.props; // wtf flow?\n      props[key] = validateProp(key, propOptions, propsData, vm);\n    }\n    toggleObserving(true);\n    // keep a copy of raw propsData\n    vm.$options.propsData = propsData;\n  }\n  \n  // fixed by xxxxxx update properties(mp runtime)\n  vm._$updateProperties && vm._$updateProperties(vm);\n  \n  // update listeners\n  listeners = listeners || emptyObject;\n  var oldListeners = vm.$options._parentListeners;\n  vm.$options._parentListeners = listeners;\n  updateComponentListeners(vm, listeners, oldListeners);\n\n  // resolve slots + force update if has children\n  if (needsForceUpdate) {\n    vm.$slots = resolveSlots(renderChildren, parentVnode.context);\n    vm.$forceUpdate();\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    isUpdatingChildComponent = false;\n  }\n}\n\nfunction isInInactiveTree (vm) {\n  while (vm && (vm = vm.$parent)) {\n    if (vm._inactive) { return true }\n  }\n  return false\n}\n\nfunction activateChildComponent (vm, direct) {\n  if (direct) {\n    vm._directInactive = false;\n    if (isInInactiveTree(vm)) {\n      return\n    }\n  } else if (vm._directInactive) {\n    return\n  }\n  if (vm._inactive || vm._inactive === null) {\n    vm._inactive = false;\n    for (var i = 0; i < vm.$children.length; i++) {\n      activateChildComponent(vm.$children[i]);\n    }\n    callHook(vm, 'activated');\n  }\n}\n\nfunction deactivateChildComponent (vm, direct) {\n  if (direct) {\n    vm._directInactive = true;\n    if (isInInactiveTree(vm)) {\n      return\n    }\n  }\n  if (!vm._inactive) {\n    vm._inactive = true;\n    for (var i = 0; i < vm.$children.length; i++) {\n      deactivateChildComponent(vm.$children[i]);\n    }\n    callHook(vm, 'deactivated');\n  }\n}\n\nfunction callHook (vm, hook) {\n  // #7573 disable dep collection when invoking lifecycle hooks\n  pushTarget();\n  var handlers = vm.$options[hook];\n  var info = hook + \" hook\";\n  if (handlers) {\n    for (var i = 0, j = handlers.length; i < j; i++) {\n      invokeWithErrorHandling(handlers[i], vm, null, vm, info);\n    }\n  }\n  if (vm._hasHookEvent) {\n    vm.$emit('hook:' + hook);\n  }\n  popTarget();\n}\n\n/*  */\n\nvar MAX_UPDATE_COUNT = 100;\n\nvar queue = [];\nvar activatedChildren = [];\nvar has = {};\nvar circular = {};\nvar waiting = false;\nvar flushing = false;\nvar index = 0;\n\n/**\n * Reset the scheduler's state.\n */\nfunction resetSchedulerState () {\n  index = queue.length = activatedChildren.length = 0;\n  has = {};\n  if (process.env.NODE_ENV !== 'production') {\n    circular = {};\n  }\n  waiting = flushing = false;\n}\n\n// Async edge case #6566 requires saving the timestamp when event listeners are\n// attached. However, calling performance.now() has a perf overhead especially\n// if the page has thousands of event listeners. Instead, we take a timestamp\n// every time the scheduler flushes and use that for all event listeners\n// attached during that flush.\nvar currentFlushTimestamp = 0;\n\n// Async edge case fix requires storing an event listener's attach timestamp.\nvar getNow = Date.now;\n\n// Determine what event timestamp the browser is using. Annoyingly, the\n// timestamp can either be hi-res (relative to page load) or low-res\n// (relative to UNIX epoch), so in order to compare time we have to use the\n// same timestamp type when saving the flush timestamp.\n// All IE versions use low-res event timestamps, and have problematic clock\n// implementations (#9632)\nif (inBrowser && !isIE) {\n  var performance = window.performance;\n  if (\n    performance &&\n    typeof performance.now === 'function' &&\n    getNow() > document.createEvent('Event').timeStamp\n  ) {\n    // if the event timestamp, although evaluated AFTER the Date.now(), is\n    // smaller than it, it means the event is using a hi-res timestamp,\n    // and we need to use the hi-res version for event listener timestamps as\n    // well.\n    getNow = function () { return performance.now(); };\n  }\n}\n\n/**\n * Flush both queues and run the watchers.\n */\nfunction flushSchedulerQueue () {\n  currentFlushTimestamp = getNow();\n  flushing = true;\n  var watcher, id;\n\n  // Sort queue before flush.\n  // This ensures that:\n  // 1. Components are updated from parent to child. (because parent is always\n  //    created before the child)\n  // 2. A component's user watchers are run before its render watcher (because\n  //    user watchers are created before the render watcher)\n  // 3. If a component is destroyed during a parent component's watcher run,\n  //    its watchers can be skipped.\n  queue.sort(function (a, b) { return a.id - b.id; });\n\n  // do not cache length because more watchers might be pushed\n  // as we run existing watchers\n  for (index = 0; index < queue.length; index++) {\n    watcher = queue[index];\n    if (watcher.before) {\n      watcher.before();\n    }\n    id = watcher.id;\n    has[id] = null;\n    watcher.run();\n    // in dev build, check and stop circular updates.\n    if (process.env.NODE_ENV !== 'production' && has[id] != null) {\n      circular[id] = (circular[id] || 0) + 1;\n      if (circular[id] > MAX_UPDATE_COUNT) {\n        warn(\n          'You may have an infinite update loop ' + (\n            watcher.user\n              ? (\"in watcher with expression \\\"\" + (watcher.expression) + \"\\\"\")\n              : \"in a component render function.\"\n          ),\n          watcher.vm\n        );\n        break\n      }\n    }\n  }\n\n  // keep copies of post queues before resetting state\n  var activatedQueue = activatedChildren.slice();\n  var updatedQueue = queue.slice();\n\n  resetSchedulerState();\n\n  // call component updated and activated hooks\n  callActivatedHooks(activatedQueue);\n  callUpdatedHooks(updatedQueue);\n\n  // devtool hook\n  /* istanbul ignore if */\n  if (devtools && config.devtools) {\n    devtools.emit('flush');\n  }\n}\n\nfunction callUpdatedHooks (queue) {\n  var i = queue.length;\n  while (i--) {\n    var watcher = queue[i];\n    var vm = watcher.vm;\n    if (vm._watcher === watcher && vm._isMounted && !vm._isDestroyed) {\n      callHook(vm, 'updated');\n    }\n  }\n}\n\n/**\n * Queue a kept-alive component that was activated during patch.\n * The queue will be processed after the entire tree has been patched.\n */\nfunction queueActivatedComponent (vm) {\n  // setting _inactive to false here so that a render function can\n  // rely on checking whether it's in an inactive tree (e.g. router-view)\n  vm._inactive = false;\n  activatedChildren.push(vm);\n}\n\nfunction callActivatedHooks (queue) {\n  for (var i = 0; i < queue.length; i++) {\n    queue[i]._inactive = true;\n    activateChildComponent(queue[i], true /* true */);\n  }\n}\n\n/**\n * Push a watcher into the watcher queue.\n * Jobs with duplicate IDs will be skipped unless it's\n * pushed when the queue is being flushed.\n */\nfunction queueWatcher (watcher) {\n  var id = watcher.id;\n  if (has[id] == null) {\n    has[id] = true;\n    if (!flushing) {\n      queue.push(watcher);\n    } else {\n      // if already flushing, splice the watcher based on its id\n      // if already past its id, it will be run next immediately.\n      var i = queue.length - 1;\n      while (i > index && queue[i].id > watcher.id) {\n        i--;\n      }\n      queue.splice(i + 1, 0, watcher);\n    }\n    // queue the flush\n    if (!waiting) {\n      waiting = true;\n\n      if (process.env.NODE_ENV !== 'production' && !config.async) {\n        flushSchedulerQueue();\n        return\n      }\n      nextTick(flushSchedulerQueue);\n    }\n  }\n}\n\n/*  */\n\n\n\nvar uid$2 = 0;\n\n/**\n * A watcher parses an expression, collects dependencies,\n * and fires callback when the expression value changes.\n * This is used for both the $watch() api and directives.\n */\nvar Watcher = function Watcher (\n  vm,\n  expOrFn,\n  cb,\n  options,\n  isRenderWatcher\n) {\n  this.vm = vm;\n  if (isRenderWatcher) {\n    vm._watcher = this;\n  }\n  vm._watchers.push(this);\n  // options\n  if (options) {\n    this.deep = !!options.deep;\n    this.user = !!options.user;\n    this.lazy = !!options.lazy;\n    this.sync = !!options.sync;\n    this.before = options.before;\n  } else {\n    this.deep = this.user = this.lazy = this.sync = false;\n  }\n  this.cb = cb;\n  this.id = ++uid$2; // uid for batching\n  this.active = true;\n  this.dirty = this.lazy; // for lazy watchers\n  this.deps = [];\n  this.newDeps = [];\n  this.depIds = new _Set();\n  this.newDepIds = new _Set();\n  this.expression = process.env.NODE_ENV !== 'production'\n    ? expOrFn.toString()\n    : '';\n  // parse expression for getter\n  if (typeof expOrFn === 'function') {\n    this.getter = expOrFn;\n  } else {\n    this.getter = parsePath(expOrFn);\n    if (!this.getter) {\n      this.getter = noop;\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Failed watching path: \\\"\" + expOrFn + \"\\\" \" +\n        'Watcher only accepts simple dot-delimited paths. ' +\n        'For full control, use a function instead.',\n        vm\n      );\n    }\n  }\n  this.value = this.lazy\n    ? undefined\n    : this.get();\n};\n\n/**\n * Evaluate the getter, and re-collect dependencies.\n */\nWatcher.prototype.get = function get () {\n  pushTarget(this);\n  var value;\n  var vm = this.vm;\n  try {\n    value = this.getter.call(vm, vm);\n  } catch (e) {\n    if (this.user) {\n      handleError(e, vm, (\"getter for watcher \\\"\" + (this.expression) + \"\\\"\"));\n    } else {\n      throw e\n    }\n  } finally {\n    // \"touch\" every property so they are all tracked as\n    // dependencies for deep watching\n    if (this.deep) {\n      traverse(value);\n    }\n    popTarget();\n    this.cleanupDeps();\n  }\n  return value\n};\n\n/**\n * Add a dependency to this directive.\n */\nWatcher.prototype.addDep = function addDep (dep) {\n  var id = dep.id;\n  if (!this.newDepIds.has(id)) {\n    this.newDepIds.add(id);\n    this.newDeps.push(dep);\n    if (!this.depIds.has(id)) {\n      dep.addSub(this);\n    }\n  }\n};\n\n/**\n * Clean up for dependency collection.\n */\nWatcher.prototype.cleanupDeps = function cleanupDeps () {\n  var i = this.deps.length;\n  while (i--) {\n    var dep = this.deps[i];\n    if (!this.newDepIds.has(dep.id)) {\n      dep.removeSub(this);\n    }\n  }\n  var tmp = this.depIds;\n  this.depIds = this.newDepIds;\n  this.newDepIds = tmp;\n  this.newDepIds.clear();\n  tmp = this.deps;\n  this.deps = this.newDeps;\n  this.newDeps = tmp;\n  this.newDeps.length = 0;\n};\n\n/**\n * Subscriber interface.\n * Will be called when a dependency changes.\n */\nWatcher.prototype.update = function update () {\n  /* istanbul ignore else */\n  if (this.lazy) {\n    this.dirty = true;\n  } else if (this.sync) {\n    this.run();\n  } else {\n    queueWatcher(this);\n  }\n};\n\n/**\n * Scheduler job interface.\n * Will be called by the scheduler.\n */\nWatcher.prototype.run = function run () {\n  if (this.active) {\n    var value = this.get();\n    if (\n      value !== this.value ||\n      // Deep watchers and watchers on Object/Arrays should fire even\n      // when the value is the same, because the value may\n      // have mutated.\n      isObject(value) ||\n      this.deep\n    ) {\n      // set new value\n      var oldValue = this.value;\n      this.value = value;\n      if (this.user) {\n        try {\n          this.cb.call(this.vm, value, oldValue);\n        } catch (e) {\n          handleError(e, this.vm, (\"callback for watcher \\\"\" + (this.expression) + \"\\\"\"));\n        }\n      } else {\n        this.cb.call(this.vm, value, oldValue);\n      }\n    }\n  }\n};\n\n/**\n * Evaluate the value of the watcher.\n * This only gets called for lazy watchers.\n */\nWatcher.prototype.evaluate = function evaluate () {\n  this.value = this.get();\n  this.dirty = false;\n};\n\n/**\n * Depend on all deps collected by this watcher.\n */\nWatcher.prototype.depend = function depend () {\n  var i = this.deps.length;\n  while (i--) {\n    this.deps[i].depend();\n  }\n};\n\n/**\n * Remove self from all dependencies' subscriber list.\n */\nWatcher.prototype.teardown = function teardown () {\n  if (this.active) {\n    // remove self from vm's watcher list\n    // this is a somewhat expensive operation so we skip it\n    // if the vm is being destroyed.\n    if (!this.vm._isBeingDestroyed) {\n      remove(this.vm._watchers, this);\n    }\n    var i = this.deps.length;\n    while (i--) {\n      this.deps[i].removeSub(this);\n    }\n    this.active = false;\n  }\n};\n\n/*  */\n\nvar sharedPropertyDefinition = {\n  enumerable: true,\n  configurable: true,\n  get: noop,\n  set: noop\n};\n\nfunction proxy (target, sourceKey, key) {\n  sharedPropertyDefinition.get = function proxyGetter () {\n    return this[sourceKey][key]\n  };\n  sharedPropertyDefinition.set = function proxySetter (val) {\n    this[sourceKey][key] = val;\n  };\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction initState (vm) {\n  vm._watchers = [];\n  var opts = vm.$options;\n  if (opts.props) { initProps(vm, opts.props); }\n  if (opts.methods) { initMethods(vm, opts.methods); }\n  if (opts.data) {\n    initData(vm);\n  } else {\n    observe(vm._data = {}, true /* asRootData */);\n  }\n  if (opts.computed) { initComputed(vm, opts.computed); }\n  if (opts.watch && opts.watch !== nativeWatch) {\n    initWatch(vm, opts.watch);\n  }\n}\n\nfunction initProps (vm, propsOptions) {\n  var propsData = vm.$options.propsData || {};\n  var props = vm._props = {};\n  // cache prop keys so that future props updates can iterate using Array\n  // instead of dynamic object key enumeration.\n  var keys = vm.$options._propKeys = [];\n  var isRoot = !vm.$parent;\n  // root instance props should be converted\n  if (!isRoot) {\n    toggleObserving(false);\n  }\n  var loop = function ( key ) {\n    keys.push(key);\n    var value = validateProp(key, propsOptions, propsData, vm);\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      var hyphenatedKey = hyphenate(key);\n      if (isReservedAttribute(hyphenatedKey) ||\n          config.isReservedAttr(hyphenatedKey)) {\n        warn(\n          (\"\\\"\" + hyphenatedKey + \"\\\" is a reserved attribute and cannot be used as component prop.\"),\n          vm\n        );\n      }\n      defineReactive$$1(props, key, value, function () {\n        if (!isRoot && !isUpdatingChildComponent) {\n          {\n            if(vm.mpHost === 'mp-baidu' || vm.mpHost === 'mp-kuaishou'){//百度、快手 observer 在 setData callback 之后触发，直接忽略该 warn\n                return\n            }\n            //fixed by xxxxxx __next_tick_pending,uni://form-field 时不告警\n            if(\n                key === 'value' && \n                Array.isArray(vm.$options.behaviors) &&\n                vm.$options.behaviors.indexOf('uni://form-field') !== -1\n              ){\n              return\n            }\n            if(vm._getFormData){\n              return\n            }\n            var $parent = vm.$parent;\n            while($parent){\n              if($parent.__next_tick_pending){\n                return  \n              }\n              $parent = $parent.$parent;\n            }\n          }\n          warn(\n            \"Avoid mutating a prop directly since the value will be \" +\n            \"overwritten whenever the parent component re-renders. \" +\n            \"Instead, use a data or computed property based on the prop's \" +\n            \"value. Prop being mutated: \\\"\" + key + \"\\\"\",\n            vm\n          );\n        }\n      });\n    } else {\n      defineReactive$$1(props, key, value);\n    }\n    // static props are already proxied on the component's prototype\n    // during Vue.extend(). We only need to proxy props defined at\n    // instantiation here.\n    if (!(key in vm)) {\n      proxy(vm, \"_props\", key);\n    }\n  };\n\n  for (var key in propsOptions) loop( key );\n  toggleObserving(true);\n}\n\nfunction initData (vm) {\n  var data = vm.$options.data;\n  data = vm._data = typeof data === 'function'\n    ? getData(data, vm)\n    : data || {};\n  if (!isPlainObject(data)) {\n    data = {};\n    process.env.NODE_ENV !== 'production' && warn(\n      'data functions should return an object:\\n' +\n      'https://vuejs.org/v2/guide/components.html#data-Must-Be-a-Function',\n      vm\n    );\n  }\n  // proxy data on instance\n  var keys = Object.keys(data);\n  var props = vm.$options.props;\n  var methods = vm.$options.methods;\n  var i = keys.length;\n  while (i--) {\n    var key = keys[i];\n    if (process.env.NODE_ENV !== 'production') {\n      if (methods && hasOwn(methods, key)) {\n        warn(\n          (\"Method \\\"\" + key + \"\\\" has already been defined as a data property.\"),\n          vm\n        );\n      }\n    }\n    if (props && hasOwn(props, key)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"The data property \\\"\" + key + \"\\\" is already declared as a prop. \" +\n        \"Use prop default value instead.\",\n        vm\n      );\n    } else if (!isReserved(key)) {\n      proxy(vm, \"_data\", key);\n    }\n  }\n  // observe data\n  observe(data, true /* asRootData */);\n}\n\nfunction getData (data, vm) {\n  // #7573 disable dep collection when invoking data getters\n  pushTarget();\n  try {\n    return data.call(vm, vm)\n  } catch (e) {\n    handleError(e, vm, \"data()\");\n    return {}\n  } finally {\n    popTarget();\n  }\n}\n\nvar computedWatcherOptions = { lazy: true };\n\nfunction initComputed (vm, computed) {\n  // $flow-disable-line\n  var watchers = vm._computedWatchers = Object.create(null);\n  // computed properties are just getters during SSR\n  var isSSR = isServerRendering();\n\n  for (var key in computed) {\n    var userDef = computed[key];\n    var getter = typeof userDef === 'function' ? userDef : userDef.get;\n    if (process.env.NODE_ENV !== 'production' && getter == null) {\n      warn(\n        (\"Getter is missing for computed property \\\"\" + key + \"\\\".\"),\n        vm\n      );\n    }\n\n    if (!isSSR) {\n      // create internal watcher for the computed property.\n      watchers[key] = new Watcher(\n        vm,\n        getter || noop,\n        noop,\n        computedWatcherOptions\n      );\n    }\n\n    // component-defined computed properties are already defined on the\n    // component prototype. We only need to define computed properties defined\n    // at instantiation here.\n    if (!(key in vm)) {\n      defineComputed(vm, key, userDef);\n    } else if (process.env.NODE_ENV !== 'production') {\n      if (key in vm.$data) {\n        warn((\"The computed property \\\"\" + key + \"\\\" is already defined in data.\"), vm);\n      } else if (vm.$options.props && key in vm.$options.props) {\n        warn((\"The computed property \\\"\" + key + \"\\\" is already defined as a prop.\"), vm);\n      }\n    }\n  }\n}\n\nfunction defineComputed (\n  target,\n  key,\n  userDef\n) {\n  var shouldCache = !isServerRendering();\n  if (typeof userDef === 'function') {\n    sharedPropertyDefinition.get = shouldCache\n      ? createComputedGetter(key)\n      : createGetterInvoker(userDef);\n    sharedPropertyDefinition.set = noop;\n  } else {\n    sharedPropertyDefinition.get = userDef.get\n      ? shouldCache && userDef.cache !== false\n        ? createComputedGetter(key)\n        : createGetterInvoker(userDef.get)\n      : noop;\n    sharedPropertyDefinition.set = userDef.set || noop;\n  }\n  if (process.env.NODE_ENV !== 'production' &&\n      sharedPropertyDefinition.set === noop) {\n    sharedPropertyDefinition.set = function () {\n      warn(\n        (\"Computed property \\\"\" + key + \"\\\" was assigned to but it has no setter.\"),\n        this\n      );\n    };\n  }\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction createComputedGetter (key) {\n  return function computedGetter () {\n    var watcher = this._computedWatchers && this._computedWatchers[key];\n    if (watcher) {\n      if (watcher.dirty) {\n        watcher.evaluate();\n      }\n      if (Dep.SharedObject.target) {// fixed by xxxxxx\n        watcher.depend();\n      }\n      return watcher.value\n    }\n  }\n}\n\nfunction createGetterInvoker(fn) {\n  return function computedGetter () {\n    return fn.call(this, this)\n  }\n}\n\nfunction initMethods (vm, methods) {\n  var props = vm.$options.props;\n  for (var key in methods) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof methods[key] !== 'function') {\n        warn(\n          \"Method \\\"\" + key + \"\\\" has type \\\"\" + (typeof methods[key]) + \"\\\" in the component definition. \" +\n          \"Did you reference the function correctly?\",\n          vm\n        );\n      }\n      if (props && hasOwn(props, key)) {\n        warn(\n          (\"Method \\\"\" + key + \"\\\" has already been defined as a prop.\"),\n          vm\n        );\n      }\n      if ((key in vm) && isReserved(key)) {\n        warn(\n          \"Method \\\"\" + key + \"\\\" conflicts with an existing Vue instance method. \" +\n          \"Avoid defining component methods that start with _ or $.\"\n        );\n      }\n    }\n    vm[key] = typeof methods[key] !== 'function' ? noop : bind(methods[key], vm);\n  }\n}\n\nfunction initWatch (vm, watch) {\n  for (var key in watch) {\n    var handler = watch[key];\n    if (Array.isArray(handler)) {\n      for (var i = 0; i < handler.length; i++) {\n        createWatcher(vm, key, handler[i]);\n      }\n    } else {\n      createWatcher(vm, key, handler);\n    }\n  }\n}\n\nfunction createWatcher (\n  vm,\n  expOrFn,\n  handler,\n  options\n) {\n  if (isPlainObject(handler)) {\n    options = handler;\n    handler = handler.handler;\n  }\n  if (typeof handler === 'string') {\n    handler = vm[handler];\n  }\n  return vm.$watch(expOrFn, handler, options)\n}\n\nfunction stateMixin (Vue) {\n  // flow somehow has problems with directly declared definition object\n  // when using Object.defineProperty, so we have to procedurally build up\n  // the object here.\n  var dataDef = {};\n  dataDef.get = function () { return this._data };\n  var propsDef = {};\n  propsDef.get = function () { return this._props };\n  if (process.env.NODE_ENV !== 'production') {\n    dataDef.set = function () {\n      warn(\n        'Avoid replacing instance root $data. ' +\n        'Use nested data properties instead.',\n        this\n      );\n    };\n    propsDef.set = function () {\n      warn(\"$props is readonly.\", this);\n    };\n  }\n  Object.defineProperty(Vue.prototype, '$data', dataDef);\n  Object.defineProperty(Vue.prototype, '$props', propsDef);\n\n  Vue.prototype.$set = set;\n  Vue.prototype.$delete = del;\n\n  Vue.prototype.$watch = function (\n    expOrFn,\n    cb,\n    options\n  ) {\n    var vm = this;\n    if (isPlainObject(cb)) {\n      return createWatcher(vm, expOrFn, cb, options)\n    }\n    options = options || {};\n    options.user = true;\n    var watcher = new Watcher(vm, expOrFn, cb, options);\n    if (options.immediate) {\n      try {\n        cb.call(vm, watcher.value);\n      } catch (error) {\n        handleError(error, vm, (\"callback for immediate watcher \\\"\" + (watcher.expression) + \"\\\"\"));\n      }\n    }\n    return function unwatchFn () {\n      watcher.teardown();\n    }\n  };\n}\n\n/*  */\n\nvar uid$3 = 0;\n\nfunction initMixin (Vue) {\n  Vue.prototype._init = function (options) {\n    var vm = this;\n    // a uid\n    vm._uid = uid$3++;\n\n    var startTag, endTag;\n    /* istanbul ignore if */\n    if (process.env.NODE_ENV !== 'production' && config.performance && mark) {\n      startTag = \"vue-perf-start:\" + (vm._uid);\n      endTag = \"vue-perf-end:\" + (vm._uid);\n      mark(startTag);\n    }\n\n    // a flag to avoid this being observed\n    vm._isVue = true;\n    // merge options\n    if (options && options._isComponent) {\n      // optimize internal component instantiation\n      // since dynamic options merging is pretty slow, and none of the\n      // internal component options needs special treatment.\n      initInternalComponent(vm, options);\n    } else {\n      vm.$options = mergeOptions(\n        resolveConstructorOptions(vm.constructor),\n        options || {},\n        vm\n      );\n    }\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      initProxy(vm);\n    } else {\n      vm._renderProxy = vm;\n    }\n    // expose real self\n    vm._self = vm;\n    initLifecycle(vm);\n    initEvents(vm);\n    initRender(vm);\n    callHook(vm, 'beforeCreate');\n    !vm._$fallback && initInjections(vm); // resolve injections before data/props  \n    initState(vm);\n    !vm._$fallback && initProvide(vm); // resolve provide after data/props\n    !vm._$fallback && callHook(vm, 'created');      \n\n    /* istanbul ignore if */\n    if (process.env.NODE_ENV !== 'production' && config.performance && mark) {\n      vm._name = formatComponentName(vm, false);\n      mark(endTag);\n      measure((\"vue \" + (vm._name) + \" init\"), startTag, endTag);\n    }\n\n    if (vm.$options.el) {\n      vm.$mount(vm.$options.el);\n    }\n  };\n}\n\nfunction initInternalComponent (vm, options) {\n  var opts = vm.$options = Object.create(vm.constructor.options);\n  // doing this because it's faster than dynamic enumeration.\n  var parentVnode = options._parentVnode;\n  opts.parent = options.parent;\n  opts._parentVnode = parentVnode;\n\n  var vnodeComponentOptions = parentVnode.componentOptions;\n  opts.propsData = vnodeComponentOptions.propsData;\n  opts._parentListeners = vnodeComponentOptions.listeners;\n  opts._renderChildren = vnodeComponentOptions.children;\n  opts._componentTag = vnodeComponentOptions.tag;\n\n  if (options.render) {\n    opts.render = options.render;\n    opts.staticRenderFns = options.staticRenderFns;\n  }\n}\n\nfunction resolveConstructorOptions (Ctor) {\n  var options = Ctor.options;\n  if (Ctor.super) {\n    var superOptions = resolveConstructorOptions(Ctor.super);\n    var cachedSuperOptions = Ctor.superOptions;\n    if (superOptions !== cachedSuperOptions) {\n      // super option changed,\n      // need to resolve new options.\n      Ctor.superOptions = superOptions;\n      // check if there are any late-modified/attached options (#4976)\n      var modifiedOptions = resolveModifiedOptions(Ctor);\n      // update base extend options\n      if (modifiedOptions) {\n        extend(Ctor.extendOptions, modifiedOptions);\n      }\n      options = Ctor.options = mergeOptions(superOptions, Ctor.extendOptions);\n      if (options.name) {\n        options.components[options.name] = Ctor;\n      }\n    }\n  }\n  return options\n}\n\nfunction resolveModifiedOptions (Ctor) {\n  var modified;\n  var latest = Ctor.options;\n  var sealed = Ctor.sealedOptions;\n  for (var key in latest) {\n    if (latest[key] !== sealed[key]) {\n      if (!modified) { modified = {}; }\n      modified[key] = latest[key];\n    }\n  }\n  return modified\n}\n\nfunction Vue (options) {\n  if (process.env.NODE_ENV !== 'production' &&\n    !(this instanceof Vue)\n  ) {\n    warn('Vue is a constructor and should be called with the `new` keyword');\n  }\n  this._init(options);\n}\n\ninitMixin(Vue);\nstateMixin(Vue);\neventsMixin(Vue);\nlifecycleMixin(Vue);\nrenderMixin(Vue);\n\n/*  */\n\nfunction initUse (Vue) {\n  Vue.use = function (plugin) {\n    var installedPlugins = (this._installedPlugins || (this._installedPlugins = []));\n    if (installedPlugins.indexOf(plugin) > -1) {\n      return this\n    }\n\n    // additional parameters\n    var args = toArray(arguments, 1);\n    args.unshift(this);\n    if (typeof plugin.install === 'function') {\n      plugin.install.apply(plugin, args);\n    } else if (typeof plugin === 'function') {\n      plugin.apply(null, args);\n    }\n    installedPlugins.push(plugin);\n    return this\n  };\n}\n\n/*  */\n\nfunction initMixin$1 (Vue) {\n  Vue.mixin = function (mixin) {\n    this.options = mergeOptions(this.options, mixin);\n    return this\n  };\n}\n\n/*  */\n\nfunction initExtend (Vue) {\n  /**\n   * Each instance constructor, including Vue, has a unique\n   * cid. This enables us to create wrapped \"child\n   * constructors\" for prototypal inheritance and cache them.\n   */\n  Vue.cid = 0;\n  var cid = 1;\n\n  /**\n   * Class inheritance\n   */\n  Vue.extend = function (extendOptions) {\n    extendOptions = extendOptions || {};\n    var Super = this;\n    var SuperId = Super.cid;\n    var cachedCtors = extendOptions._Ctor || (extendOptions._Ctor = {});\n    if (cachedCtors[SuperId]) {\n      return cachedCtors[SuperId]\n    }\n\n    var name = extendOptions.name || Super.options.name;\n    if (process.env.NODE_ENV !== 'production' && name) {\n      validateComponentName(name);\n    }\n\n    var Sub = function VueComponent (options) {\n      this._init(options);\n    };\n    Sub.prototype = Object.create(Super.prototype);\n    Sub.prototype.constructor = Sub;\n    Sub.cid = cid++;\n    Sub.options = mergeOptions(\n      Super.options,\n      extendOptions\n    );\n    Sub['super'] = Super;\n\n    // For props and computed properties, we define the proxy getters on\n    // the Vue instances at extension time, on the extended prototype. This\n    // avoids Object.defineProperty calls for each instance created.\n    if (Sub.options.props) {\n      initProps$1(Sub);\n    }\n    if (Sub.options.computed) {\n      initComputed$1(Sub);\n    }\n\n    // allow further extension/mixin/plugin usage\n    Sub.extend = Super.extend;\n    Sub.mixin = Super.mixin;\n    Sub.use = Super.use;\n\n    // create asset registers, so extended classes\n    // can have their private assets too.\n    ASSET_TYPES.forEach(function (type) {\n      Sub[type] = Super[type];\n    });\n    // enable recursive self-lookup\n    if (name) {\n      Sub.options.components[name] = Sub;\n    }\n\n    // keep a reference to the super options at extension time.\n    // later at instantiation we can check if Super's options have\n    // been updated.\n    Sub.superOptions = Super.options;\n    Sub.extendOptions = extendOptions;\n    Sub.sealedOptions = extend({}, Sub.options);\n\n    // cache constructor\n    cachedCtors[SuperId] = Sub;\n    return Sub\n  };\n}\n\nfunction initProps$1 (Comp) {\n  var props = Comp.options.props;\n  for (var key in props) {\n    proxy(Comp.prototype, \"_props\", key);\n  }\n}\n\nfunction initComputed$1 (Comp) {\n  var computed = Comp.options.computed;\n  for (var key in computed) {\n    defineComputed(Comp.prototype, key, computed[key]);\n  }\n}\n\n/*  */\n\nfunction initAssetRegisters (Vue) {\n  /**\n   * Create asset registration methods.\n   */\n  ASSET_TYPES.forEach(function (type) {\n    Vue[type] = function (\n      id,\n      definition\n    ) {\n      if (!definition) {\n        return this.options[type + 's'][id]\n      } else {\n        /* istanbul ignore if */\n        if (process.env.NODE_ENV !== 'production' && type === 'component') {\n          validateComponentName(id);\n        }\n        if (type === 'component' && isPlainObject(definition)) {\n          definition.name = definition.name || id;\n          definition = this.options._base.extend(definition);\n        }\n        if (type === 'directive' && typeof definition === 'function') {\n          definition = { bind: definition, update: definition };\n        }\n        this.options[type + 's'][id] = definition;\n        return definition\n      }\n    };\n  });\n}\n\n/*  */\n\n\n\nfunction getComponentName (opts) {\n  return opts && (opts.Ctor.options.name || opts.tag)\n}\n\nfunction matches (pattern, name) {\n  if (Array.isArray(pattern)) {\n    return pattern.indexOf(name) > -1\n  } else if (typeof pattern === 'string') {\n    return pattern.split(',').indexOf(name) > -1\n  } else if (isRegExp(pattern)) {\n    return pattern.test(name)\n  }\n  /* istanbul ignore next */\n  return false\n}\n\nfunction pruneCache (keepAliveInstance, filter) {\n  var cache = keepAliveInstance.cache;\n  var keys = keepAliveInstance.keys;\n  var _vnode = keepAliveInstance._vnode;\n  for (var key in cache) {\n    var cachedNode = cache[key];\n    if (cachedNode) {\n      var name = getComponentName(cachedNode.componentOptions);\n      if (name && !filter(name)) {\n        pruneCacheEntry(cache, key, keys, _vnode);\n      }\n    }\n  }\n}\n\nfunction pruneCacheEntry (\n  cache,\n  key,\n  keys,\n  current\n) {\n  var cached$$1 = cache[key];\n  if (cached$$1 && (!current || cached$$1.tag !== current.tag)) {\n    cached$$1.componentInstance.$destroy();\n  }\n  cache[key] = null;\n  remove(keys, key);\n}\n\nvar patternTypes = [String, RegExp, Array];\n\nvar KeepAlive = {\n  name: 'keep-alive',\n  abstract: true,\n\n  props: {\n    include: patternTypes,\n    exclude: patternTypes,\n    max: [String, Number]\n  },\n\n  created: function created () {\n    this.cache = Object.create(null);\n    this.keys = [];\n  },\n\n  destroyed: function destroyed () {\n    for (var key in this.cache) {\n      pruneCacheEntry(this.cache, key, this.keys);\n    }\n  },\n\n  mounted: function mounted () {\n    var this$1 = this;\n\n    this.$watch('include', function (val) {\n      pruneCache(this$1, function (name) { return matches(val, name); });\n    });\n    this.$watch('exclude', function (val) {\n      pruneCache(this$1, function (name) { return !matches(val, name); });\n    });\n  },\n\n  render: function render () {\n    var slot = this.$slots.default;\n    var vnode = getFirstComponentChild(slot);\n    var componentOptions = vnode && vnode.componentOptions;\n    if (componentOptions) {\n      // check pattern\n      var name = getComponentName(componentOptions);\n      var ref = this;\n      var include = ref.include;\n      var exclude = ref.exclude;\n      if (\n        // not included\n        (include && (!name || !matches(include, name))) ||\n        // excluded\n        (exclude && name && matches(exclude, name))\n      ) {\n        return vnode\n      }\n\n      var ref$1 = this;\n      var cache = ref$1.cache;\n      var keys = ref$1.keys;\n      var key = vnode.key == null\n        // same constructor may get registered as different local components\n        // so cid alone is not enough (#3269)\n        ? componentOptions.Ctor.cid + (componentOptions.tag ? (\"::\" + (componentOptions.tag)) : '')\n        : vnode.key;\n      if (cache[key]) {\n        vnode.componentInstance = cache[key].componentInstance;\n        // make current key freshest\n        remove(keys, key);\n        keys.push(key);\n      } else {\n        cache[key] = vnode;\n        keys.push(key);\n        // prune oldest entry\n        if (this.max && keys.length > parseInt(this.max)) {\n          pruneCacheEntry(cache, keys[0], keys, this._vnode);\n        }\n      }\n\n      vnode.data.keepAlive = true;\n    }\n    return vnode || (slot && slot[0])\n  }\n};\n\nvar builtInComponents = {\n  KeepAlive: KeepAlive\n};\n\n/*  */\n\nfunction initGlobalAPI (Vue) {\n  // config\n  var configDef = {};\n  configDef.get = function () { return config; };\n  if (process.env.NODE_ENV !== 'production') {\n    configDef.set = function () {\n      warn(\n        'Do not replace the Vue.config object, set individual fields instead.'\n      );\n    };\n  }\n  Object.defineProperty(Vue, 'config', configDef);\n\n  // exposed util methods.\n  // NOTE: these are not considered part of the public API - avoid relying on\n  // them unless you are aware of the risk.\n  Vue.util = {\n    warn: warn,\n    extend: extend,\n    mergeOptions: mergeOptions,\n    defineReactive: defineReactive$$1\n  };\n\n  Vue.set = set;\n  Vue.delete = del;\n  Vue.nextTick = nextTick;\n\n  // 2.6 explicit observable API\n  Vue.observable = function (obj) {\n    observe(obj);\n    return obj\n  };\n\n  Vue.options = Object.create(null);\n  ASSET_TYPES.forEach(function (type) {\n    Vue.options[type + 's'] = Object.create(null);\n  });\n\n  // this is used to identify the \"base\" constructor to extend all plain-object\n  // components with in Weex's multi-instance scenarios.\n  Vue.options._base = Vue;\n\n  extend(Vue.options.components, builtInComponents);\n\n  initUse(Vue);\n  initMixin$1(Vue);\n  initExtend(Vue);\n  initAssetRegisters(Vue);\n}\n\ninitGlobalAPI(Vue);\n\nObject.defineProperty(Vue.prototype, '$isServer', {\n  get: isServerRendering\n});\n\nObject.defineProperty(Vue.prototype, '$ssrContext', {\n  get: function get () {\n    /* istanbul ignore next */\n    return this.$vnode && this.$vnode.ssrContext\n  }\n});\n\n// expose FunctionalRenderContext for ssr runtime helper installation\nObject.defineProperty(Vue, 'FunctionalRenderContext', {\n  value: FunctionalRenderContext\n});\n\nVue.version = '2.6.11';\n\n/**\n * https://raw.githubusercontent.com/Tencent/westore/master/packages/westore/utils/diff.js\n */\nvar ARRAYTYPE = '[object Array]';\nvar OBJECTTYPE = '[object Object]';\n// const FUNCTIONTYPE = '[object Function]'\n\nfunction diff(current, pre) {\n    var result = {};\n    syncKeys(current, pre);\n    _diff(current, pre, '', result);\n    return result\n}\n\nfunction syncKeys(current, pre) {\n    if (current === pre) { return }\n    var rootCurrentType = type(current);\n    var rootPreType = type(pre);\n    if (rootCurrentType == OBJECTTYPE && rootPreType == OBJECTTYPE) {\n        if(Object.keys(current).length >= Object.keys(pre).length){\n            for (var key in pre) {\n                var currentValue = current[key];\n                if (currentValue === undefined) {\n                    current[key] = null;\n                } else {\n                    syncKeys(currentValue, pre[key]);\n                }\n            }\n        }\n    } else if (rootCurrentType == ARRAYTYPE && rootPreType == ARRAYTYPE) {\n        if (current.length >= pre.length) {\n            pre.forEach(function (item, index) {\n                syncKeys(current[index], item);\n            });\n        }\n    }\n}\n\nfunction _diff(current, pre, path, result) {\n    if (current === pre) { return }\n    var rootCurrentType = type(current);\n    var rootPreType = type(pre);\n    if (rootCurrentType == OBJECTTYPE) {\n        if (rootPreType != OBJECTTYPE || Object.keys(current).length < Object.keys(pre).length) {\n            setResult(result, path, current);\n        } else {\n            var loop = function ( key ) {\n                var currentValue = current[key];\n                var preValue = pre[key];\n                var currentType = type(currentValue);\n                var preType = type(preValue);\n                if (currentType != ARRAYTYPE && currentType != OBJECTTYPE) {\n                    // NOTE 此处将 != 修改为 !==。涉及地方太多恐怕测试不到，如果出现数据对比问题，将其修改回来。\n                    if (currentValue !== pre[key]) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    }\n                } else if (currentType == ARRAYTYPE) {\n                    if (preType != ARRAYTYPE) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    } else {\n                        if (currentValue.length < preValue.length) {\n                            setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                        } else {\n                            currentValue.forEach(function (item, index) {\n                                _diff(item, preValue[index], (path == '' ? '' : path + \".\") + key + '[' + index + ']', result);\n                            });\n                        }\n                    }\n                } else if (currentType == OBJECTTYPE) {\n                    if (preType != OBJECTTYPE || Object.keys(currentValue).length < Object.keys(preValue).length) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    } else {\n                        for (var subKey in currentValue) {\n                            _diff(currentValue[subKey], preValue[subKey], (path == '' ? '' : path + \".\") + key + '.' + subKey, result);\n                        }\n                    }\n                }\n            };\n\n            for (var key in current) loop( key );\n        }\n    } else if (rootCurrentType == ARRAYTYPE) {\n        if (rootPreType != ARRAYTYPE) {\n            setResult(result, path, current);\n        } else {\n            if (current.length < pre.length) {\n                setResult(result, path, current);\n            } else {\n                current.forEach(function (item, index) {\n                    _diff(item, pre[index], path + '[' + index + ']', result);\n                });\n            }\n        }\n    } else {\n        setResult(result, path, current);\n    }\n}\n\nfunction setResult(result, k, v) {\n    // if (type(v) != FUNCTIONTYPE) {\n        result[k] = v;\n    // }\n}\n\nfunction type(obj) {\n    return Object.prototype.toString.call(obj)\n}\n\n/*  */\n\nfunction flushCallbacks$1(vm) {\n    if (vm.__next_tick_callbacks && vm.__next_tick_callbacks.length) {\n        if (process.env.VUE_APP_DEBUG) {\n            var mpInstance = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + vm._uid +\n                ']:flushCallbacks[' + vm.__next_tick_callbacks.length + ']');\n        }\n        var copies = vm.__next_tick_callbacks.slice(0);\n        vm.__next_tick_callbacks.length = 0;\n        for (var i = 0; i < copies.length; i++) {\n            copies[i]();\n        }\n    }\n}\n\nfunction hasRenderWatcher(vm) {\n    return queue.find(function (watcher) { return vm._watcher === watcher; })\n}\n\nfunction nextTick$1(vm, cb) {\n    //1.nextTick 之前 已 setData 且 setData 还未回调完成\n    //2.nextTick 之前存在 render watcher\n    if (!vm.__next_tick_pending && !hasRenderWatcher(vm)) {\n        if(process.env.VUE_APP_DEBUG){\n            var mpInstance = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + vm._uid +\n                ']:nextVueTick');\n        }\n        return nextTick(cb, vm)\n    }else{\n        if(process.env.VUE_APP_DEBUG){\n            var mpInstance$1 = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance$1.is || mpInstance$1.route) + '][' + vm._uid +\n                ']:nextMPTick');\n        }\n    }\n    var _resolve;\n    if (!vm.__next_tick_callbacks) {\n        vm.__next_tick_callbacks = [];\n    }\n    vm.__next_tick_callbacks.push(function () {\n        if (cb) {\n            try {\n                cb.call(vm);\n            } catch (e) {\n                handleError(e, vm, 'nextTick');\n            }\n        } else if (_resolve) {\n            _resolve(vm);\n        }\n    });\n    // $flow-disable-line\n    if (!cb && typeof Promise !== 'undefined') {\n        return new Promise(function (resolve) {\n            _resolve = resolve;\n        })\n    }\n}\n\n/*  */\n\nfunction cloneWithData(vm) {\n  // 确保当前 vm 所有数据被同步\n  var ret = Object.create(null);\n  var dataKeys = [].concat(\n    Object.keys(vm._data || {}),\n    Object.keys(vm._computedWatchers || {}));\n\n  dataKeys.reduce(function(ret, key) {\n    ret[key] = vm[key];\n    return ret\n  }, ret);\n\n  // vue-composition-api\n  var compositionApiState = vm.__composition_api_state__ || vm.__secret_vfa_state__;\n  var rawBindings = compositionApiState && compositionApiState.rawBindings;\n  if (rawBindings) {\n    Object.keys(rawBindings).forEach(function (key) {\n      ret[key] = vm[key];\n    });\n  }\n\n  //TODO 需要把无用数据处理掉，比如 list=>l0 则 list 需要移除，否则多传输一份数据\n  Object.assign(ret, vm.$mp.data || {});\n  if (\n    Array.isArray(vm.$options.behaviors) &&\n    vm.$options.behaviors.indexOf('uni://form-field') !== -1\n  ) { //form-field\n    ret['name'] = vm.name;\n    ret['value'] = vm.value;\n  }\n\n  return JSON.parse(JSON.stringify(ret))\n}\n\nvar patch = function(oldVnode, vnode) {\n  var this$1 = this;\n\n  if (vnode === null) { //destroy\n    return\n  }\n  if (this.mpType === 'page' || this.mpType === 'component') {\n    var mpInstance = this.$scope;\n    var data = Object.create(null);\n    try {\n      data = cloneWithData(this);\n    } catch (err) {\n      console.error(err);\n    }\n    data.__webviewId__ = mpInstance.data.__webviewId__;\n    var mpData = Object.create(null);\n    Object.keys(data).forEach(function (key) { //仅同步 data 中有的数据\n      mpData[key] = mpInstance.data[key];\n    });\n    var diffData = this.$shouldDiffData === false ? data : diff(data, mpData);\n    if (Object.keys(diffData).length) {\n      if (process.env.VUE_APP_DEBUG) {\n        console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + this._uid +\n          ']差量更新',\n          JSON.stringify(diffData));\n      }\n      this.__next_tick_pending = true;\n      mpInstance.setData(diffData, function () {\n        this$1.__next_tick_pending = false;\n        flushCallbacks$1(this$1);\n      });\n    } else {\n      flushCallbacks$1(this);\n    }\n  }\n};\n\n/*  */\n\nfunction createEmptyRender() {\n\n}\n\nfunction mountComponent$1(\n  vm,\n  el,\n  hydrating\n) {\n  if (!vm.mpType) {//main.js 中的 new Vue\n    return vm\n  }\n  if (vm.mpType === 'app') {\n    vm.$options.render = createEmptyRender;\n  }\n  if (!vm.$options.render) {\n    vm.$options.render = createEmptyRender;\n    if (process.env.NODE_ENV !== 'production') {\n      /* istanbul ignore if */\n      if ((vm.$options.template && vm.$options.template.charAt(0) !== '#') ||\n        vm.$options.el || el) {\n        warn(\n          'You are using the runtime-only build of Vue where the template ' +\n          'compiler is not available. Either pre-compile the templates into ' +\n          'render functions, or use the compiler-included build.',\n          vm\n        );\n      } else {\n        warn(\n          'Failed to mount component: template or render function not defined.',\n          vm\n        );\n      }\n    }\n  }\n  \n  !vm._$fallback && callHook(vm, 'beforeMount');\n\n  var updateComponent = function () {\n    vm._update(vm._render(), hydrating);\n  };\n\n  // we set this to vm._watcher inside the watcher's constructor\n  // since the watcher's initial patch may call $forceUpdate (e.g. inside child\n  // component's mounted hook), which relies on vm._watcher being already defined\n  new Watcher(vm, updateComponent, noop, {\n    before: function before() {\n      if (vm._isMounted && !vm._isDestroyed) {\n        callHook(vm, 'beforeUpdate');\n      }\n    }\n  }, true /* isRenderWatcher */);\n  hydrating = false;\n  return vm\n}\n\n/*  */\n\nfunction renderClass (\n  staticClass,\n  dynamicClass\n) {\n  if (isDef(staticClass) || isDef(dynamicClass)) {\n    return concat(staticClass, stringifyClass(dynamicClass))\n  }\n  /* istanbul ignore next */\n  return ''\n}\n\nfunction concat (a, b) {\n  return a ? b ? (a + ' ' + b) : a : (b || '')\n}\n\nfunction stringifyClass (value) {\n  if (Array.isArray(value)) {\n    return stringifyArray(value)\n  }\n  if (isObject(value)) {\n    return stringifyObject(value)\n  }\n  if (typeof value === 'string') {\n    return value\n  }\n  /* istanbul ignore next */\n  return ''\n}\n\nfunction stringifyArray (value) {\n  var res = '';\n  var stringified;\n  for (var i = 0, l = value.length; i < l; i++) {\n    if (isDef(stringified = stringifyClass(value[i])) && stringified !== '') {\n      if (res) { res += ' '; }\n      res += stringified;\n    }\n  }\n  return res\n}\n\nfunction stringifyObject (value) {\n  var res = '';\n  for (var key in value) {\n    if (value[key]) {\n      if (res) { res += ' '; }\n      res += key;\n    }\n  }\n  return res\n}\n\n/*  */\n\nvar parseStyleText = cached(function (cssText) {\n  var res = {};\n  var listDelimiter = /;(?![^(]*\\))/g;\n  var propertyDelimiter = /:(.+)/;\n  cssText.split(listDelimiter).forEach(function (item) {\n    if (item) {\n      var tmp = item.split(propertyDelimiter);\n      tmp.length > 1 && (res[tmp[0].trim()] = tmp[1].trim());\n    }\n  });\n  return res\n});\n\n// normalize possible array / string values into Object\nfunction normalizeStyleBinding (bindingStyle) {\n  if (Array.isArray(bindingStyle)) {\n    return toObject(bindingStyle)\n  }\n  if (typeof bindingStyle === 'string') {\n    return parseStyleText(bindingStyle)\n  }\n  return bindingStyle\n}\n\n/*  */\n\nvar MP_METHODS = ['createSelectorQuery', 'createIntersectionObserver', 'selectAllComponents', 'selectComponent'];\n\nfunction getTarget(obj, path) {\n  var parts = path.split('.');\n  var key = parts[0];\n  if (key.indexOf('__$n') === 0) { //number index\n    key = parseInt(key.replace('__$n', ''));\n  }\n  if (parts.length === 1) {\n    return obj[key]\n  }\n  return getTarget(obj[key], parts.slice(1).join('.'))\n}\n\nfunction internalMixin(Vue) {\n\n  Vue.config.errorHandler = function(err, vm, info) {\n    Vue.util.warn((\"Error in \" + info + \": \\\"\" + (err.toString()) + \"\\\"\"), vm);\n    console.error(err);\n    /* eslint-disable no-undef */\n    var app = typeof getApp === 'function' && getApp();\n    if (app && app.onError) {\n      app.onError(err);\n    }\n  };\n\n  var oldEmit = Vue.prototype.$emit;\n\n  Vue.prototype.$emit = function(event) {\n    if (this.$scope && event) {\n      this.$scope['triggerEvent'](event, {\n        __args__: toArray(arguments, 1)\n      });\n    }\n    return oldEmit.apply(this, arguments)\n  };\n\n  Vue.prototype.$nextTick = function(fn) {\n    return nextTick$1(this, fn)\n  };\n\n  MP_METHODS.forEach(function (method) {\n    Vue.prototype[method] = function(args) {\n      if (this.$scope && this.$scope[method]) {\n        return this.$scope[method](args)\n      }\n      // mp-alipay\n      if (typeof my === 'undefined') {\n        return\n      }\n      if (method === 'createSelectorQuery') {\n        /* eslint-disable no-undef */\n        return my.createSelectorQuery(args)\n      } else if (method === 'createIntersectionObserver') {\n        /* eslint-disable no-undef */\n        return my.createIntersectionObserver(args)\n      }\n      // TODO mp-alipay 暂不支持 selectAllComponents,selectComponent\n    };\n  });\n\n  Vue.prototype.__init_provide = initProvide;\n\n  Vue.prototype.__init_injections = initInjections;\n\n  Vue.prototype.__call_hook = function(hook, args) {\n    var vm = this;\n    // #7573 disable dep collection when invoking lifecycle hooks\n    pushTarget();\n    var handlers = vm.$options[hook];\n    var info = hook + \" hook\";\n    var ret;\n    if (handlers) {\n      for (var i = 0, j = handlers.length; i < j; i++) {\n        ret = invokeWithErrorHandling(handlers[i], vm, args ? [args] : null, vm, info);\n      }\n    }\n    if (vm._hasHookEvent) {\n      vm.$emit('hook:' + hook, args);\n    }\n    popTarget();\n    return ret\n  };\n\n  Vue.prototype.__set_model = function(target, key, value, modifiers) {\n    if (Array.isArray(modifiers)) {\n      if (modifiers.indexOf('trim') !== -1) {\n        value = value.trim();\n      }\n      if (modifiers.indexOf('number') !== -1) {\n        value = this._n(value);\n      }\n    }\n    if (!target) {\n      target = this;\n    }\n    target[key] = value;\n  };\n\n  Vue.prototype.__set_sync = function(target, key, value) {\n    if (!target) {\n      target = this;\n    }\n    target[key] = value;\n  };\n\n  Vue.prototype.__get_orig = function(item) {\n    if (isPlainObject(item)) {\n      return item['$orig'] || item\n    }\n    return item\n  };\n\n  Vue.prototype.__get_value = function(dataPath, target) {\n    return getTarget(target || this, dataPath)\n  };\n\n\n  Vue.prototype.__get_class = function(dynamicClass, staticClass) {\n    return renderClass(staticClass, dynamicClass)\n  };\n\n  Vue.prototype.__get_style = function(dynamicStyle, staticStyle) {\n    if (!dynamicStyle && !staticStyle) {\n      return ''\n    }\n    var dynamicStyleObj = normalizeStyleBinding(dynamicStyle);\n    var styleObj = staticStyle ? extend(staticStyle, dynamicStyleObj) : dynamicStyleObj;\n    return Object.keys(styleObj).map(function (name) { return ((hyphenate(name)) + \":\" + (styleObj[name])); }).join(';')\n  };\n\n  Vue.prototype.__map = function(val, iteratee) {\n    //TODO 暂不考虑 string\n    var ret, i, l, keys, key;\n    if (Array.isArray(val)) {\n      ret = new Array(val.length);\n      for (i = 0, l = val.length; i < l; i++) {\n        ret[i] = iteratee(val[i], i);\n      }\n      return ret\n    } else if (isObject(val)) {\n      keys = Object.keys(val);\n      ret = Object.create(null);\n      for (i = 0, l = keys.length; i < l; i++) {\n        key = keys[i];\n        ret[key] = iteratee(val[key], key, i);\n      }\n      return ret\n    } else if (typeof val === 'number') {\n      ret = new Array(val);\n      for (i = 0, l = val; i < l; i++) {\n        // 第一个参数暂时仍和小程序一致\n        ret[i] = iteratee(i, i);\n      }\n      return ret\n    }\n    return []\n  };\n\n}\n\n/*  */\n\nvar LIFECYCLE_HOOKS$1 = [\n    //App\n    'onLaunch',\n    'onShow',\n    'onHide',\n    'onUniNViewMessage',\n    'onPageNotFound',\n    'onThemeChange',\n    'onError',\n    'onUnhandledRejection',\n    //Page\n    'onInit',\n    'onLoad',\n    // 'onShow',\n    'onReady',\n    // 'onHide',\n    'onUnload',\n    'onPullDownRefresh',\n    'onReachBottom',\n    'onTabItemTap',\n    'onAddToFavorites',\n    'onShareTimeline',\n    'onShareAppMessage',\n    'onResize',\n    'onPageScroll',\n    'onNavigationBarButtonTap',\n    'onBackPress',\n    'onNavigationBarSearchInputChanged',\n    'onNavigationBarSearchInputConfirmed',\n    'onNavigationBarSearchInputClicked',\n    //Component\n    // 'onReady', // 兼容旧版本，应该移除该事件\n    'onPageShow',\n    'onPageHide',\n    'onPageResize'\n];\nfunction lifecycleMixin$1(Vue) {\n\n    //fixed vue-class-component\n    var oldExtend = Vue.extend;\n    Vue.extend = function(extendOptions) {\n        extendOptions = extendOptions || {};\n\n        var methods = extendOptions.methods;\n        if (methods) {\n            Object.keys(methods).forEach(function (methodName) {\n                if (LIFECYCLE_HOOKS$1.indexOf(methodName)!==-1) {\n                    extendOptions[methodName] = methods[methodName];\n                    delete methods[methodName];\n                }\n            });\n        }\n\n        return oldExtend.call(this, extendOptions)\n    };\n\n    var strategies = Vue.config.optionMergeStrategies;\n    var mergeHook = strategies.created;\n    LIFECYCLE_HOOKS$1.forEach(function (hook) {\n        strategies[hook] = mergeHook;\n    });\n\n    Vue.prototype.__lifecycle_hooks__ = LIFECYCLE_HOOKS$1;\n}\n\n/*  */\n\n// install platform patch function\nVue.prototype.__patch__ = patch;\n\n// public mount method\nVue.prototype.$mount = function(\n    el ,\n    hydrating \n) {\n    return mountComponent$1(this, el, hydrating)\n};\n\nlifecycleMixin$1(Vue);\ninternalMixin(Vue);\n\n/*  */\n\nexport default Vue;\n", "const isArray = Array.isArray;\r\nconst isObject = (val) => val !== null && typeof val === 'object';\r\nconst defaultDelimiters = ['{', '}'];\r\nclass BaseFormatter {\r\n    constructor() {\r\n        this._caches = Object.create(null);\r\n    }\r\n    interpolate(message, values, delimiters = defaultDelimiters) {\r\n        if (!values) {\r\n            return [message];\r\n        }\r\n        let tokens = this._caches[message];\r\n        if (!tokens) {\r\n            tokens = parse(message, delimiters);\r\n            this._caches[message] = tokens;\r\n        }\r\n        return compile(tokens, values);\r\n    }\r\n}\r\nconst RE_TOKEN_LIST_VALUE = /^(?:\\d)+/;\r\nconst RE_TOKEN_NAMED_VALUE = /^(?:\\w)+/;\r\nfunction parse(format, [startDelimiter, endDelimiter]) {\r\n    const tokens = [];\r\n    let position = 0;\r\n    let text = '';\r\n    while (position < format.length) {\r\n        let char = format[position++];\r\n        if (char === startDelimiter) {\r\n            if (text) {\r\n                tokens.push({ type: 'text', value: text });\r\n            }\r\n            text = '';\r\n            let sub = '';\r\n            char = format[position++];\r\n            while (char !== undefined && char !== endDelimiter) {\r\n                sub += char;\r\n                char = format[position++];\r\n            }\r\n            const isClosed = char === endDelimiter;\r\n            const type = RE_TOKEN_LIST_VALUE.test(sub)\r\n                ? 'list'\r\n                : isClosed && RE_TOKEN_NAMED_VALUE.test(sub)\r\n                    ? 'named'\r\n                    : 'unknown';\r\n            tokens.push({ value: sub, type });\r\n        }\r\n        //  else if (char === '%') {\r\n        //   // when found rails i18n syntax, skip text capture\r\n        //   if (format[position] !== '{') {\r\n        //     text += char\r\n        //   }\r\n        // }\r\n        else {\r\n            text += char;\r\n        }\r\n    }\r\n    text && tokens.push({ type: 'text', value: text });\r\n    return tokens;\r\n}\r\nfunction compile(tokens, values) {\r\n    const compiled = [];\r\n    let index = 0;\r\n    const mode = isArray(values)\r\n        ? 'list'\r\n        : isObject(values)\r\n            ? 'named'\r\n            : 'unknown';\r\n    if (mode === 'unknown') {\r\n        return compiled;\r\n    }\r\n    while (index < tokens.length) {\r\n        const token = tokens[index];\r\n        switch (token.type) {\r\n            case 'text':\r\n                compiled.push(token.value);\r\n                break;\r\n            case 'list':\r\n                compiled.push(values[parseInt(token.value, 10)]);\r\n                break;\r\n            case 'named':\r\n                if (mode === 'named') {\r\n                    compiled.push(values[token.value]);\r\n                }\r\n                else {\r\n                    if (process.env.NODE_ENV !== 'production') {\r\n                        console.warn(`Type of token '${token.type}' and format of value '${mode}' don't match!`);\r\n                    }\r\n                }\r\n                break;\r\n            case 'unknown':\r\n                if (process.env.NODE_ENV !== 'production') {\r\n                    console.warn(`Detect 'unknown' type of token!`);\r\n                }\r\n                break;\r\n        }\r\n        index++;\r\n    }\r\n    return compiled;\r\n}\r\n\r\nconst LOCALE_ZH_HANS = 'zh-Hans';\r\nconst LOCALE_ZH_HANT = 'zh-Hant';\r\nconst LOCALE_EN = 'en';\r\nconst LOCALE_FR = 'fr';\r\nconst LOCALE_ES = 'es';\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\nconst hasOwn = (val, key) => hasOwnProperty.call(val, key);\r\nconst defaultFormatter = new BaseFormatter();\r\nfunction include(str, parts) {\r\n    return !!parts.find((part) => str.indexOf(part) !== -1);\r\n}\r\nfunction startsWith(str, parts) {\r\n    return parts.find((part) => str.indexOf(part) === 0);\r\n}\r\nfunction normalizeLocale(locale, messages) {\r\n    if (!locale) {\r\n        return;\r\n    }\r\n    locale = locale.trim().replace(/_/g, '-');\r\n    if (messages && messages[locale]) {\r\n        return locale;\r\n    }\r\n    locale = locale.toLowerCase();\r\n    if (locale.indexOf('zh') === 0) {\r\n        if (locale.indexOf('-hans') > -1) {\r\n            return LOCALE_ZH_HANS;\r\n        }\r\n        if (locale.indexOf('-hant') > -1) {\r\n            return LOCALE_ZH_HANT;\r\n        }\r\n        if (include(locale, ['-tw', '-hk', '-mo', '-cht'])) {\r\n            return LOCALE_ZH_HANT;\r\n        }\r\n        return LOCALE_ZH_HANS;\r\n    }\r\n    const lang = startsWith(locale, [LOCALE_EN, LOCALE_FR, LOCALE_ES]);\r\n    if (lang) {\r\n        return lang;\r\n    }\r\n}\r\nclass I18n {\r\n    constructor({ locale, fallbackLocale, messages, watcher, formater, }) {\r\n        this.locale = LOCALE_EN;\r\n        this.fallbackLocale = LOCALE_EN;\r\n        this.message = {};\r\n        this.messages = {};\r\n        this.watchers = [];\r\n        if (fallbackLocale) {\r\n            this.fallbackLocale = fallbackLocale;\r\n        }\r\n        this.formater = formater || defaultFormatter;\r\n        this.messages = messages || {};\r\n        this.setLocale(locale || LOCALE_EN);\r\n        if (watcher) {\r\n            this.watchLocale(watcher);\r\n        }\r\n    }\r\n    setLocale(locale) {\r\n        const oldLocale = this.locale;\r\n        this.locale = normalizeLocale(locale, this.messages) || this.fallbackLocale;\r\n        if (!this.messages[this.locale]) {\r\n            // 可能初始化时不存在\r\n            this.messages[this.locale] = {};\r\n        }\r\n        this.message = this.messages[this.locale];\r\n        // 仅发生变化时，通知\r\n        if (oldLocale !== this.locale) {\r\n            this.watchers.forEach((watcher) => {\r\n                watcher(this.locale, oldLocale);\r\n            });\r\n        }\r\n    }\r\n    getLocale() {\r\n        return this.locale;\r\n    }\r\n    watchLocale(fn) {\r\n        const index = this.watchers.push(fn) - 1;\r\n        return () => {\r\n            this.watchers.splice(index, 1);\r\n        };\r\n    }\r\n    add(locale, message, override = true) {\r\n        const curMessages = this.messages[locale];\r\n        if (curMessages) {\r\n            if (override) {\r\n                Object.assign(curMessages, message);\r\n            }\r\n            else {\r\n                Object.keys(message).forEach((key) => {\r\n                    if (!hasOwn(curMessages, key)) {\r\n                        curMessages[key] = message[key];\r\n                    }\r\n                });\r\n            }\r\n        }\r\n        else {\r\n            this.messages[locale] = message;\r\n        }\r\n    }\r\n    f(message, values, delimiters) {\r\n        return this.formater.interpolate(message, values, delimiters).join('');\r\n    }\r\n    t(key, locale, values) {\r\n        let message = this.message;\r\n        if (typeof locale === 'string') {\r\n            locale = normalizeLocale(locale, this.messages);\r\n            locale && (message = this.messages[locale]);\r\n        }\r\n        else {\r\n            values = locale;\r\n        }\r\n        if (!hasOwn(message, key)) {\r\n            console.warn(`Cannot translate the value of keypath ${key}. Use the value of keypath as default.`);\r\n            return key;\r\n        }\r\n        return this.formater.interpolate(message[key], values).join('');\r\n    }\r\n}\r\n\r\nfunction watchAppLocale(appVm, i18n) {\r\n    // 需要保证 watch 的触发在组件渲染之前\r\n    if (appVm.$watchLocale) {\r\n        // vue2\r\n        appVm.$watchLocale((newLocale) => {\r\n            i18n.setLocale(newLocale);\r\n        });\r\n    }\r\n    else {\r\n        appVm.$watch(() => appVm.$locale, (newLocale) => {\r\n            i18n.setLocale(newLocale);\r\n        });\r\n    }\r\n}\r\nfunction getDefaultLocale() {\r\n    if (typeof uni !== 'undefined' && uni.getLocale) {\r\n        return uni.getLocale();\r\n    }\r\n    // 小程序平台，uni 和 uni-i18n 互相引用，导致访问不到 uni，故在 global 上挂了 getLocale\r\n    if (typeof global !== 'undefined' && global.getLocale) {\r\n        return global.getLocale();\r\n    }\r\n    return LOCALE_EN;\r\n}\r\nfunction initVueI18n(locale, messages = {}, fallbackLocale, watcher) {\r\n    // 兼容旧版本入参\r\n    if (typeof locale !== 'string') {\r\n        [locale, messages] = [\r\n            messages,\r\n            locale,\r\n        ];\r\n    }\r\n    if (typeof locale !== 'string') {\r\n        // 因为小程序平台，uni-i18n 和 uni 互相引用，导致此时访问 uni 时，为 undefined\r\n        locale = getDefaultLocale();\r\n    }\r\n    if (typeof fallbackLocale !== 'string') {\r\n        fallbackLocale =\r\n            (typeof __uniConfig !== 'undefined' && __uniConfig.fallbackLocale) ||\r\n                LOCALE_EN;\r\n    }\r\n    const i18n = new I18n({\r\n        locale,\r\n        fallbackLocale,\r\n        messages,\r\n        watcher,\r\n    });\r\n    let t = (key, values) => {\r\n        if (typeof getApp !== 'function') {\r\n            // app view\r\n            /* eslint-disable no-func-assign */\r\n            t = function (key, values) {\r\n                return i18n.t(key, values);\r\n            };\r\n        }\r\n        else {\r\n            let isWatchedAppLocale = false;\r\n            t = function (key, values) {\r\n                const appVm = getApp().$vm;\r\n                // 可能$vm还不存在，比如在支付宝小程序中，组件定义较早，在props的default里使用了t()函数（如uni-goods-nav），此时app还未初始化\r\n                // options: {\r\n                // \ttype: Array,\r\n                // \tdefault () {\r\n                // \t\treturn [{\r\n                // \t\t\ticon: 'shop',\r\n                // \t\t\ttext: t(\"uni-goods-nav.options.shop\"),\r\n                // \t\t}, {\r\n                // \t\t\ticon: 'cart',\r\n                // \t\t\ttext: t(\"uni-goods-nav.options.cart\")\r\n                // \t\t}]\r\n                // \t}\r\n                // },\r\n                if (appVm) {\r\n                    // 触发响应式\r\n                    appVm.$locale;\r\n                    if (!isWatchedAppLocale) {\r\n                        isWatchedAppLocale = true;\r\n                        watchAppLocale(appVm, i18n);\r\n                    }\r\n                }\r\n                return i18n.t(key, values);\r\n            };\r\n        }\r\n        return t(key, values);\r\n    };\r\n    return {\r\n        i18n,\r\n        f(message, values, delimiters) {\r\n            return i18n.f(message, values, delimiters);\r\n        },\r\n        t(key, values) {\r\n            return t(key, values);\r\n        },\r\n        add(locale, message, override = true) {\r\n            return i18n.add(locale, message, override);\r\n        },\r\n        watch(fn) {\r\n            return i18n.watchLocale(fn);\r\n        },\r\n        getLocale() {\r\n            return i18n.getLocale();\r\n        },\r\n        setLocale(newLocale) {\r\n            return i18n.setLocale(newLocale);\r\n        },\r\n    };\r\n}\r\n\r\nconst isString = (val) => typeof val === 'string';\r\nlet formater;\r\nfunction hasI18nJson(jsonObj, delimiters) {\r\n    if (!formater) {\r\n        formater = new BaseFormatter();\r\n    }\r\n    return walkJsonObj(jsonObj, (jsonObj, key) => {\r\n        const value = jsonObj[key];\r\n        if (isString(value)) {\r\n            if (isI18nStr(value, delimiters)) {\r\n                return true;\r\n            }\r\n        }\r\n        else {\r\n            return hasI18nJson(value, delimiters);\r\n        }\r\n    });\r\n}\r\nfunction parseI18nJson(jsonObj, values, delimiters) {\r\n    if (!formater) {\r\n        formater = new BaseFormatter();\r\n    }\r\n    walkJsonObj(jsonObj, (jsonObj, key) => {\r\n        const value = jsonObj[key];\r\n        if (isString(value)) {\r\n            if (isI18nStr(value, delimiters)) {\r\n                jsonObj[key] = compileStr(value, values, delimiters);\r\n            }\r\n        }\r\n        else {\r\n            parseI18nJson(value, values, delimiters);\r\n        }\r\n    });\r\n    return jsonObj;\r\n}\r\nfunction compileI18nJsonStr(jsonStr, { locale, locales, delimiters, }) {\r\n    if (!isI18nStr(jsonStr, delimiters)) {\r\n        return jsonStr;\r\n    }\r\n    if (!formater) {\r\n        formater = new BaseFormatter();\r\n    }\r\n    const localeValues = [];\r\n    Object.keys(locales).forEach((name) => {\r\n        if (name !== locale) {\r\n            localeValues.push({\r\n                locale: name,\r\n                values: locales[name],\r\n            });\r\n        }\r\n    });\r\n    localeValues.unshift({ locale, values: locales[locale] });\r\n    try {\r\n        return JSON.stringify(compileJsonObj(JSON.parse(jsonStr), localeValues, delimiters), null, 2);\r\n    }\r\n    catch (e) { }\r\n    return jsonStr;\r\n}\r\nfunction isI18nStr(value, delimiters) {\r\n    return value.indexOf(delimiters[0]) > -1;\r\n}\r\nfunction compileStr(value, values, delimiters) {\r\n    return formater.interpolate(value, values, delimiters).join('');\r\n}\r\nfunction compileValue(jsonObj, key, localeValues, delimiters) {\r\n    const value = jsonObj[key];\r\n    if (isString(value)) {\r\n        // 存在国际化\r\n        if (isI18nStr(value, delimiters)) {\r\n            jsonObj[key] = compileStr(value, localeValues[0].values, delimiters);\r\n            if (localeValues.length > 1) {\r\n                // 格式化国际化语言\r\n                const valueLocales = (jsonObj[key + 'Locales'] = {});\r\n                localeValues.forEach((localValue) => {\r\n                    valueLocales[localValue.locale] = compileStr(value, localValue.values, delimiters);\r\n                });\r\n            }\r\n        }\r\n    }\r\n    else {\r\n        compileJsonObj(value, localeValues, delimiters);\r\n    }\r\n}\r\nfunction compileJsonObj(jsonObj, localeValues, delimiters) {\r\n    walkJsonObj(jsonObj, (jsonObj, key) => {\r\n        compileValue(jsonObj, key, localeValues, delimiters);\r\n    });\r\n    return jsonObj;\r\n}\r\nfunction walkJsonObj(jsonObj, walk) {\r\n    if (isArray(jsonObj)) {\r\n        for (let i = 0; i < jsonObj.length; i++) {\r\n            if (walk(jsonObj, i)) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    else if (isObject(jsonObj)) {\r\n        for (const key in jsonObj) {\r\n            if (walk(jsonObj, key)) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n    return false;\r\n}\r\n\r\nfunction resolveLocale(locales) {\r\n    return (locale) => {\r\n        if (!locale) {\r\n            return locale;\r\n        }\r\n        locale = normalizeLocale(locale) || locale;\r\n        return resolveLocaleChain(locale).find((locale) => locales.indexOf(locale) > -1);\r\n    };\r\n}\r\nfunction resolveLocaleChain(locale) {\r\n    const chain = [];\r\n    const tokens = locale.split('-');\r\n    while (tokens.length) {\r\n        chain.push(tokens.join('-'));\r\n        tokens.pop();\r\n    }\r\n    return chain;\r\n}\r\n\r\nexport { BaseFormatter as Formatter, I18n, LOCALE_EN, LOCALE_ES, LOCALE_FR, LOCALE_ZH_HANS, LOCALE_ZH_HANT, compileI18nJsonStr, hasI18nJson, initVueI18n, isI18nStr, isString, normalizeLocale, parseI18nJson, resolveLocale };\r\n"], "sourceRoot": ""}