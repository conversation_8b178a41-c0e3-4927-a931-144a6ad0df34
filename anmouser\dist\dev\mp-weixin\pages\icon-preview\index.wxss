@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.icon-preview-page.data-v-8f0b93a0 {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}
.header.data-v-8f0b93a0 {
  text-align: center;
  margin-bottom: 40rpx;
}
.header .title.data-v-8f0b93a0 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.header .subtitle.data-v-8f0b93a0 {
  display: block;
  font-size: 24rpx;
  color: #666;
}
.icon-grid.data-v-8f0b93a0 {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 20rpx;
}
.icon-item.data-v-8f0b93a0 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
}
.icon-item.data-v-8f0b93a0:hover {
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}
.icon-item.data-v-8f0b93a0:active {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.icon-container.data-v-8f0b93a0 {
  margin-bottom: 16rpx;
  height: 64rpx;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.icon-name.data-v-8f0b93a0 {
  display: block;
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.icon-desc.data-v-8f0b93a0 {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.icon-category.data-v-8f0b93a0 {
  display: block;
  font-size: 18rpx;
  color: #999;
  background: #f0f0f0;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}
.toast.data-v-8f0b93a0 {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  z-index: 9999;
}
