.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 10rem;
  height: 21.654rem;
  overflow: hidden;
  .block_1 {
    width: 10rem;
    height: 12.72rem;
    .box_1 {
      width: 10rem;
      height: 3.787rem;
      .section_1 {
        background-color: rgba(255, 255, 255, 1);
        width: 10rem;
        height: 0.854rem;
        .text_1 {
          width: 0.854rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.32rem;
          font-family: Inter-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 4rem;
          margin: 0.187rem 0 0 0.427rem;
        }
        .thumbnail_1 {
          width: 0.48rem;
          height: 0.48rem;
          margin: 0.187rem 0 0 6.694rem;
        }
        .thumbnail_2 {
          width: 0.48rem;
          height: 0.48rem;
          margin: 0.187rem 0 0 0.08rem;
        }
        .thumbnail_3 {
          width: 0.507rem;
          height: 0.507rem;
          margin: 0.187rem 0.4rem 0 0.08rem;
        }
      }
      .section_2 {
        background-color: rgba(255, 255, 255, 1);
        width: 10rem;
        height: 1.44rem;
        .thumbnail_4 {
          width: 0.24rem;
          height: 0.454rem;
          margin: 0.507rem 0 0 0.48rem;
        }
        .text_2 {
          width: 1.707rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.426rem;
          font-family: Inter-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 0.587rem;
          margin: 0.427rem 0 0 0.294rem;
        }
        .image_1 {
          width: 2.32rem;
          height: 0.854rem;
          margin: 0.294rem 0.16rem 0 4.8rem;
        }
      }
      .text-wrapper_1 {
        background-color: rgba(255, 255, 255, 1);
        width: 10rem;
        height: 1.174rem;
        margin: 0.027rem 0 0.294rem 0;
        .text_3 {
          width: 1.494rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 0.373rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.4rem 0 0 1.76rem;
        }
        .text_4 {
          width: 1.867rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.373rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.4rem 1.547rem 0 3.334rem;
        }
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      width: 9.36rem;
      height: 8.96rem;
      margin: 3.76rem 0.32rem 0 -9.68rem;
      .box_3 {
        width: 8.72rem;
        height: 0.4rem;
        margin: 0.48rem 0 0 0.32rem;
        .text-wrapper_2 {
          width: 2.587rem;
          height: 0.4rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          .text_5 {
            width: 2.587rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.346rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
          }
          .text_6 {
            width: 2.587rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.4rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
          }
        }
        .text_7 {
          width: 1.04rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
      }
      .text-wrapper_3 {
        width: 6.107rem;
        height: 0.4rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 0.427rem 0 0 0.32rem;
        .text_8 {
          width: 6.107rem;
          height: 0.4rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text_9 {
          width: 6.107rem;
          height: 0.4rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.4rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
      }
      .text_10 {
        width: 3.52rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 0.427rem 0 0 0.32rem;
      }
      .text_11 {
        width: 3.52rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 0.427rem 0 0 0.32rem;
      }
      .text_12 {
        width: 5.547rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 0.427rem 0 0 0.32rem;
      }
      .box_4 {
        background-color: rgba(246, 246, 246, 1);
        border-radius: 10px;
        height: 2.374rem;
        width: 8.747rem;
        margin: 0.72rem 0 0 0.32rem;
        .box_5 {
          width: 8.214rem;
          height: 0.96rem;
          margin: 0.347rem 0 0 0.32rem;
          .text-group_1 {
            width: 2.8rem;
            height: 0.96rem;
            .text_13 {
              width: 2.8rem;
              height: 0.347rem;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 0.346rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_14 {
              width: 1.387rem;
              height: 0.347rem;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 0.346rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin-top: 0.267rem;
            }
          }
          .text-group_2 {
            width: 1.067rem;
            height: 0.96rem;
            .text_15 {
              width: 0.987rem;
              height: 0.347rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.346rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin-left: 0.08rem;
            }
            .text_16 {
              width: 1.067rem;
              height: 0.347rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.346rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin-top: 0.267rem;
            }
          }
        }
        .box_6 {
          width: 8.214rem;
          height: 0.347rem;
          margin: 0.294rem 0 0.427rem 0.32rem;
          .text_17 {
            width: 1.387rem;
            height: 0.347rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.346rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
          }
          .text-wrapper_4 {
            width: 3.44rem;
            height: 0.347rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            .text_18 {
              width: 3.44rem;
              height: 0.347rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.346rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_19 {
              width: 3.44rem;
              height: 0.347rem;
              overflow-wrap: break-word;
              color: rgba(24, 200, 99, 1);
              font-size: 0.346rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
          }
        }
      }
      .text-wrapper_5 {
        width: 3.147rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 0.374rem 0 0 0.32rem;
        .text_20 {
          width: 3.147rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text_21 {
          width: 3.147rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
      }
      .text-wrapper_6 {
        width: 4.054rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 0.32rem 0 0.454rem 0.32rem;
        .text_22 {
          width: 4.054rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text_23 {
          width: 4.054rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
      }
    }
  }
}
