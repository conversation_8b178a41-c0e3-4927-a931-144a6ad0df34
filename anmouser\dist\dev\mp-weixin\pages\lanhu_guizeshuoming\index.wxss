@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}
button:active {
  opacity: 0.6;
}
.flex-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
}
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
          flex-direction: row;
}
.justify-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
}
.justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.justify-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
}
.justify-evenly {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: space-evenly;
  -webkit-justify-content: space-evenly;
          justify-content: space-evenly;
}
.justify-around {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-around;
          justify-content: space-around;
}
.justify-between {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between;
}
.align-start {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
          align-items: flex-start;
}
.align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
}
.align-end {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
}
.page {
  background-color: rgb(245, 245, 245);
  position: relative;
  width: 750rpx;
  height: 1772rpx;
  overflow: hidden;
}
.page .group_1 {
  background-image: -webkit-linear-gradient(top, rgb(62, 200, 174) 41.346154%, rgba(255, 255, 255, 0) 100%);
  background-image: linear-gradient(180deg, rgb(62, 200, 174) 41.346154%, rgba(255, 255, 255, 0) 100%);
  position: relative;
  width: 750rpx;
  height: 642rpx;
}
.page .group_1 .box_1 {
  width: 688rpx;
  height: 38rpx;
  margin: 14rpx 0 0 32rpx;
}
.page .group_1 .box_1 .text_1 {
  width: 64rpx;
  height: 36rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 24rpx;
  font-family: Inter-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 300rpx;
}
.page .group_1 .box_1 .thumbnail_1 {
  width: 36rpx;
  height: 36rpx;
  margin-left: 502rpx;
}
.page .group_1 .box_1 .thumbnail_2 {
  width: 36rpx;
  height: 36rpx;
  margin-left: 6rpx;
}
.page .group_1 .box_1 .thumbnail_3 {
  width: 38rpx;
  height: 38rpx;
  margin-left: 6rpx;
}
.page .group_1 .box_2 {
  width: 702rpx;
  height: 64rpx;
  margin: 34rpx 0 0 36rpx;
}
.page .group_1 .box_2 .thumbnail_4 {
  width: 18rpx;
  height: 34rpx;
  margin-top: 16rpx;
}
.page .group_1 .box_2 .text_2 {
  width: 192rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-family: Inter-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 10rpx 0 0 22rpx;
}
.page .group_1 .box_2 .image_1 {
  width: 174rpx;
  height: 64rpx;
  margin-left: 296rpx;
}
.page .group_1 .box_3 {
  width: 476rpx;
  height: 100rpx;
  margin: 56rpx 0 0 24rpx;
}
.page .group_1 .box_3 .image-wrapper_1 {
  background-color: rgb(235, 243, 237);
  border-radius: 50%;
  height: 100rpx;
  border: 6px solid rgb(255, 255, 255);
  width: 100rpx;
}
.page .group_1 .box_3 .image-wrapper_1 .label_1 {
  width: 54rpx;
  height: 52rpx;
  margin: 24rpx 0 0 26rpx;
}
.page .group_1 .box_3 .text_3 {
  width: 120rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 22rpx 0 0 32rpx;
}
.page .group_1 .box_3 .text-wrapper_1 {
  background-image: -webkit-linear-gradient(left, rgb(227, 231, 239) 0, rgb(204, 212, 223) 100%);
  background-image: linear-gradient(90deg, rgb(227, 231, 239) 0, rgb(204, 212, 223) 100%);
  border-radius: 50px;
  height: 46rpx;
  width: 212rpx;
  margin: 28rpx 0 0 12rpx;
}
.page .group_1 .box_3 .text-wrapper_1 .text_4 {
  width: 168rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(110, 110, 110);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 14rpx 0 0 22rpx;
}
.page .group_1 .box_4 {
  background-color: rgb(233, 255, 251);
  border-radius: 10px 10px 0px 0px;
  height: 202rpx;
  width: 674rpx;
  margin: 38rpx 0 96rpx 38rpx;
}
.page .group_1 .box_4 .text-wrapper_2 {
  width: 470rpx;
  height: 22rpx;
  margin: 44rpx 0 0 60rpx;
}
.page .group_1 .box_4 .text-wrapper_2 .text_5 {
  width: 122rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(102, 102, 102, 0.8);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_1 .box_4 .text-wrapper_2 .text_6 {
  width: 122rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(102, 102, 102, 0.8);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_1 .box_4 .text-wrapper_3 {
  width: 522rpx;
  height: 38rpx;
  margin: 22rpx 0 76rpx 60rpx;
}
.page .group_1 .box_4 .text-wrapper_3 .text_7 {
  width: 174rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 44rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_1 .box_4 .text-wrapper_3 .text_8 {
  width: 174rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 44rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_1 .text-wrapper_4 {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 50px 0px 50px 0px;
  height: 56rpx;
  width: 118rpx;
  position: absolute;
  left: 634rpx;
  top: 228rpx;
}
.page .group_1 .text-wrapper_4 .text_9 {
  width: 60rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 30rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 16rpx 0 0 30rpx;
}
.page .group_2 {
  position: absolute;
  left: 0;
  top: 642rpx;
  width: 750rpx;
  height: 1132rpx;
}
.page .group_2 .section_1 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 314rpx;
  width: 700rpx;
  position: relative;
  margin: 246rpx 0 0 24rpx;
}
.page .group_2 .section_1 .text-wrapper_5 {
  width: 112rpx;
  height: 44rpx;
  margin: 32rpx 0 0 36rpx;
}
.page .group_2 .section_1 .text-wrapper_5 .text_10 {
  width: 112rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_2 .section_1 .block_1 {
  width: 652rpx;
  height: 186rpx;
  margin: 16rpx 0 36rpx 24rpx;
}
.page .group_2 .section_1 .block_1 .box_5 {
  background-color: rgb(65, 196, 147);
  border-radius: 10px;
  position: relative;
  width: 206rpx;
  height: 186rpx;
}
.page .group_2 .section_1 .block_1 .box_5 .image-wrapper_2 {
  background-color: rgb(65, 196, 147);
  border-radius: 10px;
  height: 124rpx;
  width: 138rpx;
  margin: 62rpx 0 0 68rpx;
}
.page .group_2 .section_1 .block_1 .box_5 .image-wrapper_2 .image_2 {
  width: 110rpx;
  height: 104rpx;
  margin: 20rpx 0 0 28rpx;
}
.page .group_2 .section_1 .block_1 .box_5 .text_11 {
  position: absolute;
  left: 20rpx;
  top: 30rpx;
  width: 132rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_2 .section_1 .block_1 .box_6 {
  background-color: rgb(111, 140, 253);
  border-radius: 10px;
  width: 200rpx;
  height: 186rpx;
  margin-left: 20rpx;
}
.page .group_2 .section_1 .block_1 .box_6 .text-wrapper_6 {
  width: 138rpx;
  height: 82rpx;
  margin: 30rpx 0 0 20rpx;
}
.page .group_2 .section_1 .block_1 .box_6 .text-wrapper_6 .text_12 {
  width: 132rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_2 .section_1 .block_1 .box_6 .text-wrapper_6 .text_13 {
  width: 138rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.8);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: justified;
  line-height: 44rpx;
  margin-top: -6rpx;
}
.page .group_2 .section_1 .block_1 .box_6 .image-wrapper_3 {
  background-color: rgb(111, 140, 253);
  border-radius: 10px;
  height: 118rpx;
  width: 132rpx;
  margin: 68rpx 0 0 -90rpx;
}
.page .group_2 .section_1 .block_1 .box_6 .image-wrapper_3 .image_3 {
  width: 110rpx;
  height: 114rpx;
  margin: 4rpx 0 0 22rpx;
}
.page .group_2 .section_1 .block_1 .box_7 {
  background-color: rgb(243, 161, 66);
  border-radius: 10px;
  width: 204rpx;
  height: 186rpx;
  margin-left: 22rpx;
}
.page .group_2 .section_1 .block_1 .box_7 .text_14 {
  width: 132rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(255, 255, 255);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
  margin: 30rpx 0 0 18rpx;
}
.page .group_2 .section_1 .block_1 .box_7 .text_15 {
  width: 138rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.8);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: justified;
  line-height: 44rpx;
  margin: -6rpx 0 0 18rpx;
}
.page .group_2 .section_1 .block_1 .box_7 .image-wrapper_4 {
  background-color: rgb(243, 161, 66);
  border-radius: 10px;
  height: 110rpx;
  width: 120rpx;
  margin: -36rpx 0 0 84rpx;
}
.page .group_2 .section_1 .block_1 .box_7 .image-wrapper_4 .image_4 {
  width: 110rpx;
  height: 110rpx;
  margin-left: 10rpx;
}
.page .group_2 .section_1 .text_16 {
  position: absolute;
  left: 44rpx;
  top: 160rpx;
  width: 200rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 0.8);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: justified;
  line-height: 44rpx;
}
.page .group_2 .section_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 328rpx;
  width: 702rpx;
  margin: 20rpx 0 0 26rpx;
}
.page .group_2 .section_2 .text-wrapper_7 {
  width: 112rpx;
  height: 44rpx;
  margin: 30rpx 0 0 48rpx;
}
.page .group_2 .section_2 .text-wrapper_7 .text_17 {
  width: 112rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_2 .section_2 .section_3 {
  width: 546rpx;
  height: 68rpx;
  margin: 34rpx 0 0 60rpx;
}
.page .group_2 .section_2 .section_3 .text-group_1 {
  width: 144rpx;
  height: 68rpx;
}
.page .group_2 .section_2 .section_3 .text-group_1 .text_18 {
  width: 144rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_2 .section_2 .section_3 .text-group_1 .text_19 {
  width: 24rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 14rpx;
}
.page .group_2 .section_2 .section_3 .text-group_2 {
  width: 144rpx;
  height: 68rpx;
}
.page .group_2 .section_2 .section_3 .text-group_2 .text_20 {
  width: 144rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_2 .section_2 .section_3 .text-group_2 .text_21 {
  width: 24rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 14rpx;
}
.page .group_2 .section_2 .section_4 {
  width: 546rpx;
  height: 68rpx;
  margin: 46rpx 0 38rpx 60rpx;
}
.page .group_2 .section_2 .section_4 .text-group_3 {
  width: 144rpx;
  height: 68rpx;
}
.page .group_2 .section_2 .section_4 .text-group_3 .text_22 {
  width: 144rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_2 .section_2 .section_4 .text-group_3 .text_23 {
  width: 24rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 14rpx;
}
.page .group_2 .section_2 .section_4 .text-group_4 {
  width: 144rpx;
  height: 68rpx;
}
.page .group_2 .section_2 .section_4 .text-group_4 .text_24 {
  width: 144rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_2 .section_2 .section_4 .text-group_4 .text_25 {
  width: 24rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 14rpx;
}
.page .group_2 .section_5 {
  width: 702rpx;
  height: 144rpx;
  margin: 20rpx 0 60rpx 24rpx;
}
.page .group_2 .section_5 .block_2 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 144rpx;
  width: 338rpx;
  position: relative;
}
.page .group_2 .section_5 .block_2 .text-wrapper_8 {
  width: 200rpx;
  height: 44rpx;
  margin: 70rpx 0 0 124rpx;
}
.page .group_2 .section_5 .block_2 .text-wrapper_8 .text_26 {
  width: 200rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: justified;
  line-height: 44rpx;
}
.page .group_2 .section_5 .block_2 .box_8 {
  position: absolute;
  left: 28rpx;
  top: 32rpx;
  width: 296rpx;
  height: 82rpx;
}
.page .group_2 .section_5 .block_2 .box_8 .box_9 {
  background-image: -webkit-linear-gradient(top, rgb(207, 178, 123) 0, rgb(192, 162, 105) 100%);
  background-image: linear-gradient(180deg, rgb(207, 178, 123) 0, rgb(192, 162, 105) 100%);
  border-radius: 50%;
  height: 78rpx;
  width: 78rpx;
  position: relative;
}
.page .group_2 .section_5 .block_2 .box_8 .box_9 .box_10 {
  width: 54rpx;
  height: 56rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAcCAYAAAB/E6/TAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMISURBVHgBrVXdURtBDJYOkwxv+DFvTgWYVEAqSEqgg9BBIA2EVEA6yKSBABXEaQDsp8xkMsEBBxv7TopWq/25g/N4DAL59nb39H3SSlqAFYSZt0UPRS9Fib18Fz0Q7cFTiBjaE73iJJQ9nV6I9uEx4ti2GM/G+vjDPF8fzELFyWA2pirTUqbLb7COWMgajpD8VKTG9Vn6Z7WQJVFe7LXZK5Zg7QicQwzIooTyxDRvc+h3APFOm7HOEqBtNWQw8o+Zv+ABbYwY2GyvAUTJG2LHmuXPe4OBgYE5rwAZlkg7EJExDcbsqYA65lpo0bxrkWL5ip4De7LOE+ZayMI5eXBYz6OKR8pSDxs9oLcrvxoq8xANRAgUMGgzh20Lru0AzS9k1PXmA+MYNvSf2wIJsc3FLmJ3/JC91tAh4hiq6kNMYR86M8xoXnC29rkNZCXh6vYjl/+k8G+JFxNWLSekuriR92vRm/fwFKJgAWDh9NoDzFVPVrFx74x4Ou3BhrQS7EiVk2uU/ViIHGsopbfOw1hC90OmB1IW51KCA9zqDu8B6cGXkwN5fQfaEfzXmljM9a7AnNcSNOYhS3MB4jOg4siBSmWIBxWdymTPb0JfN7rfDj0VLvsugWxpz7H3QfCUG4RwBM+LXeT55EQM79cMcvCGPHCtz5k3iUyaT/syAuhy+xglY67Aha7WPjJWGmDkWiP1IWLbgzFkuYdhrJGHsXj0t9E7QvtvgLaRaHZ2zppu6onYyTIHakw8c45nlQz6eCBkXoblaCtvuupxJ8YzuM92JTghSkxTRoUDbzTXRkSsQRq5YSGbRrH7on2lsaUHLjqObTW2oBgvN6RA1sZ6+17C7O51Ac9gF5A+RYDwYbgC8usAAgFO4KFz5wDhm4KOBeQVdl8OI2ue/+pLcX2Rjb0Y8/xwmbGWSZifKzSz7gxw4xC3Xpznka8J3/1+I5v3hfnblHFWV5S9JwuWYVrAp+LGUQ7QChQBpz97UGzuAJd7YqpvnaOXZeVQ5sZQCHveGMBs9lVC1HpN/AePwfamUavxKQAAAABJRU5ErkJggg==) 100% no-repeat;
  background-size: 100% 100%;
  margin: 22rpx 0 0 14rpx;
}
.page .group_2 .section_5 .block_2 .box_8 .box_9 .box_11 {
  position: absolute;
  left: 0;
  top: 0;
  width: 78rpx;
  height: 78rpx;
}
.page .group_2 .section_5 .block_2 .box_8 .text-group_5 {
  width: 206rpx;
  height: 80rpx;
  margin-top: 2rpx;
}
.page .group_2 .section_5 .block_2 .box_8 .text-group_5 .text_27 {
  width: 132rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_2 .section_5 .block_2 .box_8 .text-group_5 .text_26 {
  width: 200rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: justified;
  line-height: 44rpx;
}
.page .group_2 .section_5 .block_3 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  height: 144rpx;
  width: 338rpx;
  position: relative;
}
.page .group_2 .section_5 .block_3 .text-wrapper_9 {
  width: 200rpx;
  height: 44rpx;
  margin: 70rpx 0 0 124rpx;
}
.page .group_2 .section_5 .block_3 .text-wrapper_9 .text_28 {
  width: 200rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: justified;
  line-height: 44rpx;
}
.page .group_2 .section_5 .block_3 .box_12 {
  position: absolute;
  left: 28rpx;
  top: 32rpx;
  width: 296rpx;
  height: 82rpx;
}
.page .group_2 .section_5 .block_3 .box_12 .block_4 {
  background-image: -webkit-linear-gradient(top, rgb(207, 178, 123) 0, rgb(192, 162, 105) 100%);
  background-image: linear-gradient(180deg, rgb(207, 178, 123) 0, rgb(192, 162, 105) 100%);
  border-radius: 50%;
  height: 78rpx;
  width: 78rpx;
  position: relative;
}
.page .group_2 .section_5 .block_3 .box_12 .block_4 .block_5 {
  width: 60rpx;
  height: 50rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAZCAYAAAAmNZ4aAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMrSURBVHgBjVbdbRNBEJ65M0iBB+JHnjAVEDpIB1ACHURUEKeCkApIB1EqcKgAV4DsJyQCip9ic76byez/7N1al5XWu7c/8803f2tg5m+cGh2Y8zPWw57rJAOlT6bOde4uQRqa0zKajuBamLP/DutwYF+dUUvM0FuLGFhNsIJhw8J8IEUheCXY7RvAyIWTAiznmKJGEwXABVDO2WACYcYCOFq5mY28QvZMuldBblIusNGj7BN44U4BOxom5BihYh3OsezZMxw1meRAil30JBUU0US4FxvBzH6JvEyMFomMLyBD87eC5qDY6W5YWFbeb/YsufXkDnVeAMnKmkO/SYCfS7hLKrSmS/jvXe8aM5IdXZf5f+J2J/PY5XvLsXdb+X4k3j+a+Xkfq2fq1iiH3micmZd9cDhLOFYIKWr9qRhoJCMim4QtJUMOTP5uBMJgL+875OTTzDPsh6C0jmBOOZ1aNfxiUJHjfRaE24gWNtavX0XTTXaOo/8hi4VCy4FJXQAfLJ6+EmbS5hrrV1digOuUJoPgU2lHI8AuKkNPDIhUmkhodnuXCc3+Ku2F/FX+QuZDjCdQbNrPvqGYlqsV1K+XOMG1XTqarrh5+Ag8eSMAJ3JhLqvHEYyDz8eCyzJWwClKN9C0twaoLwBfTpf24PZhDTWeO/v6wAqZUSBd9XEHPnPmPoa6XojwGRSaXa9gIUynMaBQRfqzfJxXHHR+sr6dCaPvJWCo4cbuuxLreNqx8y/SKLAj4MFC+mA0H/EGym0JKd0gKV+oHAcZG01dpKb3M0Q4QvDnjJt/l9H0RMvsHQ7VKtQAGmNMWmMKa5go0Iy393Oo6KesnwG2v7i5l78yfGrNGliHO4PXLbVCOmlTRcxQJr8kYRxqzFlKH5XTmblHa3W3hgp90qM3GUCsufGfhtfIlnJXmVNA2uZi2l03j8WqDzxIMG7+nAiLGzn8zr4wwQqIDjiLGcXExgAqP5uruJCfCzx6+2MUOMpp/36CVkzL9FmB5E9lsAJHa4TlO8B6XgIcBY4itr+lOLz4IJqcChspizCTcZbYGjNKmlV4J9hL2O1ucfp+Myb3CdSWD1FzMuRxAAAAAElFTkSuQmCC) 100% no-repeat;
  background-size: 100% 100%;
  margin: 28rpx 0 0 10rpx;
}
.page .group_2 .section_5 .block_3 .box_12 .block_4 .block_6 {
  position: absolute;
  left: 0;
  top: 0;
  width: 78rpx;
  height: 78rpx;
}
.page .group_2 .section_5 .block_3 .box_12 .text-group_6 {
  width: 206rpx;
  height: 80rpx;
  margin-top: 2rpx;
}
.page .group_2 .section_5 .block_3 .box_12 .text-group_6 .text_29 {
  width: 132rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.page .group_2 .section_5 .block_3 .box_12 .text-group_6 .text_28 {
  width: 200rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgb(153, 153, 153);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: justified;
  line-height: 44rpx;
}
.page .group_2 .section_6 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px;
  position: absolute;
  left: 24rpx;
  top: -136rpx;
  width: 702rpx;
  height: 362rpx;
}
.page .group_2 .section_6 .paragraph_1 {
  width: 136rpx;
  height: 56rpx;
  overflow-wrap: break-word;
  color: rgb(102, 102, 102);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  line-height: 36rpx;
  margin: 96rpx 0 0 66rpx;
}
.page .group_2 .section_6 .group_3 {
  background-color: rgb(62, 200, 174);
  border-radius: 100px;
  width: 598rpx;
  height: 88rpx;
  margin: 50rpx 0 72rpx 56rpx;
}
.page .group_4 {
  background-color: rgba(0, 0, 0, 0.3);
  position: absolute;
  left: 0;
  top: 140rpx;
  width: 752rpx;
  height: 1632rpx;
}
.page .group_4 .section_7 {
  background-color: rgb(255, 255, 255);
  border-radius: 10px 10px 0px 0px;
  width: 560rpx;
  height: 248rpx;
  margin: 502rpx 0 0 76rpx;
}
.page .group_4 .section_7 .text-group_7 {
  width: 480rpx;
  height: 114rpx;
  margin: 28rpx 0 0 40rpx;
}
.page .group_4 .section_7 .text-group_7 .text_30 {
  width: 480rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgb(34, 34, 34);
  font-size: 34rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.page .group_4 .section_7 .text-group_7 .text_31 {
  width: 480rpx;
  height: 64rpx;
  overflow-wrap: break-word;
  color: rgba(22, 24, 35, 0.75);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: NaN;
  text-align: center;
  margin-top: 12rpx;
}
.page .group_4 .section_8 {
  background-color: rgb(255, 255, 255);
  border-radius: 0px 0px 10px 10px;
  width: 560rpx;
  height: 76rpx;
  margin: 0 0 806rpx 76rpx;
}
.page .group_4 .section_8 .group_5 {
  background-color: rgba(22, 24, 35, 0.12);
  width: 560rpx;
  height: 2rpx;
}
.page .group_4 .section_8 .text_32 {
  width: 230rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgba(22, 24, 35, 0.75);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 30rpx;
  margin: 16rpx 0 0 -536rpx;
}
.page .group_4 .section_8 .group_6 {
  background-color: rgba(22, 24, 35, 0.12);
  width: 2rpx;
  height: 76rpx;
  margin-left: 26rpx;
}
.page .group_4 .section_8 .text_33 {
  width: 232rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgb(62, 200, 174);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 30rpx;
  margin: 18rpx 24rpx 0 22rpx;
}
